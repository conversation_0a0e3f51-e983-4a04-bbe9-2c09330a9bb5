# 通知模块 REST API 设计

## 1. API 设计原则

### 1.1 RESTful 设计规范

- 使用标准 HTTP 方法 (GET, POST, PUT, DELETE)
- 统一的响应格式
- 合理的状态码使用
- 支持分页、排序、过滤

### 1.2 JHipster API 规范

- 使用 OpenAPI 3.0 文档
- 统一的错误处理
- 支持国际化
- 安全认证和授权

## 2. 通知记录 API

### 2.1 NotificationRecordResource

```java
@RestController
@RequestMapping("/api/notification-records")
@Tag(name = "notification-record", description = "通知记录管理 API")
public class NotificationRecordResource {

  private final Logger log = LoggerFactory.getLogger(NotificationRecordResource.class);

  private static final String ENTITY_NAME = "notificationRecord";

  @Value("${jhipster.clientApp.name}")
  private String applicationName;

  private final NotificationRecordService notificationRecordService;
  private final NotificationRecordQueryService notificationRecordQueryService;

  /**
   * 创建通知记录
   */
  @PostMapping("")
  @Operation(summary = "创建通知记录", description = "创建新的通知记录")
  @ApiResponses(
    value = {
      @ApiResponse(responseCode = "201", description = "创建成功"),
      @ApiResponse(responseCode = "400", description = "请求参数错误"),
      @ApiResponse(responseCode = "500", description = "服务器内部错误"),
    }
  )
  public ResponseEntity<NotificationRecordDTO> createNotificationRecord(@Valid @RequestBody NotificationRecordDTO notificationRecordDTO)
    throws URISyntaxException {
    log.debug("REST request to save NotificationRecord : {}", notificationRecordDTO);

    if (notificationRecordDTO.getId() != null) {
      throw new BadRequestAlertException("A new notificationRecord cannot already have an ID", ENTITY_NAME, "idexists");
    }

    NotificationRecordDTO result = notificationRecordService.save(notificationRecordDTO);

    return ResponseEntity.created(new URI("/api/notification-records/" + result.getId()))
      .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, result.getId().toString()))
      .body(result);
  }

  /**
   * 更新通知记录
   */
  @PutMapping("/{id}")
  @Operation(summary = "更新通知记录", description = "更新指定ID的通知记录")
  public ResponseEntity<NotificationRecordDTO> updateNotificationRecord(
    @PathVariable(value = "id", required = false) final Long id,
    @Valid @RequestBody NotificationRecordDTO notificationRecordDTO
  ) throws URISyntaxException {
    log.debug("REST request to update NotificationRecord : {}, {}", id, notificationRecordDTO);

    if (notificationRecordDTO.getId() == null) {
      throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
    }

    if (!Objects.equals(id, notificationRecordDTO.getId())) {
      throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
    }

    if (!notificationRecordRepository.existsById(id)) {
      throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
    }

    NotificationRecordDTO result = notificationRecordService.update(notificationRecordDTO);

    return ResponseEntity.ok()
      .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, notificationRecordDTO.getId().toString()))
      .body(result);
  }

  /**
   * 部分更新通知记录
   */
  @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
  @Operation(summary = "部分更新通知记录", description = "部分更新指定ID的通知记录")
  public ResponseEntity<NotificationRecordDTO> partialUpdateNotificationRecord(
    @PathVariable(value = "id", required = false) final Long id,
    @NotNull @RequestBody NotificationRecordDTO notificationRecordDTO
  ) throws URISyntaxException {
    log.debug("REST request to partial update NotificationRecord partially : {}, {}", id, notificationRecordDTO);

    if (notificationRecordDTO.getId() == null) {
      throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
    }

    if (!Objects.equals(id, notificationRecordDTO.getId())) {
      throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
    }

    if (!notificationRecordRepository.existsById(id)) {
      throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
    }

    Optional<NotificationRecordDTO> result = notificationRecordService.partialUpdate(notificationRecordDTO);

    return ResponseUtil.wrapOrNotFound(
      result,
      HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, notificationRecordDTO.getId().toString())
    );
  }

  /**
   * 获取所有通知记录
   */
  @GetMapping("")
  @Operation(summary = "获取通知记录列表", description = "分页获取通知记录列表")
  public ResponseEntity<List<NotificationRecordDTO>> getAllNotificationRecords(
    NotificationRecordCriteria criteria,
    @ParameterObject Pageable pageable
  ) {
    log.debug("REST request to get NotificationRecords by criteria: {}", criteria);

    Page<NotificationRecordDTO> page = notificationRecordQueryService.findByCriteria(criteria, pageable);
    HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);

    return ResponseEntity.ok().headers(headers).body(page.getContent());
  }

  /**
   * 获取通知记录数量
   */
  @GetMapping("/count")
  @Operation(summary = "获取通知记录数量", description = "根据条件获取通知记录数量")
  public ResponseEntity<Long> countNotificationRecords(NotificationRecordCriteria criteria) {
    log.debug("REST request to count NotificationRecords by criteria: {}", criteria);
    return ResponseEntity.ok().body(notificationRecordQueryService.countByCriteria(criteria));
  }

  /**
   * 根据ID获取通知记录
   */
  @GetMapping("/{id}")
  @Operation(summary = "获取通知记录详情", description = "根据ID获取通知记录详情")
  public ResponseEntity<NotificationRecordDTO> getNotificationRecord(@PathVariable Long id) {
    log.debug("REST request to get NotificationRecord : {}", id);
    Optional<NotificationRecordDTO> notificationRecordDTO = notificationRecordService.findOne(id);
    return ResponseUtil.wrapOrNotFound(notificationRecordDTO);
  }

  /**
   * 删除通知记录
   */
  @DeleteMapping("/{id}")
  @Operation(summary = "删除通知记录", description = "根据ID删除通知记录")
  public ResponseEntity<Void> deleteNotificationRecord(@PathVariable Long id) {
    log.debug("REST request to delete NotificationRecord : {}", id);
    notificationRecordService.delete(id);
    return ResponseEntity.noContent()
      .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
      .build();
  }

  /**
   * 获取用户通知记录
   */
  @GetMapping("/user/{userId}")
  @Operation(summary = "获取用户通知记录", description = "获取指定用户的通知记录")
  public ResponseEntity<List<NotificationRecordDTO>> getUserNotificationRecords(
    @PathVariable Long userId,
    @ParameterObject Pageable pageable
  ) {
    log.debug("REST request to get NotificationRecords for user: {}", userId);

    Long tenantId = TenantContextUtil.getCurrentTenantId();
    Page<NotificationRecordDTO> page = notificationRecordService.findByUserIdAndTenantId(userId, tenantId, pageable);
    HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);

    return ResponseEntity.ok().headers(headers).body(page.getContent());
  }

  /**
   * 标记通知为已读
   */
  @PutMapping("/{id}/read")
  @Operation(summary = "标记通知为已读", description = "标记指定通知为已读状态")
  public ResponseEntity<Void> markAsRead(@PathVariable Long id) {
    log.debug("REST request to mark NotificationRecord as read: {}", id);
    notificationRecordService.markAsRead(id);
    return ResponseEntity.ok().build();
  }

  /**
   * 批量标记通知为已读
   */
  @PutMapping("/batch-read")
  @Operation(summary = "批量标记通知为已读", description = "批量标记多个通知为已读状态")
  public ResponseEntity<Void> batchMarkAsRead(@RequestBody List<Long> ids) {
    log.debug("REST request to batch mark NotificationRecords as read: {}", ids);
    notificationRecordService.batchMarkAsRead(ids);
    return ResponseEntity.ok().build();
  }
}

```

## 3. 通知中心 API

### 3.1 NotificationCenterResource

```java
@RestController
@RequestMapping("/api/notification-center")
@Tag(name = "notification-center", description = "通知中心 API")
public class NotificationCenterResource {

  private final Logger log = LoggerFactory.getLogger(NotificationCenterResource.class);

  private final NotificationCenterService notificationCenterService;

  /**
   * 发送单个通知
   */
  @PostMapping("/send")
  @Operation(summary = "发送通知", description = "发送单个通知")
  public ResponseEntity<NotificationRecordDTO> sendNotification(@Valid @RequestBody NotificationRequestDTO request) {
    log.debug("REST request to send notification: {}", request);

    NotificationRecordDTO result = notificationCenterService.sendNotification(request);

    return ResponseEntity.ok(result);
  }

  /**
   * 批量发送通知
   */
  @PostMapping("/batch-send")
  @Operation(summary = "批量发送通知", description = "批量发送通知给多个接收者")
  public ResponseEntity<BatchNotificationResponseDTO> sendBatchNotification(@Valid @RequestBody BatchNotificationRequestDTO request) {
    log.debug("REST request to send batch notification: {}", request);

    String batchId = notificationCenterService.sendBatchNotification(request);
    BatchNotificationResponseDTO response = new BatchNotificationResponseDTO();
    response.setBatchId(batchId);
    response.setStatus("PROCESSING");
    response.setMessage("批量通知已提交处理");

    return ResponseEntity.ok(response);
  }

  /**
   * 发送系统通知
   */
  @PostMapping("/system")
  @Operation(summary = "发送系统通知", description = "发送系统级通知")
  public ResponseEntity<NotificationRecordDTO> sendSystemNotification(@Valid @RequestBody SystemNotificationRequestDTO request) {
    log.debug("REST request to send system notification: {}", request);

    NotificationRecordDTO result = notificationCenterService.sendSystemNotification(request);

    return ResponseEntity.ok(result);
  }

  /**
   * 发送任务通知
   */
  @PostMapping("/task")
  @Operation(summary = "发送任务通知", description = "发送任务相关通知")
  public ResponseEntity<NotificationRecordDTO> sendTaskNotification(@Valid @RequestBody TaskNotificationRequestDTO request) {
    log.debug("REST request to send task notification: {}", request);

    NotificationRecordDTO result = notificationCenterService.sendTaskNotification(request);

    return ResponseEntity.ok(result);
  }

  /**
   * 发送用户通知
   */
  @PostMapping("/user")
  @Operation(summary = "发送用户通知", description = "发送用户相关通知")
  public ResponseEntity<NotificationRecordDTO> sendUserNotification(@Valid @RequestBody UserNotificationRequestDTO request) {
    log.debug("REST request to send user notification: {}", request);

    NotificationRecordDTO result = notificationCenterService.sendUserNotification(request);

    return ResponseEntity.ok(result);
  }

  /**
   * 取消计划通知
   */
  @PutMapping("/{id}/cancel")
  @Operation(summary = "取消计划通知", description = "取消指定的计划通知")
  public ResponseEntity<Void> cancelScheduledNotification(@PathVariable Long id) {
    log.debug("REST request to cancel scheduled notification: {}", id);
    notificationCenterService.cancelScheduledNotification(id);
    return ResponseEntity.ok().build();
  }

  /**
   * 重试失败通知
   */
  @PostMapping("/retry-failed")
  @Operation(summary = "重试失败通知", description = "重新发送失败的通知")
  public ResponseEntity<Void> retryFailedNotifications() {
    log.debug("REST request to retry failed notifications");
    notificationCenterService.retryFailedNotifications();
    return ResponseEntity.ok().build();
  }

  /**
   * 获取批量通知状态
   */
  @GetMapping("/batch/{batchId}/status")
  @Operation(summary = "获取批量通知状态", description = "获取批量通知的处理状态")
  public ResponseEntity<BatchNotificationStatusDTO> getBatchNotificationStatus(@PathVariable String batchId) {
    log.debug("REST request to get batch notification status: {}", batchId);
    BatchNotificationStatusDTO status = notificationCenterService.getBatchNotificationStatus(batchId);
    return ResponseEntity.ok(status);
  }
}

```

## 4. 通知统计 API

### 4.1 NotificationStatisticsResource

```java
@RestController
@RequestMapping("/api/notification-statistics")
@Tag(name = "notification-statistics", description = "通知统计 API")
public class NotificationStatisticsResource {

  private final Logger log = LoggerFactory.getLogger(NotificationStatisticsResource.class);

  private final NotificationStatisticsService notificationStatisticsService;

  /**
   * 获取通知发送统计
   */
  @GetMapping("/send-stats")
  @Operation(summary = "获取通知发送统计", description = "获取指定时间范围内的通知发送统计")
  public ResponseEntity<NotificationSendStatsDTO> getSendStatistics(
    @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
    @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
    @RequestParam(required = false) NotificationCategory category
  ) {
    log.debug("REST request to get notification send statistics from {} to {}", startDate, endDate);

    NotificationSendStatsDTO stats = notificationStatisticsService.getSendStatistics(startDate, endDate, category);

    return ResponseEntity.ok(stats);
  }

  /**
   * 获取用户通知偏好统计
   */
  @GetMapping("/preference-stats")
  @Operation(summary = "获取用户通知偏好统计", description = "获取用户通知偏好的统计信息")
  public ResponseEntity<NotificationPreferenceStatsDTO> getPreferenceStatistics() {
    log.debug("REST request to get notification preference statistics");

    NotificationPreferenceStatsDTO stats = notificationStatisticsService.getPreferenceStatistics();

    return ResponseEntity.ok(stats);
  }

  /**
   * 获取通知渠道效果统计
   */
  @GetMapping("/channel-stats")
  @Operation(summary = "获取通知渠道效果统计", description = "获取各通知渠道的效果统计")
  public ResponseEntity<NotificationChannelStatsDTO> getChannelStatistics(
    @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
    @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate
  ) {
    log.debug("REST request to get notification channel statistics from {} to {}", startDate, endDate);

    NotificationChannelStatsDTO stats = notificationStatisticsService.getChannelStatistics(startDate, endDate);

    return ResponseEntity.ok(stats);
  }
}

```

这个API设计完全符合JHipster规范，包括：

1. **标准RESTful设计**: 使用标准HTTP方法和状态码
2. **OpenAPI文档**: 完整的API文档注解
3. **统一错误处理**: 使用JHipster的错误处理机制
4. **分页支持**: 标准的分页、排序、过滤支持
5. **安全认证**: 集成JHipster的安全框架
6. **国际化支持**: 支持多语言错误消息

您觉得这个API设计如何？还需要补充哪些接口？
