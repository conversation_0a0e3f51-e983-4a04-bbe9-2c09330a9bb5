# 通知模块实现计划

## 1. 实体设计完善

### 1.1 审计字段标准化

根据 `SensitiveWord.java` 实体，所有通知模块实体都包含以下标准审计字段：

```java
// 标准审计字段
/** 乐观锁版本 */
@NotNull
@Column(name = "version", nullable = false)
private Integer version;

/** 创建者 */
@Column(name = "created_by", length = 50)
private String createdBy;

/** 创建时间 */
@NotNull
@Column(name = "created_at", nullable = false)
private Instant createdAt;

/** 更新者 */
@Column(name = "updated_by", length = 50)
private String updatedBy;

/** 更新时间 */
@NotNull
@Column(name = "updated_at", nullable = false)
private Instant updatedAt;

/** 软删除标志 */
@NotNull
@Column(name = "is_deleted", nullable = false)
private Boolean isDeleted;

/** 租户ID（0 = 平台级） */
@NotNull
@Column(name = "tenant_id", nullable = false)
private Long tenantId;

```

### 1.2 租户隔离策略

- **租户ID = 0**: 平台级数据，所有租户可见
- **租户ID > 0**: 租户级数据，仅对应租户可见
- **数据查询**: 自动添加租户ID过滤条件
- **数据创建**: 自动设置当前租户ID

## 2. JDL 文件优化

### 2.1 符合项目规范的 JDL 定义

- ✅ 枚举注释格式：`/** 描述 */ ENUM_VALUE`
- ✅ 实体注释格式：`/** 实体描述 */`
- ✅ 字段注释格式：`/** 字段描述 */`
- ✅ 完整的审计字段定义
- ✅ 租户ID字段必填
- ✅ 乐观锁版本控制
- ✅ 软删除支持

### 2.2 数据库表设计

```sql
-- 通知记录表
CREATE TABLE notification_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    category VARCHAR(50) NOT NULL,
    sub_type VARCHAR(100) NOT NULL,
    scope VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content VARCHAR(2000),
    recipient_type VARCHAR(50) NOT NULL,
    recipient_ids VARCHAR(2000),
    channels VARCHAR(500),
    priority VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    scheduled_time TIMESTAMP,
    sent_time TIMESTAMP,
    business_id VARCHAR(100),
    business_type VARCHAR(50),
    template_params VARCHAR(2000),
    retry_count INT DEFAULT 0,
    error_message VARCHAR(1000),
    version INT NOT NULL DEFAULT 1,
    created_by VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50),
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_status (status),
    INDEX idx_category_subtype (category, sub_type),
    INDEX idx_business (business_type, business_id),
    INDEX idx_created_at (created_at)
);

-- 通知模板表
CREATE TABLE notification_template (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    code VARCHAR(100) NOT NULL,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(50) NOT NULL,
    sub_type VARCHAR(100) NOT NULL,
    title_template VARCHAR(500),
    content_template VARCHAR(2000),
    sms_template VARCHAR(500),
    email_template VARCHAR(2000),
    push_template VARCHAR(500),
    supported_channels VARCHAR(200),
    default_channels VARCHAR(200),
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    language VARCHAR(10) DEFAULT 'zh-CN',
    version INT NOT NULL DEFAULT 1,
    created_by VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50),
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    UNIQUE KEY uk_tenant_code (tenant_id, code),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_category_subtype (category, sub_type),
    INDEX idx_enabled (enabled)
);

-- 用户通知偏好表
CREATE TABLE user_notification_preference (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    category VARCHAR(50) NOT NULL,
    sub_type VARCHAR(100),
    enabled_channels VARCHAR(200),
    quiet_hours_start VARCHAR(5),
    quiet_hours_end VARCHAR(5),
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    version INT NOT NULL DEFAULT 1,
    created_by VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50),
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    UNIQUE KEY uk_tenant_user_category (tenant_id, user_id, category, sub_type),
    INDEX idx_tenant_user (tenant_id, user_id),
    INDEX idx_enabled (enabled)
);

-- 通知发送记录表
CREATE TABLE notification_send_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    notification_id BIGINT NOT NULL,
    recipient_id BIGINT NOT NULL,
    recipient_type VARCHAR(50) NOT NULL,
    channel VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    sent_time TIMESTAMP,
    read_time TIMESTAMP,
    error_message VARCHAR(1000),
    external_id VARCHAR(100),
    version INT NOT NULL DEFAULT 1,
    created_by VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50),
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY (notification_id) REFERENCES notification_record(id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_notification_id (notification_id),
    INDEX idx_recipient (recipient_id),
    INDEX idx_status (status),
    INDEX idx_channel (channel)
);
```

## 3. 实施步骤

### 第一阶段：基础实体生成

1. **使用 JDL 生成实体**

   ```bash
   jhipster jdl docs/notification.jdl
   ```

2. **验证生成的代码**

   - 检查实体类是否包含所有审计字段
   - 验证数据库表结构
   - 确认 Repository、Service、Resource 层代码

3. **添加租户上下文支持**
   - 集成现有的 `TenantContextUtil`
   - 在 Repository 查询中自动添加租户过滤
   - 在数据创建时自动设置租户ID

### 第二阶段：核心服务实现

1. **通知中心服务**

   - 实现统一的通知发送入口
   - 支持同步和异步发送
   - 集成现有的短信、邮件服务

2. **模板管理服务**

   - 模板的 CRUD 操作
   - 模板渲染引擎集成
   - 租户级模板管理

3. **用户偏好服务**
   - 用户偏好设置管理
   - 免打扰时间处理
   - 渠道选择逻辑

### 第三阶段：高级功能

1. **批量通知处理**

   - 异步批量发送
   - 进度跟踪
   - 失败重试机制

2. **统计分析功能**

   - 发送成功率统计
   - 用户偏好分析
   - 渠道效果分析

3. **事件驱动集成**
   - Spring Event 集成
   - 消息队列支持
   - 与其他微服务的事件集成

## 4. 配置和集成

### 4.1 应用配置扩展

```yaml
application:
  notification:
    # 现有配置保持不变
    center:
      enabled: true
      async-enabled: true
      batch-size: 100
      retry-max-attempts: 3
      retry-delay-seconds: 60
    template:
      cache-enabled: true
      cache-ttl-minutes: 30
    preference:
      default-channels: ['EMAIL', 'PUSH']
      quiet-hours-enabled: true
```

### 4.2 安全和权限

- 基于租户的数据隔离
- API 访问权限控制
- 敏感信息脱敏处理

### 4.3 监控和日志

- 通知发送成功率监控
- 失败通知告警
- 详细的审计日志

## 5. 测试策略

### 5.1 单元测试

- Repository 层测试
- Service 层业务逻辑测试
- 模板渲染测试

### 5.2 集成测试

- 端到端通知发送测试
- 多租户数据隔离测试
- 异步处理测试

### 5.3 性能测试

- 批量通知性能测试
- 并发发送压力测试
- 数据库查询性能测试

这个实现计划完全符合您的项目规范，包含了完整的审计字段、租户支持和 JHipster 标准。您觉得可以开始实施了吗？
