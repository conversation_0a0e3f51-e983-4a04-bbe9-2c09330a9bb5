/**
 * 敏感词严重级别
 */
enum SeverityType {
  /** 阻断：命中即拒绝请求 */ BLOCK,
  /** 待复核：命中后进入人工审核队列 */ REVIEW,
  /** 替换：命中后可自动脱敏替换 */ REPLACE
}

/**
 * 敏感词分类
 */
enum SensitiveCategory {
  /** 政治敏感 */ POLITICAL,
  /** 暴恐 */ VIOLENCE,
  /** 色情 */ PORNOGRAPHY,
  /** 赌博 */ GAMBLING,
  /** 欺诈/诈骗 */ FRAUD,
  /** 个人隐私/PII */ PII,
  /** 广告法违规 */ ADVERTISING,
  /** 毒品 */ DRUGS,
  /** 其他 */ OTHER
}

/**
 * 支持的语言
 */
enum LanguageType {
  ZH,   // 中文
  EN,   // English
  ES,   // Español
  JA,   // 日本語
  KO,   // 한국어
  FR,   // Français
  DE,   // Deutsch
  RU,   // Русский
  OTHER // 其他
}

/** 敏感词实体 */
/** 存储平台级或租户级的敏感词及策略信息 */
entity SensitiveWord {
  /** 主键ID */
  id Long required,
  /** 租户ID（0 = 平台级） */
  tenantId Long required,
  /** 敏感词条 */
  term String required,
  /** 语言 */
  lang LanguageType required,
  /** 分类 */
  category SensitiveCategory required,
  /** 严重级别 */
  severity SeverityType required,
  /** 有效开始时间 */
  validFrom Instant required,
  /** 有效结束时间 */
  validTo Instant required,
  /** 备注 */
  notes String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/* 生成选项 */
dto SensitiveWord with mapstruct
service SensitiveWord with serviceClass
paginate SensitiveWord with pagination
