# 天眼查 API 集成使用说明

## 项目概述

本文档详细说明了如何将天眼查 API 集成到您的 JHipster Spring Cloud 微服务 `whiskerguard-general-service` 中。该集成实现了企业信息查询功能，并提供本地数据库缓存以节省 API 调用费用。

## 功能特性

### 支持的天眼查 API 接口

1. **企业基本信息** (`/baseinfo/normal`) - 获取企业的基础信息，包括名称、统一社会信用代码、法人代表、注册资本等
2. **企业天眼风险** (`/risk/company`) - 获取企业相关的风险信息
3. **企业工商信息** (`/baseinfo/business`) - 获取详细的工商注册信息
4. **企业变更记录** (`/baseinfo/changeinfo`) - 获取企业工商信息变更历史
5. **企业类型** (`/baseinfo/companytype`) - 获取企业类型信息
6. **企业联系方式** (`/baseinfo/contact`) - 获取企业联系信息
7. **企业三要素验证** (`/baseinfo/verify`) - 验证企业名称、统一社会信用代码和法人姓名
8. **企业历史失信人** (`/risk/dishonest`) - 获取企业相关的失信人记录
9. **企业立案信息** (`/risk/casefiling`) - 获取企业相关的法律案件信息

### 核心功能

- **智能缓存机制**: 自动缓存查询结果到本地数据库，避免重复 API 调用
- **多种查询方式**: 支持通过企业名称、统一社会信用代码、注册号、税号等多种方式查询
- **数据持久化**: 完整的数据库实体设计，支持企业信息的长期存储
- **错误处理**: 完善的异常处理和重试机制
- **RESTful API**: 提供标准的 REST 接口供前端或其他服务调用

## 安装步骤

### 1. 复制代码文件

将提供的代码文件按照以下目录结构复制到您的项目中：

```
src/main/java/com/whiskerguard/general/
├── config/
│   ├── TianyanchaConfiguration.java
│   └── TianyanchaProperties.java
├── domain/
│   ├── Company.java
│   ├── CompanyCaseFiling.java
│   ├── CompanyChangeRecord.java
│   ├── CompanyContact.java
│   ├── CompanyDishonestPerson.java
│   └── CompanyRisk.java
├── repository/
│   ├── CompanyCaseFilingRepository.java
│   ├── CompanyChangeRecordRepository.java
│   ├── CompanyContactRepository.java
│   ├── CompanyDishonestPersonRepository.java
│   ├── CompanyRepository.java
│   └── CompanyRiskRepository.java
├── service/
│   ├── client/
│   │   └── TianyanchaApiClient.java
│   ├── dto/
│   │   ├── tianyancha/
│   │   │   ├── TianyanchaBaseResponseDTO.java
│   │   │   ├── TianyanchaCompanyBasicInfoResponseDTO.java
│   │   │   └── TianyanchaCompanyVerificationResponseDTO.java
│   │   ├── CompanyDTO.java
│   │   └── CompanyVerificationResultDTO.java
│   ├── exception/
│   │   └── TianyanchaApiException.java
│   ├── mapper/
│   │   └── CompanyMapper.java
│   ├── CompanyService.java
│   └── TianyanchaQueryService.java
└── web/rest/
    └── TianyanchaResource.java
```

### 2. 添加数据库迁移文件

将以下 Liquibase 迁移文件复制到 `src/main/resources/config/liquibase/changelog/` 目录：

- `20250610000001_added_entity_Company.xml`
- `20250610000002_added_entity_CompanyContact.xml`
- `20250610000003_added_entity_CompanyRisk.xml`
- `20250610000004_added_entity_CompanyChangeRecord.xml`
- `20250610000005_added_entity_CompanyDishonestPerson.xml`
- `20250610000006_added_entity_CompanyCaseFiling.xml`

### 3. 更新主 Liquibase 配置

在您的 `src/main/resources/config/liquibase/master.xml` 文件中添加以下条目：

```xml
<include file="config/liquibase/changelog/20250610000001_added_entity_Company.xml" relativeToChangelogFile="false"/>
<include file="config/liquibase/changelog/20250610000002_added_entity_CompanyContact.xml" relativeToChangelogFile="false"/>
<include file="config/liquibase/changelog/20250610000003_added_entity_CompanyRisk.xml" relativeToChangelogFile="false"/>
<include file="config/liquibase/changelog/20250610000004_added_entity_CompanyChangeRecord.xml" relativeToChangelogFile="false"/>
<include file="config/liquibase/changelog/20250610000005_added_entity_CompanyDishonestPerson.xml" relativeToChangelogFile="false"/>
<include file="config/liquibase/changelog/20250610000006_added_entity_CompanyCaseFiling.xml" relativeToChangelogFile="false"/>
```

### 4. 添加依赖

确保您的 `pom.xml` 包含以下依赖（JHipster 项目通常已包含）：

```xml
<dependency>
    <groupId>org.mapstruct</groupId>
    <artifactId>mapstruct</artifactId>
</dependency>
<dependency>
    <groupId>org.mapstruct</groupId>
    <artifactId>mapstruct-processor</artifactId>
    <scope>provided</scope>
</dependency>
```

### 5. 配置天眼查 API

在您的 `application.yml` 文件中添加天眼查 API 配置：

```yaml
tianyancha:
  api-token: 'YOUR_TIANYANCHA_API_TOKEN' # 替换为您的天眼查 API Token
  base-url: 'http://open.api.tianyancha.com/services/open/ic/'
  cache-expiration-hours: 168 # 缓存过期时间（小时），默认7天
  connection-timeout-ms: 5000 # 连接超时时间（毫秒）
  read-timeout-ms: 10000 # 读取超时时间（毫秒）
  max-retry-attempts: 3 # 最大重试次数
  retry-delay-ms: 1000 # 重试延迟时间（毫秒）
```

**重要**: 请将 `YOUR_TIANYANCHA_API_TOKEN` 替换为您从天眼查开放平台获取的实际 API Token。

## API 使用说明

### 基础企业信息查询

**接口**: `GET /api/tianyancha/company/basic-info`

**参数**:

- `keyword` (必需): 查询关键词，可以是企业名称、统一社会信用代码、注册号或税号

**示例**:

```bash
curl -X GET "http://localhost:8080/api/tianyancha/company/basic-info?keyword=中航重机股份有限公司"
```

**响应示例**:

```json
{
  "id": 1,
  "tianyanchaId": 11684584,
  "name": "中航重机股份有限公司",
  "unifiedSocialCreditCode": "91520000214434146R",
  "legalPersonName": "姬苏春",
  "regStatus": "存续",
  "regCapital": "77800.32万人民币",
  "establishTime": "1996-11-20T00:00:00Z",
  "companyOrgType": "其他股份有限公司(上市)",
  "industry": "汽车制造业",
  "regLocation": "贵州双龙航空港经济区机场路9号太升国际A栋3单元5层",
  "cacheTime": "2025-06-10T02:45:00Z"
}
```

### 企业三要素验证

**接口**: `POST /api/tianyancha/company/verify`

**请求体**:

```json
{
  "companyName": "中航重机股份有限公司",
  "creditCode": "91520000214434146R",
  "legalPersonName": "姬苏春"
}
```

**响应示例**:

```json
{
  "verifyResult": "一致",
  "companyName": "中航重机股份有限公司",
  "creditCode": "91520000214434146R",
  "legalPersonName": "姬苏春",
  "message": "企业三要素验证通过"
}
```

### 其他企业信息查询

以下接口返回原始的 JSON 数据，您可以根据需要进行解析：

1. **企业风险信息**: `GET /api/tianyancha/company/risk?keyword={keyword}`
2. **企业联系方式**: `GET /api/tianyancha/company/contact?keyword={keyword}`
3. **企业变更记录**: `GET /api/tianyancha/company/change-records?keyword={keyword}`
4. **企业类型信息**: `GET /api/tianyancha/company/type?keyword={keyword}`
5. **企业工商信息**: `GET /api/tianyancha/company/business?keyword={keyword}`
6. **企业失信人记录**: `GET /api/tianyancha/company/dishonest-persons?keyword={keyword}`
7. **企业立案信息**: `GET /api/tianyancha/company/case-filings?keyword={keyword}`

### 强制刷新数据

**接口**: `POST /api/tianyancha/company/refresh`

**参数**:

- `keyword` (必需): 查询关键词

此接口会绕过缓存，强制从天眼查 API 获取最新数据并更新本地缓存。

## 缓存机制说明

### 缓存策略

1. **首次查询**: 当查询一个企业信息时，系统首先检查本地数据库是否存在该企业的缓存数据
2. **缓存命中**: 如果存在缓存且数据未过期（默认7天），直接返回缓存数据
3. **缓存失效**: 如果缓存不存在或已过期，调用天眼查 API 获取最新数据，并更新本地缓存
4. **智能匹配**: 支持通过多种标识符（企业名称、统一社会信用代码、注册号、税号）查找缓存数据

### 缓存配置

可以通过 `application.yml` 中的 `tianyancha.cache-expiration-hours` 配置缓存过期时间。建议设置为 168 小时（7天）以平衡数据新鲜度和 API 调用成本。

## 错误处理

### 常见错误类型

1. **API 认证失败**: 检查 API Token 是否正确配置
2. **网络超时**: 调整 `connection-timeout-ms` 和 `read-timeout-ms` 配置
3. **API 限流**: 系统会自动重试，可调整 `max-retry-attempts` 和 `retry-delay-ms`
4. **企业不存在**: API 返回相应错误信息

### 日志监控

系统会记录详细的操作日志，包括：

- API 调用记录
- 缓存命中/失效情况
- 错误和重试信息

建议在生产环境中监控这些日志以优化性能和排查问题。

## 性能优化建议

### 数据库索引

系统已为以下字段创建索引以提高查询性能：

- `unified_social_credit_code`
- `name`
- `reg_number`
- `tax_number`
- `tianyancha_id`

### 批量查询

对于批量企业信息查询需求，建议：

1. 先检查本地缓存，只对缓存中不存在的企业调用 API
2. 控制并发 API 调用数量，避免触发限流
3. 考虑使用异步处理大批量查询

### 缓存管理

定期清理过期缓存数据以节省存储空间：

```sql
DELETE FROM company WHERE cache_time < NOW() - INTERVAL 30 DAY;
```

## 安全注意事项

1. **API Token 安全**:

   - 不要在代码中硬编码 API Token
   - 使用环境变量或配置中心管理敏感信息
   - 定期轮换 API Token

2. **数据访问控制**:

   - 根据业务需要限制 API 访问权限
   - 记录和监控 API 使用情况

3. **数据隐私**:
   - 遵守相关数据保护法规
   - 合理设置数据保留期限

## 故障排除

### 常见问题

**Q: API 调用失败，返回认证错误**
A: 检查 `tianyancha.api-token` 配置是否正确，确保 Token 有效且有足够的调用次数。

**Q: 数据库连接错误**
A: 确保 Liquibase 迁移已正确执行，检查数据库表是否创建成功。

**Q: 查询结果为空**
A: 检查查询关键词是否正确，某些企业可能在天眼查数据库中不存在。

**Q: 缓存数据不更新**
A: 使用强制刷新接口 (`POST /api/tianyancha/company/refresh`) 更新缓存，或调整缓存过期时间。

### 调试模式

在开发环境中，可以通过以下配置启用详细日志：

```yaml
logging:
  level:
    com.whiskerguard.general.client.TianyanchaApiClient: DEBUG
    com.whiskerguard.general.service.TianyanchaQueryService: DEBUG
```

## 扩展开发

### 添加新的天眼查 API

1. 在 `TianyanchaApiClient` 中添加新的方法
2. 创建对应的响应 DTO 类
3. 在 `TianyanchaQueryService` 中添加业务逻辑
4. 在 `TianyanchaResource` 中暴露 REST 接口

### 自定义缓存策略

可以通过修改 `TianyanchaQueryService` 中的缓存逻辑来实现自定义缓存策略，例如：

- 基于企业类型的差异化缓存时间
- 基于查询频率的智能缓存
- 缓存预热机制

## 技术支持

如果在集成过程中遇到问题，请检查：

1. 代码文件是否正确复制到对应目录
2. 配置文件是否正确设置
3. 数据库迁移是否成功执行
4. 天眼查 API Token 是否有效

建议在测试环境中先进行完整测试，确认功能正常后再部署到生产环境。
