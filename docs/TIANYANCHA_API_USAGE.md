# 天眼查API集成使用指南

## 概述

本项目已集成天眼查API v2.0，提供企业基本信息查询、风险信息查询、企业三要素验证等功能。

## API配置

### 1. 获取API Token

首先需要在天眼查开放平台获取API Token：

1. 访问 [天眼查开放平台](https://open.tianyancha.com/)
2. 注册账号并申请API服务
3. 获取您的API Token

### 2. 配置API Token

有两种方式配置API Token：

#### 方式一：在application.yml中配置

```yaml
tianyancha:
  api-token: your-actual-api-token-here
  base-url: https://open.api.tianyancha.com/services/open/ic/
  connection-timeout-ms: 10000
  read-timeout-ms: 30000
  max-retry-attempts: 3
  retry-delay-ms: 1000
```

#### 方式二：使用环境变量

```bash
export TIANYANCHA_API_TOKEN=your-actual-api-token-here
```

## API端点说明

当前使用的API版本和端点：

- **基础URL**: `https://open.api.tianyancha.com/services/open/ic/`
- **企业基本信息**: `baseinfoV2/2.0`
- **完整URL**: `https://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0`

## 使用示例

### 1. 通过REST API调用

#### 查询企业基本信息

```bash
# GET请求
curl -X GET "http://localhost:8080/api/tianyancha/company/basic-info?keyword=中合数联（苏州）科技有限公司" \
  -H "Accept: application/json"
```

#### 验证企业三要素

```bash
# POST请求
curl -X POST "http://localhost:8080/api/tianyancha/company/verify" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "companyName": "中合数联（苏州）科技有限公司",
    "creditCode": "91320505MAE9MJCD5Q",
    "legalPersonName": "罗琼"
  }'
```

#### 查询企业风险信息

```bash
# GET请求
curl -X GET "http://localhost:8080/api/tianyancha/company/risk?keyword=中合数联（苏州）科技有限公司" \
  -H "Accept: application/json"
```

### 2. 在Java代码中使用

```java
@Autowired
private TianyanchaQueryService tianyanchaQueryService;

// 查询企业基本信息
public CompanyDTO getCompanyInfo(String keyword) {
  return tianyanchaQueryService.getCompanyBasicInfo(keyword);
}

// 验证企业三要素
public CompanyVerificationResultDTO verifyCompany(String companyName, String creditCode, String legalPersonName) {
  return tianyanchaQueryService.verifyCompanyThreeElements(companyName, creditCode, legalPersonName);
}

```

## 测试工具

项目提供了多个测试工具：

### 1. 单元测试

```bash
# 运行单元测试
mvn test -Dtest=TianyanchaApiClientTest
```

### 2. 集成测试（需要真实API Token）

```bash
# 设置API Token并运行集成测试
export TIANYANCHA_API_TOKEN=your-actual-token
mvn test -Dtest=TianyanchaApiIntegrationTest
```

### 3. REST端点测试

```bash
# 启动应用
mvn spring-boot:run

# 在另一个终端运行REST端点测试
./test_rest_endpoints.sh
```

### 4. 直接API调用测试

```bash
# 使用curl直接测试天眼查API
export API_TOKEN=your-actual-token
./test_api_call.sh
```

### 5. Java测试工具

```bash
# 编译测试类
mvn test-compile

# 运行Java测试工具
mvn exec:java -Dexec.mainClass=com.whiskerguard.general.util.TianyanchaApiTestUtil \
  -Dexec.args="your-api-token 中合数联（苏州）科技有限公司"
```

## 官方API示例

根据天眼查官方文档，API调用示例：

### 请求

```
GET https://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0?keyword=中合数联（苏州）科技有限公司
Authorization: your-api-token
Content-Type: application/json
```

### 响应

```json
{
  "result": {
    "regStatus": "存续",
    "type": 1,
    "property3": "",
    "approvedTime": 1748188800000,
    "id": 7374483149,
    "orgNumber": "MAE9MJCD-5",
    "businessScope": "许可项目：第一类增值电信业务...",
    "taxNumber": "91320505MAE9MJCD5Q",
    "regCapitalCurrency": "人民币",
    "tags": "存续",
    "district": "虎丘区",
    "name": "中合数联（苏州）科技有限公司",
    "percentileScore": 5839,
    "industryAll": {
      "categoryMiddle": "互联网信息服务",
      "categoryBig": "互联网和相关服务",
      "category": "信息传输、软件和信息技术服务业",
      "categorySmall": ""
    },
    "isMicroEnt": 0,
    "regCapital": "100万人民币",
    "city": "苏州市",
    "industry": "互联网和相关服务",
    "updateTimes": 1748522941000,
    "legalPersonName": "罗琼",
    "regNumber": "320512001036851",
    "creditCode": "91320505MAE9MJCD5Q",
    "fromTime": 1735833600000,
    "actualCapitalCurrency": "人民币",
    "alias": "中合数联",
    "companyOrgType": "有限责任公司(自然人投资或控股)",
    "email": "",
    "actualCapital": "100万人民币",
    "estiblishTime": 1735833600000,
    "regInstitute": "苏州高新区（虎丘区）数据局",
    "regLocation": "苏州高新区鹿山路369号39幢627室",
    "base": "js"
  },
  "reason": "ok",
  "error_code": 0
}
```

## 错误处理

系统会自动处理以下错误情况：

1. **网络超时**: 自动重试，最多3次
2. **API限流**: 指数退避重试
3. **认证失败**: 返回明确的错误信息
4. **数据解析错误**: 记录详细日志

## 缓存机制

- 企业信息会自动缓存到本地数据库
- 默认缓存有效期：7天
- 可通过`/api/tianyancha/company/refresh`强制刷新

## 性能优化

- 连接超时：10秒
- 读取超时：30秒
- 连接池：自动管理
- 重试机制：指数退避

## 安全考虑

- API Token通过Header传递
- 支持HTTPS连接
- 敏感信息不记录到日志

## 故障排除

### 常见问题

1. **401 Unauthorized**

   - 检查API Token是否正确
   - 确认Token未过期

2. **400 Bad Request**

   - 检查请求参数格式
   - 确认企业名称编码正确

3. **500 Internal Server Error**

   - 检查网络连接
   - 查看应用日志

4. **网络超时**
   - 检查防火墙设置
   - 增加超时时间配置

### 日志查看

```bash
# 查看应用日志
tail -f logs/whiskerguard-general-service.log

# 过滤天眼查相关日志
grep -i tianyancha logs/whiskerguard-general-service.log
```

## 更新记录

- **2025-06-11**: 更新到API v2.0，端点从`baseinfo/normal`改为`baseinfoV2/2.0`
- **2025-06-11**: 修改基础URL从HTTP改为HTTPS
- **2025-06-11**: 添加完整的测试工具和文档

## 相关链接

- [天眼查开放平台](https://open.tianyancha.com/)
- [API文档](https://open.tianyancha.com/open/api_doc)
- [项目GitHub](https://github.com/your-repo/whiskerguard-general-service)
