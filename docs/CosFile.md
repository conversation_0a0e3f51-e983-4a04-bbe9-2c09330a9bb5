这是我要给其他微服务通过 Feign 提供的接口：
// ========== COS文件服务相关接口 ==========

    /**
     * 根据文件名读取腾讯云COS中的文件内容
     * <p>
     * 通过文件名从腾讯云COS读取文件内容，支持文本文件、PDF、Word等格式。
     * 返回的内容已经过格式转换，可以直接用于AI分析。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件的文本内容，如果是二进制文件会转换为文本格式
     */
    @PostMapping("/api/file/cos/content")
    String readFileContent(@RequestBody FileOperationRequestDTO request);

    /**
     * 检查腾讯云COS中的文件是否存在
     * <p>
     * 验证指定的文件名在腾讯云COS中是否存在，
     * 用于在读取文件内容前进行预检查。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件是否存在的布尔值
     */
    @PostMapping("/api/file/cos/exists")
    Boolean checkFileExists(@RequestBody FileOperationRequestDTO request);

    /**
     * 获取文件的基本信息
     * <p>
     * 获取文件的元数据信息，包括文件大小、类型、创建时间等。
     * 用于在处理文件前了解文件的基本属性。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件信息的JSON字符串
     */
    @PostMapping("/api/file/cos/info")
    String getFileInfo(@RequestBody FileOperationRequestDTO request);

这是我的 FileOperationRequestDTO 类：

/\*\*

- 文件操作请求DTO
- <p>
- 用于通用服务的文件操作接口，包括文件读取、存在性检查和信息获取等操作。
-
- <AUTHOR>
- @since 1.0
  \*/
  public class FileOperationRequestDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  /\*\*

  - 腾讯云COS中的文件名（包含路径）
    \*/
    private String cosFileName;

  /\*\*

  - 租户ID，用于多租户数据隔离和权限控制
    \*/
    private Long tenantId;

  /\*\*

  - 默认构造函数
    \*/
    public FileOperationRequestDTO() {}

  /\*\*

  - 构造函数
  -
  - @param cosFileName 腾讯云COS中的文件名
  - @param tenantId 租户ID
    \*/
    public FileOperationRequestDTO(String cosFileName, Long tenantId) {
    this.cosFileName = cosFileName;
    this.tenantId = tenantId;
    }

  /\*\*

  - 获取文件名
  -
  - @return 腾讯云COS中的文件名
    \*/
    public String getCosFileName() {
    return cosFileName;
    }

  /\*\*

  - 设置文件名
  -
  - @param cosFileName 腾讯云COS中的文件名
    \*/
    public void setCosFileName(String cosFileName) {
    this.cosFileName = cosFileName;
    }

  /\*\*

  - 获取租户ID
  -
  - @return 租户ID
    \*/
    public Long getTenantId() {
    return tenantId;
    }

  /\*\*

  - 设置租户ID
  -
  - @param tenantId 租户ID
    \*/
    public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
    }

  @Override
  public String toString() {
  return "FileOperationRequestDTO{" + "cosFileName='" + cosFileName + '\'' + ", tenantId=" + tenantId + '}';
  }
  }
