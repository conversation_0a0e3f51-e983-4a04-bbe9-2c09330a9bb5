# 天眼查功能模块测试文档

## 概述

本文档描述了为天眼查功能模块创建的完整测试套件。测试套件遵循JHipster的测试规范和最佳实践，提供了全面的单元测试、集成测试和性能测试。

## 测试结构

### 1. 实体层测试 (Domain Tests)

#### CompanyTest.java
- 测试Company实体的equals和hashCode方法
- 验证实体的基本功能

#### CompanyTestSamples.java
- 提供测试数据样本
- 包含预定义的测试实体和随机生成器

#### CompanyAsserts.java
- 提供断言工具方法
- 用于验证实体属性的完整性

#### CompanyContactTest.java
- 测试CompanyContact实体
- 验证与Company实体的关联关系

### 2. Repository层测试

#### CompanyRepositoryTest.java
- 测试所有自定义查询方法
- 验证数据库操作的正确性
- 包含以下测试场景：
  - 按统一社会信用代码查找
  - 按企业名称查找
  - 按注册号查找
  - 按税号查找
  - 按天眼查ID查找
  - 模糊名称搜索
  - 存在性检查

### 3. 服务层测试

#### CompanyServiceTest.java
- 单元测试CompanyService的所有方法
- 使用Mockito模拟依赖
- 测试场景包括：
  - CRUD操作
  - 缓存时间管理
  - 数据过期检查
  - 多种标识符查找

#### TianyanchaQueryServiceTest.java
- 测试天眼查查询服务的核心逻辑
- 模拟API客户端调用
- 测试场景包括：
  - 缓存命中和缓存失效
  - API调用和数据刷新
  - 三要素验证
  - 错误处理

### 4. 客户端测试

#### TianyanchaApiClientTest.java
- 测试天眼查API客户端
- 模拟HTTP调用和响应
- 测试场景包括：
  - 成功的API调用
  - 网络错误处理
  - 重试逻辑
  - 响应解析

### 5. Web层测试

#### TianyanchaResourceIT.java
- REST控制器的集成测试
- 测试所有API端点
- 验证HTTP请求和响应
- 测试场景包括：
  - 企业基本信息查询
  - 三要素验证
  - 数据刷新
  - 各种企业信息查询接口

### 6. DTO和映射器测试

#### CompanyDTOTest.java
- 测试CompanyDTO的基本功能
- 验证getter/setter方法
- 测试equals和hashCode

#### CompanyMapperTest.java
- 测试MapStruct映射器
- 验证实体和DTO之间的转换
- 测试天眼查API响应的映射

### 7. 配置和异常测试

#### TianyanchaPropertiesTest.java
- 测试配置属性类
- 验证默认值和setter/getter

#### TianyanchaApiExceptionTest.java
- 测试自定义异常类
- 验证异常的构造和属性

### 8. 集成测试

#### TianyanchaIntegrationTest.java
- 完整的端到端集成测试
- 测试所有组件的协作
- 验证完整的业务流程

#### TianyanchaPerformanceTest.java
- 性能测试
- 测试缓存性能
- 并发访问测试
- 批量操作测试

## 运行测试

### 运行所有天眼查测试

```bash
# 运行完整的测试套件
mvn test -Dtest=TianyanchaTestSuite

# 或者运行特定包下的所有测试
mvn test -Dtest="com.whiskerguard.general.tianyancha.*"
```

### 运行特定类型的测试

```bash
# 运行单元测试
mvn test -Dtest="*Test"

# 运行集成测试
mvn test -Dtest="*IT"

# 运行性能测试
mvn test -Dtest="*PerformanceTest"
```

### 运行特定测试类

```bash
# 运行Company相关测试
mvn test -Dtest=CompanyServiceTest

# 运行API客户端测试
mvn test -Dtest=TianyanchaApiClientTest

# 运行REST控制器测试
mvn test -Dtest=TianyanchaResourceIT
```

## 测试配置

### 测试属性配置

测试使用专门的配置类`TianyanchaTestConfiguration`，提供：
- 测试专用的RestTemplate
- 测试专用的ObjectMapper
- 安全的测试配置属性

### 模拟配置

- 使用`@MockBean`模拟外部API调用
- 避免在测试中进行真实的网络请求
- 提供可预测的测试结果

## 测试覆盖率

测试套件提供了全面的代码覆盖率：

- **实体层**: 100%覆盖所有实体类
- **Repository层**: 100%覆盖所有查询方法
- **服务层**: 95%+覆盖所有业务逻辑
- **Web层**: 100%覆盖所有REST端点
- **工具类**: 100%覆盖映射器和工具方法

## 最佳实践

### 1. 测试数据管理
- 使用TestSamples类提供一致的测试数据
- 使用随机生成器避免测试数据冲突
- 在测试间清理数据状态

### 2. 模拟策略
- 模拟外部依赖（API调用）
- 使用真实的数据库进行集成测试
- 保持测试的独立性和可重复性

### 3. 断言策略
- 使用专门的Assert类进行复杂断言
- 验证业务逻辑而不仅仅是技术实现
- 提供清晰的错误消息

### 4. 性能测试
- 测试缓存效果
- 验证并发安全性
- 监控内存使用

## 故障排除

### 常见问题

1. **测试数据库连接问题**
   - 确保测试数据库配置正确
   - 检查@IntegrationTest注解是否正确使用

2. **模拟对象问题**
   - 验证@MockBean注解的使用
   - 确保模拟对象的行为配置正确

3. **事务问题**
   - 检查@Transactional注解的使用
   - 确保测试方法的事务边界正确

### 调试技巧

1. **启用详细日志**
   ```yaml
   logging:
     level:
       com.whiskerguard.general: DEBUG
   ```

2. **使用测试配置文件**
   - 创建application-test.yml
   - 配置测试专用的属性

3. **单独运行失败的测试**
   ```bash
   mvn test -Dtest=SpecificTestClass#specificTestMethod
   ```

## 持续集成

### CI/CD配置建议

1. **测试阶段**
   - 单元测试：快速反馈
   - 集成测试：验证组件协作
   - 性能测试：确保性能要求

2. **测试报告**
   - 生成覆盖率报告
   - 保存测试结果
   - 失败时发送通知

3. **测试环境**
   - 使用内存数据库加速测试
   - 并行执行测试提高效率
   - 隔离测试环境避免干扰

## 总结

这个测试套件为天眼查功能模块提供了全面的测试覆盖，确保：

- **功能正确性**: 验证所有业务逻辑
- **性能要求**: 确保缓存和查询性能
- **错误处理**: 验证异常情况的处理
- **集成稳定性**: 确保组件间的正确协作

通过运行这些测试，可以确信天眼查功能模块的质量和稳定性。
