/** 签名文档状态枚举 */
/** 表示签名文档的不同状态 */
enum SignatureDocumentStatus {
  /** 文档已创建但未发送签名 */
  CREATED,
  /** 文档已发送签名，等待签署 */
  PENDING,
  /** 文档已成功签署 */
  SIGNED,
  /** 文档签署被拒绝 */
  REJECTED,
  /** 签署过程中出现技术故障 */
  FAILED,
  /** 签署已过期 */
  EXPIRED,
  /** 签署已被取消 */
  CANCELLED
}

/** 签名服务提供商枚举 */
/** 支持的电子签名服务提供商 */
enum SignatureProvider {
  /** 法大大电子签名服务 */
  FADADA,
  /** e签宝电子签名服务 */
  ESIGN
}

/** 签名文档实体 */
/** 存储电子签名文档的相关信息 */
entity SignatureDocument {
  /** 主键ID */
  id Long required,
  /** 文档标题 */
  title String required,
  /** 文档描述 */
  description String,
  /** 文档URL地址 */
  documentUrl String required,
  /** 文档状态 */
  status SignatureDocumentStatus required,
  /** 签名服务提供商 */
  provider SignatureProvider required,
  /** 外部系统文档ID */
  externalId String,
  /** 事务ID */
  transactionId String,
  /** 用户ID */
  userId String required,
  /** 过期时间 */
  expireTime Instant,
  /** 签署时间 */
  signedTime Instant,
  /** 签署后文档URL */
  signedDocumentUrl String,
  /** 扩展元数据（JSON格式） */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}
dto * with mapstruct
service all with serviceImpl
paginate * with pagination
