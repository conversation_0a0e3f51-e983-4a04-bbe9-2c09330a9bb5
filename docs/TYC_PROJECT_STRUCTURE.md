# 天眼查 API 集成 - 项目文件清单

## 目录结构

```
tianyancha-integration-final/
├── README.md                           # 详细使用说明文档
├── src/main/java/com/whiskerguard/general/
│   ├── config/                         # 配置类
│   │   ├── TianyanchaConfiguration.java    # Spring 配置类
│   │   └── TianyanchaProperties.java       # 配置属性类
│   ├── domain/                         # JPA 实体类
│   │   ├── Company.java                    # 企业主实体
│   │   ├── CompanyCaseFiling.java          # 企业立案信息实体
│   │   ├── CompanyChangeRecord.java        # 企业变更记录实体
│   │   ├── CompanyContact.java             # 企业联系方式实体
│   │   ├── CompanyDishonestPerson.java     # 企业失信人实体
│   │   └── CompanyRisk.java                # 企业风险信息实体
│   ├── repository/                     # 数据访问层
│   │   ├── CompanyCaseFilingRepository.java
│   │   ├── CompanyChangeRecordRepository.java
│   │   ├── CompanyContactRepository.java
│   │   ├── CompanyDishonestPersonRepository.java
│   │   ├── CompanyRepository.java
│   │   └── CompanyRiskRepository.java
│   ├── service/                        # 业务逻辑层
│   │   ├── client/                     # 外部 API 客户端
│   │   │   └── TianyanchaApiClient.java    # 天眼查 API 客户端
│   │   ├── dto/                        # 数据传输对象
│   │   │   ├── tianyancha/             # 天眼查 API 响应 DTO
│   │   │   │   ├── TianyanchaBaseResponseDTO.java
│   │   │   │   ├── TianyanchaCompanyBasicInfoResponseDTO.java
│   │   │   │   └── TianyanchaCompanyVerificationResponseDTO.java
│   │   │   ├── CompanyDTO.java             # 企业信息 DTO
│   │   │   └── CompanyVerificationResultDTO.java  # 验证结果 DTO
│   │   ├── exception/                  # 异常类
│   │   │   └── TianyanchaApiException.java # 天眼查 API 异常
│   │   ├── mapper/                     # 对象映射器
│   │   │   └── CompanyMapper.java          # 企业信息映射器
│   │   ├── CompanyService.java             # 企业信息服务
│   │   └── TianyanchaQueryService.java     # 天眼查查询服务
│   └── web/rest/                       # REST 控制器
│       └── TianyanchaResource.java         # 天眼查 API 控制器
└── src/main/resources/config/liquibase/changelog/  # 数据库迁移文件
    ├── 20250610000001_added_entity_Company.xml
    ├── 20250610000002_added_entity_CompanyContact.xml
    ├── 20250610000003_added_entity_CompanyRisk.xml
    ├── 20250610000004_added_entity_CompanyChangeRecord.xml
    ├── 20250610000005_added_entity_CompanyDishonestPerson.xml
    └── 20250610000006_added_entity_CompanyCaseFiling.xml
```

## 文件说明

### 配置文件

- **TianyanchaProperties.java**: 天眼查 API 配置属性类，包含 API Token、超时设置、缓存配置等
- **TianyanchaConfiguration.java**: Spring 配置类，提供 RestTemplate Bean 等

### 实体类 (Domain)

- **Company.java**: 核心企业实体，存储企业基本信息
- **CompanyContact.java**: 企业联系方式实体
- **CompanyRisk.java**: 企业风险信息实体
- **CompanyChangeRecord.java**: 企业变更记录实体
- **CompanyDishonestPerson.java**: 企业失信人记录实体
- **CompanyCaseFiling.java**: 企业立案信息实体

### 数据访问层 (Repository)

- 各实体对应的 Spring Data JPA Repository 接口
- 提供基础 CRUD 操作和自定义查询方法

### 服务层 (Service)

- **TianyanchaApiClient.java**: 天眼查 API HTTP 客户端，处理 API 调用、重试、错误处理
- **CompanyService.java**: 企业信息管理服务，处理本地数据库操作
- **TianyanchaQueryService.java**: 天眼查查询服务，协调 API 调用和本地缓存
- **CompanyMapper.java**: MapStruct 映射器，处理实体和 DTO 之间的转换

### DTO 类

- **CompanyDTO.java**: 企业信息传输对象
- **CompanyVerificationResultDTO.java**: 三要素验证结果 DTO
- **tianyancha/** 目录下的类: 天眼查 API 响应结构映射

### 控制器 (Web/REST)

- **TianyanchaResource.java**: REST API 控制器，提供企业信息查询接口

### 数据库迁移文件

- Liquibase 迁移文件，用于创建数据库表结构和索引

## 核心功能

1. **企业信息查询**: 支持通过多种标识符查询企业基本信息
2. **智能缓存**: 本地数据库缓存，减少 API 调用成本
3. **三要素验证**: 验证企业名称、统一社会信用代码、法人姓名
4. **多接口支持**: 集成天眼查 9 个主要 API 接口
5. **错误处理**: 完善的异常处理和重试机制
6. **RESTful API**: 标准的 REST 接口设计

## 技术特点

- **JHipster 兼容**: 完全遵循 JHipster 项目结构和最佳实践
- **Spring Boot**: 基于 Spring Boot 框架
- **JPA/Hibernate**: 使用 JPA 进行数据持久化
- **Liquibase**: 数据库版本控制和迁移
- **MapStruct**: 高性能对象映射
- **缓存策略**: 智能缓存机制，可配置过期时间

## 部署要求

- Java 11+
- Spring Boot 2.7+
- MySQL/PostgreSQL 数据库
- 天眼查开放平台 API Token

请参考 README.md 文件获取详细的安装和使用说明。
