# 通知模块使用指南

## 概述

通知模块是 WhiskerGuard 通用微服务的核心功能之一，为整个系统的23个微服务提供统一的通知发送能力。支持多种通知类型、多渠道发送、模板管理、用户偏好设置等企业级功能。

## 功能特性

### 🎯 通知类型

- **系统通知**: 系统公告、维护通知、租户到期等
- **任务通知**: 审批流程、任务完成、超时提醒等
- **用户通知**: 登录安全、密码变更、操作确认等
- **业务通知**: 合规告警、培训提醒、合同到期等

### 📱 支持渠道

- **邮件通知**: 基于 Spring Mail
- **短信通知**: 支持腾讯云和阿里云
- **APP推送**: 基于极光推送
- **站内消息**: 系统内消息通知

### ⚡ 高级功能

- **批量发送**: 支持大批量用户通知
- **定时发送**: 支持延时和定时发送
- **优先级管理**: 紧急、高、普通、低四个级别
- **用户偏好**: 用户可自定义接收渠道和免打扰时间
- **失败重试**: 智能重试机制
- **模板管理**: 支持动态模板和多语言

## 快速开始

### 1. 基本配置

在 `application.yml` 中配置通知模块：

```yaml
application:
  notification:
    center:
      enabled: true
      async-enabled: true
    template:
      cache-enabled: true
      default-language: 'zh-CN'
    preference:
      default-channels: ['EMAIL', 'PUSH']
      quiet-hours-enabled: true
```

### 2. 依赖注入

在您的服务类中注入通知服务：

```java
@Service
public class YourBusinessService {

  private final NotificationHelper notificationHelper;

  public YourBusinessService(NotificationHelper notificationHelper) {
    this.notificationHelper = notificationHelper;
  }
}

```

## 使用示例

### 1. 发送系统通知

```java
// 发送系统公告
NotificationRecordDTO result = notificationHelper.sendSystemAnnouncement(
  "系统升级通知",
  "系统将于今晚22:00进行升级维护，预计2小时完成。",
  NotificationScope.GLOBAL,
  null // 全局通知不需要指定目标ID
);

// 发送租户到期提醒
NotificationRecordDTO result = notificationHelper.sendTenantExpirationNotice(
  tenantId,
  "ABC公司",
  7, // 7天后到期
  "https://example.com/renew"
);

```

### 2. 发送任务通知

```java
// 发送审批待处理通知
NotificationRecordDTO result = notificationHelper.sendApprovalPendingNotification(
  "TASK_001",
  "合同审批",
  "张三",
  Arrays.asList(100L, 101L) // 审批人ID列表
);

// 发送任务完成通知
NotificationRecordDTO result = notificationHelper.sendTaskCompletionNotification(
  "TASK_001",
  "数据导入任务",
  "李四",
  "成功导入1000条记录",
  200L // 任务创建者ID
);

```

### 3. 发送用户通知

```java
// 发送密码修改通知
NotificationRecordDTO result = notificationHelper.sendPasswordChangeNotification(userId, "*************");

// 发送登录异常通知
NotificationRecordDTO result = notificationHelper.sendLoginAnomalyNotification(userId, "203.0.113.1", "北京市");

```

### 4. 发送业务通知

```java
// 发送培训提醒（批量）
String batchId = notificationHelper.sendTrainingReminderNotification(
  Arrays.asList(1L, 2L, 3L, 4L, 5L),
  "安全合规培训",
  Instant.now().plusSeconds(1800) // 30分钟后开始
);

// 发送合同到期提醒
NotificationRecordDTO result = notificationHelper.sendContractExpiryNotification(
  userId,
  "服务合同-2024001",
  Instant.now().plusDays(30),
  30 // 30天后到期
);

```

## REST API 调用

### 1. 发送单个通知

```bash
POST /api/notification-center/send
Content-Type: application/json

{
    "category": "SYSTEM",
    "subType": "SYSTEM_ANNOUNCEMENT",
    "scope": "TENANT",
    "title": "系统公告",
    "content": "系统将进行维护",
    "recipientType": "ALL",
    "channels": ["EMAIL", "PUSH"],
    "priority": "HIGH"
}
```

### 2. 批量发送通知

```bash
POST /api/notification-center/batch-send
Content-Type: application/json

{
    "category": "BUSINESS",
    "subType": "TRAINING_REMINDER",
    "title": "培训提醒",
    "content": "您有一个培训即将开始",
    "recipientIds": [1, 2, 3, 4, 5],
    "channels": ["EMAIL", "PUSH"],
    "priority": "NORMAL",
    "batchSize": 2
}
```

### 3. 发送用户通知

```bash
POST /api/notification-center/user
Content-Type: application/json

{
    "userId": 12345,
    "subType": "PASSWORD_CHANGED",
    "title": "密码修改通知",
    "content": "您的密码已成功修改",
    "channels": ["EMAIL", "SMS"],
    "priority": "HIGH"
}
```

## 通知模板

### 1. 创建通知模板

```bash
POST /api/notification-templates
Content-Type: application/json

{
    "code": "PASSWORD_CHANGE_TEMPLATE",
    "name": "密码修改通知模板",
    "category": "USER",
    "subType": "PASSWORD_CHANGED",
    "titleTemplate": "密码修改通知",
    "contentTemplate": "尊敬的用户，您的密码已于 {{changeTime}} 成功修改，操作IP：{{ipAddress}}。",
    "smsTemplate": "您的密码已修改，操作IP：{{ipAddress}}，如非本人操作请联系客服。",
    "emailTemplate": "<p>尊敬的用户，</p><p>您的密码已于 {{changeTime}} 成功修改。</p><p>操作IP：{{ipAddress}}</p>",
    "enabled": true
}
```

### 2. 使用模板发送通知

```java
Map<String, Object> templateParams = new HashMap<>();
templateParams.put("changeTime", "2025-06-23 14:30:00");
templateParams.put("ipAddress", "*************");

UserNotificationRequestDTO request = new UserNotificationRequestDTO();
request.setUserId(12345L);
request.setSubType(NotificationSubType.PASSWORD_CHANGED);
request.setTemplateId(templateId);
request.setTemplateParams(templateParams);
request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.SMS));
request.setPriority(NotificationPriority.HIGH);

NotificationRecordDTO result = notificationCenterService.sendUserNotification(request);
```

## 用户偏好设置

### 1. 设置用户通知偏好

```bash
POST /api/user-notification-preferences
Content-Type: application/json

{
    "userId": 12345,
    "category": "BUSINESS",
    "subType": "TRAINING_REMINDER",
    "enabledChannels": "[\"EMAIL\", \"PUSH\"]",
    "quietHoursStart": "22:00",
    "quietHoursEnd": "08:00",
    "enabled": true
}
```

### 2. 查询用户通知偏好

```bash
GET /api/user-notification-preferences?userId.equals=12345
```

## 监控和统计

### 1. 查询通知记录

```bash
GET /api/notification-records?status.equals=SENT&page=0&size=20
```

### 2. 获取批量通知状态

```bash
GET /api/notification-center/batch/{batchId}/status
```

### 3. 标记通知为已读

```bash
PUT /api/notification-center/{notificationId}/read?userId=12345
```

## 最佳实践

### 1. 通知优先级使用建议

- **URGENT**: 系统故障、安全告警
- **HIGH**: 审批任务、密码修改、合同到期
- **NORMAL**: 一般业务通知、培训提醒
- **LOW**: 系统公告、操作确认

### 2. 渠道选择建议

- **安全相关**: EMAIL + SMS
- **任务相关**: EMAIL + PUSH
- **系统公告**: EMAIL + 站内消息
- **用户操作**: 站内消息

### 3. 批量通知优化

- 合理设置批次大小（建议100-500）
- 使用异步发送避免阻塞
- 监控发送状态和失败重试

### 4. 模板管理

- 使用有意义的模板编码
- 支持多语言模板
- 定期清理无用模板

## 故障排查

### 1. 通知发送失败

- 检查通知渠道配置
- 验证接收者信息
- 查看错误日志

### 2. 模板渲染失败

- 检查模板参数是否完整
- 验证模板语法
- 确认模板是否启用

### 3. 用户偏好不生效

- 检查用户偏好配置
- 验证免打扰时间设置
- 确认偏好是否启用

通过以上指南，您可以快速集成和使用通知模块的各项功能。如有问题，请参考API文档或联系开发团队。
