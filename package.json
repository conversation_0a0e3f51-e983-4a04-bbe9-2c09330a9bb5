{"name": "whiskerguard-general-service", "version": "0.0.0", "private": true, "description": "Description for whiskerguardGeneralService", "license": "UNLICENSED", "scripts": {"app:start": "./mvnw -ntp --batch-mode", "app:up": "docker compose -f src/main/docker/app.yml up --wait", "backend:build-cache": "./mvnw dependency:go-offline -ntp", "backend:debug": "./mvnw -Dspring-boot.run.jvmArguments=\"-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000\"", "backend:doc:test": "./mvnw -ntp javadoc:javadoc --batch-mode", "backend:info": "./mvnw --version", "backend:nohttp:test": "./mvnw -ntp checkstyle:check --batch-mode", "backend:start": "./mvnw -ntp --batch-mode", "backend:unit:test": "./mvnw -ntp verify --batch-mode -Dlogging.level.ROOT=OFF -Dlogging.level.tech.jhipster=OFF -Dlogging.level.com.whiskerguard.general=OFF -Dlogging.level.org.springframework=OFF -Dlogging.level.org.springframework.web=OFF -Dlogging.level.org.springframework.security=OFF", "ci:backend:test": "npm run backend:info && npm run backend:doc:test && npm run backend:nohttp:test && npm run backend:unit:test -- -P$npm_package_config_default_environment", "ci:e2e:package": "npm run java:$npm_package_config_packaging:$npm_package_config_default_environment -- -Pe2e -Denforcer.skip=true", "ci:e2e:prepare": "npm run ci:e2e:prepare:docker", "ci:e2e:prepare:docker": "npm run services:up --if-present && docker ps -a", "preci:e2e:server:start": "npm run services:db:await --if-present && npm run services:others:await --if-present", "ci:e2e:server:start": "java -jar target/e2e.$npm_package_config_packaging --spring.profiles.active=e2e,$npm_package_config_default_environment -Dlogging.level.ROOT=OFF -Dlogging.level.tech.jhipster=OFF -Dlogging.level.com.whiskerguard.general=OFF -Dlogging.level.org.springframework=OFF -Dlogging.level.org.springframework.web=OFF -Dlogging.level.org.springframework.security=OFF --logging.level.org.springframework.web=ERROR", "ci:e2e:teardown": "npm run ci:e2e:teardown:docker --if-present", "ci:e2e:teardown:docker": "docker compose -f src/main/docker/services.yml down -v && docker ps -a", "ci:server:await": "echo \"Waiting for server at port $npm_package_config_backend_port to start\" && wait-on -t 180000 http-get://127.0.0.1:undefined/services/whiskerguardgeneralservice/management/health/readiness && echo \"Server at port $npm_package_config_backend_port started\"", "docker:consul:down": "docker compose -f src/main/docker/consul.yml down -v", "docker:consul:up": "docker compose -f src/main/docker/consul.yml up --wait", "docker:db:down": "docker compose -f src/main/docker/mysql.yml down -v", "docker:db:up": "docker compose -f src/main/docker/mysql.yml up --wait", "docker:redis:down": "docker compose -f src/main/docker/redis.yml down -v", "docker:redis:up": "docker compose -f src/main/docker/redis.yml up --wait", "java:docker": "./mvnw -ntp verify -DskipTests -Pprod jib:dockerBuild", "java:docker:arm64": "npm run java:docker -- -Djib-maven-plugin.architecture=arm64", "java:docker:dev": "npm run java:docker -- -Pdev,webapp", "java:docker:prod": "npm run java:docker -- -P<PERSON>rod", "java:jar": "./mvnw -ntp verify -DskipTests --batch-mode", "java:jar:dev": "npm run java:jar -- -Pdev,webapp", "java:jar:prod": "npm run java:jar -- -P<PERSON>rod", "java:war": "./mvnw -ntp verify -DskipTests --batch-mode -Pwar", "java:war:dev": "npm run java:war -- -Pdev,webapp", "java:war:prod": "npm run java:war -- -P<PERSON>rod", "prepare": "husky", "prettier:check": "prettier --check \"{,src/**/,.blueprint/**/}*.{md,json,yml,js,cjs,mjs,ts,cts,mts,java}\"", "prettier:format": "prettier --write \"{,src/**/,.blueprint/**/}*.{md,json,yml,js,cjs,mjs,ts,cts,mts,java}\"", "services:up": "docker compose -f src/main/docker/services.yml up --wait"}, "config": {"backend_port": "8187", "default_environment": "prod", "packaging": "jar"}, "devDependencies": {"generator-jhipster": "8.10.0", "husky": "9.1.7", "lint-staged": "15.5.0", "prettier": "3.5.3", "prettier-plugin-java": "2.6.7", "prettier-plugin-packagejson": "2.5.10"}, "engines": {"node": ">=22.14.0"}, "cacheDirectories": ["node_modules"]}