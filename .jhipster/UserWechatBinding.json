{"annotations": {"changelogDate": "20250624134316"}, "applications": "*", "clientRootFolder": "whiskerguardGeneralService", "databaseType": "sql", "documentation": "用户微信绑定实体JDL定义", "dto": "mapstruct", "fields": [{"documentation": "租户ID", "fieldName": "tenantId", "fieldType": "<PERSON>"}, {"documentation": "用户ID（员工ID）", "fieldName": "employeeId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "微信OpenID", "fieldName": "openId", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "微信UnionID", "fieldName": "unionId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "扩展信息（JSON格式）", "fieldName": "metadata", "fieldType": "TextBlob"}, {"documentation": "版本号（乐观锁）", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建人", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新人", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "是否删除", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardGeneralService", "name": "UserWechatBinding", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}