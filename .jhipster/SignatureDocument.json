{"annotations": {"changelogDate": "20250523091435"}, "applications": "*", "clientRootFolder": "whiskerguardGeneralService", "databaseType": "sql", "documentation": "存储电子签名文档的相关信息", "dto": "mapstruct", "fields": [{"documentation": "主键ID", "fieldName": "id", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "文档标题", "fieldName": "title", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "文档描述", "fieldName": "description", "fieldType": "String"}, {"documentation": "文档URL地址", "fieldName": "documentUrl", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "文档状态", "fieldName": "status", "fieldType": "SignatureDocumentStatus", "fieldTypeDocumentation": "表示签名文档的不同状态", "fieldValidateRules": ["required"], "fieldValues": "CREATED,PENDING,SIGNED,REJECTED,FAILED,EXPIRED,CANCELLED", "fieldValuesJavadocs": {"CANCELLED": "签署已被取消", "CREATED": "文档已创建但未发送签名", "EXPIRED": "签署已过期", "FAILED": "签署过程中出现技术故障", "PENDING": "文档已发送签名，等待签署", "REJECTED": "文档签署被拒绝", "SIGNED": "文档已成功签署"}}, {"documentation": "签名服务提供商", "fieldName": "provider", "fieldType": "SignatureProvider", "fieldTypeDocumentation": "支持的电子签名服务提供商", "fieldValidateRules": ["required"], "fieldValues": "FADADA,ESIGN", "fieldValuesJavadocs": {"ESIGN": "e签宝电子签名服务", "FADADA": "法大大电子签名服务"}}, {"documentation": "外部系统文档ID", "fieldName": "externalId", "fieldType": "String"}, {"documentation": "事务ID", "fieldName": "transactionId", "fieldType": "String"}, {"documentation": "用户ID", "fieldName": "userId", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "过期时间", "fieldName": "expireTime", "fieldType": "Instant"}, {"documentation": "签署时间", "fieldName": "signedTime", "fieldType": "Instant"}, {"documentation": "签署后文档URL", "fieldName": "signedDocumentUrl", "fieldType": "String"}, {"documentation": "扩展元数据（JSON格式）", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardGeneralService", "name": "SignatureDocument", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}