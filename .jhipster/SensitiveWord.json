{"annotations": {"changelogDate": "20250609084418"}, "applications": "*", "clientRootFolder": "whiskerguardGeneralService", "databaseType": "sql", "documentation": "存储平台级或租户级的敏感词及策略信息", "dto": "mapstruct", "fields": [{"documentation": "主键ID", "fieldName": "id", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "租户ID（0 = 平台级）", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "敏感词条", "fieldName": "term", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "语言", "fieldName": "lang", "fieldType": "LanguageType", "fieldTypeDocumentation": "支持的语言", "fieldValidateRules": ["required"], "fieldValues": "ZH,EN,ES,JA,KO,FR,DE,RU,OTHER"}, {"documentation": "分类", "fieldName": "category", "fieldType": "SensitiveCategory", "fieldTypeDocumentation": "敏感词分类", "fieldValidateRules": ["required"], "fieldValues": "POLIT<PERSON>AL,VI<PERSON><PERSON><PERSON>,PORNOGRAPHY,<PERSON><PERSON><PERSON><PERSON>,FRAUD,PII,ADVERTISING,DRUGS,OTHER", "fieldValuesJavadocs": {"ADVERTISING": "广告法违规", "DRUGS": "毒品", "FRAUD": "欺诈/诈骗", "GAMBLING": "赌博", "OTHER": "其他", "PII": "个人隐私/PII", "POLITICAL": "政治敏感", "PORNOGRAPHY": "色情", "VIOLENCE": "暴恐"}}, {"documentation": "严重级别", "fieldName": "severity", "fieldType": "SeverityType", "fieldTypeDocumentation": "敏感词严重级别", "fieldValidateRules": ["required"], "fieldValues": "BLOCK,REVIEW,REPLACE", "fieldValuesJavadocs": {"BLOCK": "阻断：命中即拒绝请求", "REPLACE": "替换：命中后可自动脱敏替换", "REVIEW": "待复核：命中后进入人工审核队列"}}, {"documentation": "有效开始时间", "fieldName": "validFrom", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "有效结束时间", "fieldName": "validTo", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "备注", "fieldName": "notes", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardGeneralService", "name": "SensitiveWord", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceClass"}