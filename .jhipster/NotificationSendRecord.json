{"annotations": {"changelogDate": "20250623112937"}, "applications": "*", "clientRootFolder": "whiskerguardGeneralService", "databaseType": "sql", "documentation": "存储每个接收者的通知发送详细记录", "dto": "mapstruct", "fields": [{"documentation": "主键ID", "fieldName": "id", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "租户ID（0 = 平台级）", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "接收者ID", "fieldName": "recipientId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "接收者类型", "fieldName": "recipientType", "fieldType": "RecipientType", "fieldTypeDocumentation": "接收者类型枚举", "fieldValidateRules": ["required"], "fieldValues": "USER,ROLE,DEPARTMENT,ALL", "fieldValuesJavadocs": {"ALL": "全部", "DEPARTMENT": "部门", "ROLE": "角色", "USER": "用户"}}, {"documentation": "发送渠道", "fieldName": "channel", "fieldType": "NotificationType", "fieldTypeDocumentation": "通知类型枚举", "fieldValidateRules": ["required"], "fieldValues": "SMS,EMAIL,PUSH,WECHAT", "fieldValuesJavadocs": {"EMAIL": "* 邮件通知", "PUSH": "* APP推送通知", "SMS": "* 短信通知", "WECHAT": "* 微信公众号推送"}}, {"documentation": "发送状态", "fieldName": "status", "fieldType": "SendStatus", "fieldTypeDocumentation": "发送状态枚举", "fieldValidateRules": ["required"], "fieldValues": "PENDING,SENT,DELIVERED,READ,FAILED", "fieldValuesJavadocs": {"DELIVERED": "已送达", "FAILED": "发送失败", "PENDING": "待发送", "READ": "已读", "SENT": "已发送"}}, {"documentation": "发送时间", "fieldName": "sentTime", "fieldType": "Instant"}, {"documentation": "阅读时间", "fieldName": "readTime", "fieldType": "Instant"}, {"documentation": "错误信息", "fieldName": "errorMessage", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"documentation": "第三方服务返回的ID", "fieldName": "externalId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardGeneralService", "name": "NotificationSendRecord", "pagination": "pagination", "relationships": [{"otherEntityName": "notificationRecord", "otherEntityRelationshipName": "sendRecords", "relationshipName": "notification", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}