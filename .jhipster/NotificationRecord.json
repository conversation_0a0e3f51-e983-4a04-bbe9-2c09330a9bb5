{"annotations": {"changelogDate": "20250623112934"}, "applications": "*", "clientRootFolder": "whiskerguardGeneralService", "databaseType": "sql", "documentation": "存储通知发送记录和状态信息", "dto": "mapstruct", "fields": [{"documentation": "主键ID", "fieldName": "id", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "租户ID（0 = 平台级）", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "通知分类", "fieldName": "category", "fieldType": "NotificationCategory", "fieldTypeDocumentation": "通知分类枚举", "fieldValidateRules": ["required"], "fieldValues": "SYSTEM,TASK,USER,BUSINESS,SECURITY,MAINTENANCE", "fieldValuesJavadocs": {"BUSINESS": "业务通知", "MAINTENANCE": "维护通知", "SECURITY": "安全通知", "SYSTEM": "系统通知", "TASK": "任务通知", "USER": "用户通知"}}, {"documentation": "通知子类型", "fieldName": "subType", "fieldType": "NotificationSubType", "fieldTypeDocumentation": "通知子类型枚举", "fieldValidateRules": ["required"], "fieldValues": "SYSTEM_ANNOUNCEMENT,SY<PERSON><PERSON>_MAINTENANCE,SY<PERSON><PERSON>_UPGRADE,TENANT_EXPIRED,LICENSE_EXPIRED,TASK_COMPLETED,TASK_FAILED,TASK_TIMEOUT,AP<PERSON><PERSON>VA<PERSON>_PENDING,APPROVAL_APPROVED,<PERSON><PERSON>OVA<PERSON>_REJECTED,AP<PERSON><PERSON>VAL_TIMEOUT,AP<PERSON>OVAL_WITHDRAWN,USER_LOGIN,USER_OPERATION,PASSWORD_CHANGED,PASSWORD_EXPIRING,ACCOUNT_LOCKED,PROFILE_UPDATED,COMPLIANCE_ALERT,TRAINING_REMINDER,TRAINING_COMPLETED,EXAM_SCHEDULED,EXAM_RESULT_PUBLISHED,CONTRACT_EXPIRING,CONTRACT_APPROVAL_PENDING,CONTRACT_RENEWED,CONTRACT_AMENDED,VIOLATION_REPORTED,VI<PERSON><PERSON><PERSON>_PROCESSING,VIOLATION_RESOLVED,OR<PERSON>R_STATUS_CHANGED,ORDER_PAYMENT_FAILED,SERVICE_RENEWAL_REMINDER,ORG_STRUCTURE_CHANGED,PERSONNEL_CHANGE,REGULATORY_UPDATE,COMPLIANCE_DEADLINE,REGULATORY_IMPACT_ANALYSIS,LICENSE_EXPIRING,LICENSE_USAGE_ALERT,LICENSE_RENEWED,RISK_ALERT,RISK_LEVEL_CHANGED,RISK_MITIGATED,AI_ANALYSIS_COMPLETED,AI_MODEL_UPDATED,AI_ANOMALY_DETECTED,DASHBOARD_ALERT,DATA_ANOMALY_DETECTED,REPORT_GENERATED,THRESHOLD_EXCEEDED,ESG_REMINDER,ESG_ASSESSMENT_DUE,ESG_METRICS_UPDATED,ESG_REPORT_PUBLISHED,FINANCIAL_ALERT,FINANCIAL_AUDIT_DUE,FINANCIAL_COMPLIANCE_CHECK,FINANCIAL_ANOMALY,USER_ACCOUNT_OPERATION,SYSTEM_PERFORMANCE_ALERT,COMPLIANCE_LIST_UPDATED,COMPLIANCE_CHECK_REMINDER,COMPLIANCE_LIST_EXPIRING", "fieldValuesJavadocs": {"ACCOUNT_LOCKED": "账户锁定", "AI_ANALYSIS_COMPLETED": "AI分析完成", "AI_ANOMALY_DETECTED": "AI异常检测", "AI_MODEL_UPDATED": "AI模型更新", "APPROVAL_APPROVED": "审批通过", "APPROVAL_PENDING": "待审批", "APPROVAL_REJECTED": "审批拒绝", "APPROVAL_TIMEOUT": "审批超时", "APPROVAL_WITHDRAWN": "审批撤回", "COMPLIANCE_ALERT": "合规告警", "COMPLIANCE_CHECK_REMINDER": "合规检查提醒", "COMPLIANCE_DEADLINE": "合规截止日期", "COMPLIANCE_LIST_EXPIRING": "合规清单即将到期", "COMPLIANCE_LIST_UPDATED": "合规清单更新", "CONTRACT_AMENDED": "合同修订", "CONTRACT_APPROVAL_PENDING": "合同审批待处理", "CONTRACT_EXPIRING": "合同到期", "CONTRACT_RENEWED": "合同续签", "DASHBOARD_ALERT": "仪表板告警", "DATA_ANOMALY_DETECTED": "数据异常检测", "ESG_ASSESSMENT_DUE": "ESG评估到期", "ESG_METRICS_UPDATED": "ESG指标更新", "ESG_REMINDER": "ESG合规提醒", "ESG_REPORT_PUBLISHED": "ESG报告发布", "EXAM_RESULT_PUBLISHED": "考试成绩发布", "EXAM_SCHEDULED": "考试安排", "FINANCIAL_ALERT": "财务合规告警", "FINANCIAL_ANOMALY": "财务数据异常", "FINANCIAL_AUDIT_DUE": "财务审计到期", "FINANCIAL_COMPLIANCE_CHECK": "财务合规检查", "LICENSE_EXPIRED": "许可证到期", "LICENSE_EXPIRING": "许可证即将到期", "LICENSE_RENEWED": "许可证续期", "LICENSE_USAGE_ALERT": "许可证使用量告警", "ORDER_PAYMENT_FAILED": "订单支付失败", "ORDER_STATUS_CHANGED": "订单状态变更", "ORG_STRUCTURE_CHANGED": "组织架构变更", "PASSWORD_CHANGED": "密码修改", "PASSWORD_EXPIRING": "密码即将过期", "PERSONNEL_CHANGE": "人员变动", "PROFILE_UPDATED": "资料更新", "REGULATORY_IMPACT_ANALYSIS": "法规影响分析", "REGULATORY_UPDATE": "法规更新", "REPORT_GENERATED": "报表生成", "RISK_ALERT": "风险告警", "RISK_LEVEL_CHANGED": "风险等级变更", "RISK_MITIGATED": "风险已缓解", "SERVICE_RENEWAL_REMINDER": "服务续费提醒", "SYSTEM_ANNOUNCEMENT": "系统公告", "SYSTEM_MAINTENANCE": "系统维护", "SYSTEM_PERFORMANCE_ALERT": "系统性能告警", "SYSTEM_UPGRADE": "系统升级", "TASK_COMPLETED": "任务完成", "TASK_FAILED": "任务失败", "TASK_TIMEOUT": "任务超时", "TENANT_EXPIRED": "租户到期", "THRESHOLD_EXCEEDED": "阈值超限", "TRAINING_COMPLETED": "培训完成", "TRAINING_REMINDER": "培训提醒", "USER_ACCOUNT_OPERATION": "用户账户操作", "USER_LOGIN": "用户登录", "USER_OPERATION": "用户操作", "VIOLATION_PROCESSING": "违规处理中", "VIOLATION_REPORTED": "违规举报", "VIOLATION_RESOLVED": "违规已解决"}}, {"documentation": "通知范围", "fieldName": "scope", "fieldType": "NotificationScope", "fieldTypeDocumentation": "通知范围枚举", "fieldValidateRules": ["required"], "fieldValues": "GLOBAL,TENANT,DEPARTMENT,ROLE,USER", "fieldValuesJavadocs": {"DEPARTMENT": "部门级通知", "GLOBAL": "全局通知（所有租户）", "ROLE": "角色级通知", "TENANT": "租户级通知", "USER": "用户级通知"}}, {"documentation": "通知标题", "fieldName": "title", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "200"}, {"documentation": "通知内容", "fieldName": "content", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "2000"}, {"documentation": "接收者类型", "fieldName": "recipientType", "fieldType": "RecipientType", "fieldTypeDocumentation": "接收者类型枚举", "fieldValidateRules": ["required"], "fieldValues": "USER,ROLE,DEPARTMENT,ALL", "fieldValuesJavadocs": {"ALL": "全部", "DEPARTMENT": "部门", "ROLE": "角色", "USER": "用户"}}, {"documentation": "接收者ID列表(JSON格式)", "fieldName": "recipientIds", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "2000"}, {"documentation": "发送渠道列表(JSON格式)", "fieldName": "channels", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "优先级", "fieldName": "priority", "fieldType": "NotificationPriority", "fieldTypeDocumentation": "通知优先级枚举", "fieldValidateRules": ["required"], "fieldValues": "LOW,NORMAL,HIGH,URGENT", "fieldValuesJavadocs": {"HIGH": "高优先级", "LOW": "低优先级", "NORMAL": "普通优先级", "URGENT": "紧急"}}, {"documentation": "状态", "fieldName": "status", "fieldType": "NotificationStatus", "fieldTypeDocumentation": "通知状态枚举", "fieldValidateRules": ["required"], "fieldValues": "DRAFT,SCHEDULED,SENDING,SENT,FAILED,CANCELLED", "fieldValuesJavadocs": {"CANCELLED": "已取消", "DRAFT": "草稿", "FAILED": "发送失败", "SCHEDULED": "已调度", "SENDING": "发送中", "SENT": "已发送"}}, {"documentation": "计划发送时间", "fieldName": "scheduledTime", "fieldType": "Instant"}, {"documentation": "实际发送时间", "fieldName": "sentTime", "fieldType": "Instant"}, {"documentation": "关联业务ID", "fieldName": "businessId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"documentation": "业务类型", "fieldName": "businessType", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "模板参数(JSON格式)", "fieldName": "templateParams", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "2000"}, {"documentation": "重试次数", "fieldName": "retryCount", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "10", "fieldValidateRulesMin": "0"}, {"documentation": "错误信息", "fieldName": "errorMessage", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardGeneralService", "name": "NotificationRecord", "pagination": "pagination", "relationships": [{"otherEntityName": "notificationTemplate", "relationshipName": "template", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "notificationSendRecord", "otherEntityRelationshipName": "notification", "relationshipName": "sendRecords", "relationshipSide": "right", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}