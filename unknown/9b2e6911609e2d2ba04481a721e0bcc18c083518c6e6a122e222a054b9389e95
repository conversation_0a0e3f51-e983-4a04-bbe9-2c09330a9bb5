package com.whiskerguard.general.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.config.TianyanchaProperties;
import com.whiskerguard.general.service.dto.TianyanchaBaseResponseDTO;
import com.whiskerguard.general.service.dto.TianyanchaCompanyBasicInfoResponseDTO;
import com.whiskerguard.general.service.dto.TianyanchaCompanyVerificationResponseDTO;
import com.whiskerguard.general.service.exception.TianyanchaApiException;
import java.net.URI;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * Client for interacting with Tianyancha API.
 * Handles HTTP requests, authentication, error handling, and retry logic.
 *
 * 天眼查API客户端。
 * 处理HTTP请求、认证、错误处理和重试逻辑。
 */
@Component
public class TianyanchaApiClient {

    private static final Logger log = LoggerFactory.getLogger(TianyanchaApiClient.class);

    private final TianyanchaProperties tianyanchaProperties;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public TianyanchaApiClient(TianyanchaProperties tianyanchaProperties, RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.tianyanchaProperties = tianyanchaProperties;
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Retrieves company basic information from Tianyancha API.
     *
     * @param keyword Search keyword (company name, ID, registration number, or credit code)
     * @return Company basic information response
     * @throws TianyanchaApiException if API call fails
     *
     * 从天眼查API获取企业基本信息。
     *
     * @param keyword 搜索关键词（企业名称、ID、注册号或统一社会信用代码）
     * @return 企业基本信息响应
     * @throws TianyanchaApiException 如果API调用失败
     */
    public TianyanchaCompanyBasicInfoResponseDTO getCompanyBasicInfo(String keyword) {
        String endpoint = "baseinfoV2/2.0";
        URI uri = buildUri(endpoint, "keyword", keyword);

        return executeWithRetry(
            () -> {
                ResponseEntity<String> response = makeApiCall(uri);
                return parseResponse(response.getBody(), TianyanchaCompanyBasicInfoResponseDTO.class, endpoint);
            },
            endpoint
        );
    }

    /**
     * Verifies company three elements (name, credit code, legal person name).
     *
     * @param companyName Company full name
     * @param creditCode Unified social credit code
     * @param legalPersonName Legal person's name
     * @return Verification result
     * @throws TianyanchaApiException if API call fails
     *
     * 验证企业三要素（名称、统一社会信用代码、法人姓名）。
     *
     * @param companyName 企业全称
     * @param creditCode 统一社会信用代码
     * @param legalPersonName 法人姓名
     * @return 验证结果
     * @throws TianyanchaApiException 如果API调用失败
     */
    public TianyanchaCompanyVerificationResponseDTO verifyCompanyThreeElements(
        String companyName,
        String creditCode,
        String legalPersonName
    ) {
        String endpoint = "baseinfo/verify";
        URI uri = UriComponentsBuilder.fromHttpUrl(tianyanchaProperties.getBaseUrl() + endpoint)
            .queryParam("companyName", companyName)
            .queryParam("creditCode", creditCode)
            .queryParam("legalPersonName", legalPersonName)
            .build()
            .toUri();

        return executeWithRetry(
            () -> {
                ResponseEntity<String> response = makeApiCall(uri);
                return parseResponse(response.getBody(), TianyanchaCompanyVerificationResponseDTO.class, endpoint);
            },
            endpoint
        );
    }

    /**
     * Generic method to retrieve company information from various endpoints.
     * This can be used for risk, contact, change records, etc.
     *
     * @param endpoint API endpoint (e.g., "risk/company", "baseinfo/contact")
     * @param keyword Search keyword
     * @return Raw JSON response as string
     * @throws TianyanchaApiException if API call fails
     *
     * 通用方法，用于从各种端点获取公司信息。
     * 可用于风险信息、联系方式、变更记录等。
     *
     * @param endpoint API端点（如"risk/company"、"baseinfo/contact"）
     * @param keyword 搜索关键词
     * @return 原始JSON响应字符串
     * @throws TianyanchaApiException 如果API调用失败
     */
    public String getCompanyInfo(String endpoint, String keyword) {
        URI uri = buildUri(endpoint, "keyword", keyword);

        return executeWithRetry(
            () -> {
                ResponseEntity<String> response = makeApiCall(uri);
                // Validate that the response is successful
                TianyanchaBaseResponseDTO baseResponse = parseResponse(response.getBody(), TianyanchaBaseResponseDTO.class, endpoint);
                if (!baseResponse.isSuccess()) {
                    throw new TianyanchaApiException(
                        "API returned error: " + baseResponse.getReason(),
                        baseResponse.getErrorCode(),
                        endpoint
                    );
                }
                return response.getBody();
            },
            endpoint
        );
    }

    /**
     * Builds URI for API calls with query parameters.
     *
     * 构建带查询参数的API调用URI。
     */
    private URI buildUri(String endpoint, String paramName, String paramValue) {
        return UriComponentsBuilder.fromHttpUrl(tianyanchaProperties.getBaseUrl() + endpoint)
            .queryParam(paramName, paramValue)
            .build()
            .toUri();
    }

    /**
     * Makes the actual HTTP API call with proper headers.
     *
     * 使用适当的头信息进行实际的HTTP API调用。
     */
    private ResponseEntity<String> makeApiCall(URI uri) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", tianyanchaProperties.getApiToken());
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> entity = new HttpEntity<>(headers);

        log.debug("Making API call to: {}", uri);

        try {
            return restTemplate.exchange(uri, HttpMethod.GET, entity, String.class);
        } catch (HttpClientErrorException e) {
            log.error("Client error calling Tianyancha API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new TianyanchaApiException(
                "Client error: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(),
                e.getStatusCode().value(),
                uri.getPath(),
                e
            );
        } catch (HttpServerErrorException e) {
            log.error("Server error calling Tianyancha API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new TianyanchaApiException(
                "Server error: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(),
                e.getStatusCode().value(),
                uri.getPath(),
                e
            );
        } catch (ResourceAccessException e) {
            log.error("Network error calling Tianyancha API: {}", e.getMessage());
            throw new TianyanchaApiException("Network error: " + e.getMessage(), e);
        }
    }

    /**
     * Parses JSON response into specified DTO class.
     *
     * 将JSON响应解析为指定的DTO类。
     */
    private <T extends TianyanchaBaseResponseDTO> T parseResponse(String responseBody, Class<T> responseClass, String endpoint) {
        try {
            T response = objectMapper.readValue(responseBody, responseClass);

            if (!response.isSuccess()) {
                throw new TianyanchaApiException("API returned error: " + response.getReason(), response.getErrorCode(), endpoint);
            }

            return response;
        } catch (Exception e) {
            log.error("Error parsing API response: {}", e.getMessage());
            throw new TianyanchaApiException("Error parsing API response: " + e.getMessage(), e);
        }
    }

    /**
     * Executes API call with retry logic.
     *
     * 使用重试逻辑执行API调用。
     */
    private <T> T executeWithRetry(ApiCallSupplier<T> apiCall, String endpoint) {
        int attempts = 0;
        long delay = tianyanchaProperties.getRetryDelayMs();

        while (attempts < tianyanchaProperties.getMaxRetryAttempts()) {
            try {
                return apiCall.get();
            } catch (TianyanchaApiException e) {
                attempts++;

                // Don't retry for client errors (4xx) except for rate limiting (429)
                // 对于客户端错误（4xx）不重试，除了限流错误（429）
                if (e.getErrorCode() != null && e.getErrorCode() >= 400 && e.getErrorCode() < 500 && e.getErrorCode() != 429) {
                    throw e;
                }

                if (attempts >= tianyanchaProperties.getMaxRetryAttempts()) {
                    log.error("Max retry attempts ({}) exceeded for endpoint: {}", tianyanchaProperties.getMaxRetryAttempts(), endpoint);
                    throw e;
                }

                log.warn(
                    "API call failed (attempt {}/{}), retrying in {}ms: {}",
                    attempts,
                    tianyanchaProperties.getMaxRetryAttempts(),
                    delay,
                    e.getMessage()
                );

                try {
                    TimeUnit.MILLISECONDS.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new TianyanchaApiException("Interrupted during retry delay", ie);
                }

                // Exponential backoff - 指数退避策略
                delay *= 2;
            }
        }

        throw new TianyanchaApiException("Unexpected error in retry logic");
    }

    /**
     * Functional interface for API call suppliers.
     *
     * API调用提供者的函数式接口。
     */
    @FunctionalInterface
    private interface ApiCallSupplier<T> {
        T get() throws TianyanchaApiException;
    }
}
