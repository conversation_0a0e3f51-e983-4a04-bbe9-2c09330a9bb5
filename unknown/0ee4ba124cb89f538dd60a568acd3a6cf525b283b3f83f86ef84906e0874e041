package com.whiskerguard.general.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;

/**
 * DTO for Tianyancha Company Basic Information API response.
 * Maps to the response from /baseinfoV2/2.0 endpoint.
 *
 * 天眼查企业基本信息API响应的DTO。
 * 映射/baseinfoV2/2.0端点的响应。
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TianyanchaCompanyBasicInfoResponseDTO extends TianyanchaBaseResponseDTO {

    /**
     * The company basic information result object.
     *
     * 企业基本信息结果对象。
     */
    @JsonProperty("result")
    private CompanyBasicInfo result;

    public CompanyBasicInfo getResult() {
        return result;
    }

    public void setResult(CompanyBasicInfo result) {
        this.result = result;
    }

    /**
     * Inner class representing company basic information details.
     *
     * 表示企业基本信息详情的内部类。
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CompanyBasicInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * Tianyancha company ID.
         *
         * 天眼查企业ID。
         */
        @JsonProperty("id")
        private Long id;

        /**
         * Company name.
         *
         * 企业名称。
         */
        @JsonProperty("name")
        private String name;

        /**
         * Unified social credit code.
         *
         * 统一社会信用代码。
         */
        @JsonProperty("creditCode")
        private String creditCode;

        /**
         * Name of the legal representative.
         *
         * 法定代表人姓名。
         */
        @JsonProperty("legalPersonName")
        private String legalPersonName;

        /**
         * Registration status (e.g., "存续", "注销", "吊销").
         *
         * 登记状态（例如："存续", "注销", "吊销"）。
         */
        @JsonProperty("regStatus")
        private String regStatus;

        /**
         * Registered capital amount.
         *
         * 注册资本金额。
         */
        @JsonProperty("regCapital")
        private String regCapital;

        /**
         * Currency of registered capital.
         *
         * 注册资本币种。
         */
        @JsonProperty("regCapitalCurrency")
        private String regCapitalCurrency;

        /**
         * Establishment time in Unix timestamp.
         *
         * 成立时间的Unix时间戳。
         */
        @JsonProperty("estiblishTime")
        private Long estiblishTime;

        /**
         * Company organization type.
         *
         * 企业组织类型。
         */
        @JsonProperty("companyOrgType")
        private String companyOrgType;

        /**
         * Registration number.
         *
         * 工商注册号。
         */
        @JsonProperty("regNumber")
        private String regNumber;

        /**
         * Tax registration number.
         *
         * 税务登记号。
         */
        @JsonProperty("taxNumber")
        private String taxNumber;

        /**
         * Organization code.
         *
         * 组织机构代码。
         */
        @JsonProperty("orgNumber")
        private String orgNumber;

        /**
         * Industry category.
         *
         * 行业类别。
         */
        @JsonProperty("industry")
        private String industry;

        /**
         * Registered address.
         *
         * 注册地址。
         */
        @JsonProperty("regLocation")
        private String regLocation;

        /**
         * Business scope.
         *
         * 经营范围。
         */
        @JsonProperty("businessScope")
        private String businessScope;

        /**
         * Number of updates to company information.
         *
         * 企业信息更新次数。
         */
        @JsonProperty("updateTimes")
        private Long updateTimes;

        /**
         * History company names as a single string.
         *
         * 历史企业名称（单个字符串）。
         */
        @JsonProperty("historyNames")
        private String historyNames;

        /**
         * List of historical company names.
         *
         * 历史企业名称列表。
         */
        @JsonProperty("historyNameList")
        private List<String> historyNameList;

        /**
         * Cancellation date in Unix timestamp.
         *
         * 注销日期的Unix时间戳。
         */
        @JsonProperty("cancelDate")
        private Long cancelDate;

        /**
         * Revocation date in Unix timestamp.
         *
         * 吊销日期的Unix时间戳。
         */
        @JsonProperty("revokeDate")
        private Long revokeDate;

        /**
         * Reason for revocation.
         *
         * 吊销原因。
         */
        @JsonProperty("revokeReason")
        private String revokeReason;

        /**
         * Reason for cancellation.
         *
         * 注销原因。
         */
        @JsonProperty("cancelReason")
        private String cancelReason;

        /**
         * Approval time in Unix timestamp.
         *
         * 核准时间的Unix时间戳。
         */
        @JsonProperty("approvedTime")
        private Long approvedTime;

        /**
         * Start time of business term in Unix timestamp.
         *
         * 营业期限起始时间的Unix时间戳。
         */
        @JsonProperty("fromTime")
        private Long fromTime;

        /**
         * End time of business term in Unix timestamp.
         *
         * 营业期限结束时间的Unix时间戳。
         */
        @JsonProperty("toTime")
        private Long toTime;

        /**
         * Actual paid-in capital.
         *
         * 实缴资本。
         */
        @JsonProperty("actualCapital")
        private String actualCapital;

        /**
         * Currency of actual paid-in capital.
         *
         * 实缴资本币种。
         */
        @JsonProperty("actualCapitalCurrency")
        private String actualCapitalCurrency;

        /**
         * Registration authority.
         *
         * 登记机关。
         */
        @JsonProperty("regInstitute")
        private String regInstitute;

        /**
         * City of registration.
         *
         * 注册城市。
         */
        @JsonProperty("city")
        private String city;

        /**
         * District of registration.
         *
         * 注册区县。
         */
        @JsonProperty("district")
        private String district;

        /**
         * Employee number range.
         *
         * 员工人数范围。
         */
        @JsonProperty("staffNumRange")
        private String staffNumRange;

        /**
         * Number of social insurance participants.
         *
         * 参保人数。
         */
        @JsonProperty("socialStaffNum")
        private Integer socialStaffNum;

        /**
         * Bond number.
         *
         * 债券代码。
         */
        @JsonProperty("bondNum")
        private String bondNum;

        /**
         * Bond name.
         *
         * 债券名称。
         */
        @JsonProperty("bondName")
        private String bondName;

        /**
         * Bond type.
         *
         * 债券类型。
         */
        @JsonProperty("bondType")
        private String bondType;

        /**
         * Used bond name.
         *
         * 曾用债券名称。
         */
        @JsonProperty("usedBondName")
        private String usedBondName;

        /**
         * Company alias.
         *
         * 企业别名。
         */
        @JsonProperty("alias")
        private String alias;

        /**
         * Additional property.
         *
         * 额外属性。
         */
        @JsonProperty("property3")
        private String property3;

        /**
         * Company tags.
         *
         * 企业标签。
         */
        @JsonProperty("tags")
        private String tags;

        /**
         * Company percentile score.
         *
         * 企业百分位评分。
         */
        @JsonProperty("percentileScore")
        private Integer percentileScore;

        /**
         * Indicates if the company is a micro enterprise.
         *
         * 表示该企业是否为微型企业。
         */
        @JsonProperty("isMicroEnt")
        private Integer isMicroEnt;

        /**
         * Company base information.
         *
         * 企业基本信息。
         */
        @JsonProperty("base")
        private String base;

        /**
         * Company type.
         *
         * 企业类型。
         */
        @JsonProperty("type")
        private Integer type;

        /**
         * Company form.
         *
         * 企业形态。
         */
        @JsonProperty("compForm")
        private String compForm;

        /**
         * Detailed industry information.
         *
         * 行业详细信息。
         */
        @JsonProperty("industryAll")
        private IndustryInfo industryAll;

        // Getters and setters below
        // 以下是getter和setter方法

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCreditCode() {
            return creditCode;
        }

        public void setCreditCode(String creditCode) {
            this.creditCode = creditCode;
        }

        public String getLegalPersonName() {
            return legalPersonName;
        }

        public void setLegalPersonName(String legalPersonName) {
            this.legalPersonName = legalPersonName;
        }

        public String getRegStatus() {
            return regStatus;
        }

        public void setRegStatus(String regStatus) {
            this.regStatus = regStatus;
        }

        public String getRegCapital() {
            return regCapital;
        }

        public void setRegCapital(String regCapital) {
            this.regCapital = regCapital;
        }

        public String getRegCapitalCurrency() {
            return regCapitalCurrency;
        }

        public void setRegCapitalCurrency(String regCapitalCurrency) {
            this.regCapitalCurrency = regCapitalCurrency;
        }

        public Long getEstiblishTime() {
            return estiblishTime;
        }

        public void setEstiblishTime(Long estiblishTime) {
            this.estiblishTime = estiblishTime;
        }

        public String getCompanyOrgType() {
            return companyOrgType;
        }

        public void setCompanyOrgType(String companyOrgType) {
            this.companyOrgType = companyOrgType;
        }

        public String getRegNumber() {
            return regNumber;
        }

        public void setRegNumber(String regNumber) {
            this.regNumber = regNumber;
        }

        public String getTaxNumber() {
            return taxNumber;
        }

        public void setTaxNumber(String taxNumber) {
            this.taxNumber = taxNumber;
        }

        public String getOrgNumber() {
            return orgNumber;
        }

        public void setOrgNumber(String orgNumber) {
            this.orgNumber = orgNumber;
        }

        public String getIndustry() {
            return industry;
        }

        public void setIndustry(String industry) {
            this.industry = industry;
        }

        public String getRegLocation() {
            return regLocation;
        }

        public void setRegLocation(String regLocation) {
            this.regLocation = regLocation;
        }

        public String getBusinessScope() {
            return businessScope;
        }

        public void setBusinessScope(String businessScope) {
            this.businessScope = businessScope;
        }

        public Long getUpdateTimes() {
            return updateTimes;
        }

        public void setUpdateTimes(Long updateTimes) {
            this.updateTimes = updateTimes;
        }

        public String getHistoryNames() {
            return historyNames;
        }

        public void setHistoryNames(String historyNames) {
            this.historyNames = historyNames;
        }

        public List<String> getHistoryNameList() {
            return historyNameList;
        }

        public void setHistoryNameList(List<String> historyNameList) {
            this.historyNameList = historyNameList;
        }

        public Long getCancelDate() {
            return cancelDate;
        }

        public void setCancelDate(Long cancelDate) {
            this.cancelDate = cancelDate;
        }

        public Long getRevokeDate() {
            return revokeDate;
        }

        public void setRevokeDate(Long revokeDate) {
            this.revokeDate = revokeDate;
        }

        public String getRevokeReason() {
            return revokeReason;
        }

        public void setRevokeReason(String revokeReason) {
            this.revokeReason = revokeReason;
        }

        public String getCancelReason() {
            return cancelReason;
        }

        public void setCancelReason(String cancelReason) {
            this.cancelReason = cancelReason;
        }

        public Long getApprovedTime() {
            return approvedTime;
        }

        public void setApprovedTime(Long approvedTime) {
            this.approvedTime = approvedTime;
        }

        public Long getFromTime() {
            return fromTime;
        }

        public void setFromTime(Long fromTime) {
            this.fromTime = fromTime;
        }

        public Long getToTime() {
            return toTime;
        }

        public void setToTime(Long toTime) {
            this.toTime = toTime;
        }

        public String getActualCapital() {
            return actualCapital;
        }

        public void setActualCapital(String actualCapital) {
            this.actualCapital = actualCapital;
        }

        public String getActualCapitalCurrency() {
            return actualCapitalCurrency;
        }

        public void setActualCapitalCurrency(String actualCapitalCurrency) {
            this.actualCapitalCurrency = actualCapitalCurrency;
        }

        public String getRegInstitute() {
            return regInstitute;
        }

        public void setRegInstitute(String regInstitute) {
            this.regInstitute = regInstitute;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getDistrict() {
            return district;
        }

        public void setDistrict(String district) {
            this.district = district;
        }

        public String getStaffNumRange() {
            return staffNumRange;
        }

        public void setStaffNumRange(String staffNumRange) {
            this.staffNumRange = staffNumRange;
        }

        public Integer getSocialStaffNum() {
            return socialStaffNum;
        }

        public void setSocialStaffNum(Integer socialStaffNum) {
            this.socialStaffNum = socialStaffNum;
        }

        public String getBondNum() {
            return bondNum;
        }

        public void setBondNum(String bondNum) {
            this.bondNum = bondNum;
        }

        public String getBondName() {
            return bondName;
        }

        public void setBondName(String bondName) {
            this.bondName = bondName;
        }

        public String getBondType() {
            return bondType;
        }

        public void setBondType(String bondType) {
            this.bondType = bondType;
        }

        public String getUsedBondName() {
            return usedBondName;
        }

        public void setUsedBondName(String usedBondName) {
            this.usedBondName = usedBondName;
        }

        public String getAlias() {
            return alias;
        }

        public void setAlias(String alias) {
            this.alias = alias;
        }

        public String getProperty3() {
            return property3;
        }

        public void setProperty3(String property3) {
            this.property3 = property3;
        }

        public String getTags() {
            return tags;
        }

        public void setTags(String tags) {
            this.tags = tags;
        }

        public Integer getPercentileScore() {
            return percentileScore;
        }

        public void setPercentileScore(Integer percentileScore) {
            this.percentileScore = percentileScore;
        }

        public Integer getIsMicroEnt() {
            return isMicroEnt;
        }

        public void setIsMicroEnt(Integer isMicroEnt) {
            this.isMicroEnt = isMicroEnt;
        }

        public String getBase() {
            return base;
        }

        public void setBase(String base) {
            this.base = base;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getCompForm() {
            return compForm;
        }

        public void setCompForm(String compForm) {
            this.compForm = compForm;
        }

        public IndustryInfo getIndustryAll() {
            return industryAll;
        }

        public void setIndustryAll(IndustryInfo industryAll) {
            this.industryAll = industryAll;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IndustryInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @JsonProperty("category")
        private String category;

        @JsonProperty("categoryBig")
        private String categoryBig;

        @JsonProperty("categoryMiddle")
        private String categoryMiddle;

        @JsonProperty("categorySmall")
        private String categorySmall;

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getCategoryBig() {
            return categoryBig;
        }

        public void setCategoryBig(String categoryBig) {
            this.categoryBig = categoryBig;
        }

        public String getCategoryMiddle() {
            return categoryMiddle;
        }

        public void setCategoryMiddle(String categoryMiddle) {
            this.categoryMiddle = categoryMiddle;
        }

        public String getCategorySmall() {
            return categorySmall;
        }

        public void setCategorySmall(String categorySmall) {
            this.categorySmall = categorySmall;
        }
    }
}
