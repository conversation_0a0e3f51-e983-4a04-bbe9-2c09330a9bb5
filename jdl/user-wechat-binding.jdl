/**
 * 用户微信绑定实体JDL定义
 */
entity UserWechatBinding {
  /** 租户ID */
  tenantId Long

  /** 用户ID（员工ID） */
  employeeId Long required

  /** 微信OpenID */
  openId String required maxlength(64)

  /** 微信UnionID */
  unionId String maxlength(64)

  /** 扩展信息（JSON格式） */
  metadata TextBlob

  /** 版本号（乐观锁） */
  version Integer required

  /** 创建人 */
  createdBy String maxlength(64)

  /** 创建时间 */
  createdAt Instant required

  /** 更新人 */
  updatedBy String maxlength(64)

  /** 更新时间 */
  updatedAt Instant required

  /** 是否删除 */
  isDeleted Boolean required
}

/**
 * 分页配置
 */
paginate UserWechatBinding with pagination

/**
 * 服务层配置
 */
service UserWechatBinding with serviceImpl

/**
 * DTO配置
 */
dto UserWechatBinding with mapstruct
