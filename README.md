# WhiskerGuard通用服务

## 项目简介

WhiskerGuard通用服务是一个基于Spring Boot的微服务应用，作为WhiskerGuard平台的基础服务组件，提供通用功能支持。本服务使用JHipster 8.10.0生成的微服务架构项目，集成了多种云服务，包括腾讯云对象存储(COS)、阿里云短信服务和腾讯云短信服务，为其他微服务提供统一的通知和文件存储能力。

## 技术栈

- **框架**: Spring Boot
- **服务发现**: Consul
- **数据库**: MySQL
- **缓存**: Redis
- **构建工具**: Maven
- **容器化**: Docker
- **监控**: Prometheus + Grafana
- **文档**: SpringDoc (OpenAPI)
- **云服务集成**:
  - 腾讯云对象存储(COS)
  - 阿里云短信服务
  - 腾讯云短信服务
  - 极光推送服务

## 主要功能

### 通知服务

- **短信通知**: 支持阿里云和腾讯云双通道短信发送，可根据配置或请求动态切换
- **邮件通知**: 支持HTML格式邮件，支持抄送和密送
- **推送通知**: 集成极光推送，支持多种推送目标类型

### 文件服务

- **文件上传**: 支持将文件上传至腾讯云COS对象存储
- **文件列表**: 获取指定目录下的文件列表
- **文件访问**: 生成文件临时访问链接

### 其他通用功能

- 微服务通用功能支持
- 统一认证和授权

## 环境要求

- JDK 17+
- Node.js 22.14.0+
- Docker
- Maven 3.x
- MySQL 8.x
- Redis

## 快速开始

### 1. 启动必要的服务

```bash
# 启动MySQL和Redis
docker compose -f src/main/docker/mysql.yml up -d
docker compose -f src/main/docker/redis.yml up -d

# 启动Consul服务发现
docker compose -f src/main/docker/consul.yml up -d
```

### 2. 运行应用

```bash
# 开发环境运行
./mvnw

# 或者生产环境打包
./mvnw -Pprod clean verify
```

## 开发指南

### 本地开发

```bash
# 启动开发服务器
./mvnw

# 开启远程调试模式
./mvnw -Dspring-boot.run.jvmArguments="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000"
```

### 运行测试

```bash
# 运行单元测试
./mvnw verify

# 运行代码质量检查
./mvnw checkstyle:check
```

## API接口

### 通知服务API

- `POST /api/notifications/sms`: 发送短信通知
- `POST /api/notifications/email`: 发送邮件通知
- `POST /api/notifications/push`: 发送推送通知

### 文件服务API

- `POST /api/file/upload`: 上传文件
- `GET /api/file/list`: 获取文件列表
- `GET /api/file/url`: 获取文件访问链接

## 使用示例

### 短信服务调用示例

#### 使用阿里云发送短信

```java
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.SmsProviderType;
import com.whiskerguard.general.model.SmsRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

// 创建短信请求
SmsRequest request = new SmsRequest();
request.setRecipient("13800138000");  // 接收短信的手机号
request.setTemplateId("SMS_123456");  // 阿里云短信模板ID
request.setProviderType(SmsProviderType.ALIYUN);  // 指定使用阿里云

// 设置模板参数
Map<String, Object> params = new HashMap<>();
params.put("code", "123456");  // 验证码参数
request.setTemplateParams(params);

// 发送HTTP请求
RestTemplate restTemplate = new RestTemplate();
ResponseEntity<NotificationResponse> response = restTemplate.postForEntity(
    "http://localhost:8187/api/notifications/sms",
    request,
    NotificationResponse.class
);
```

#### 使用腾讯云发送短信

```java
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.SmsProviderType;
import com.whiskerguard.general.model.SmsRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

// 创建短信请求
SmsRequest request = new SmsRequest();
request.setRecipient("13800138000");  // 接收短信的手机号
request.setRegionCode("86");  // 国际区号，默认86
request.setTemplateId("1234567");  // 腾讯云短信模板ID
request.setProviderType(SmsProviderType.TENCENT);  // 指定使用腾讯云

// 设置模板参数
Map<String, Object> params = new HashMap<>();
params.put("0", "123456");  // 腾讯云短信参数是按数组索引传递的
params.put("1", "5");  // 有效期5分钟
request.setTemplateParams(params);

// 发送HTTP请求
RestTemplate restTemplate = new RestTemplate();
ResponseEntity<NotificationResponse> response = restTemplate.postForEntity(
    "http://localhost:8187/api/notifications/sms",
    request,
    NotificationResponse.class
);
```

### 邮件服务调用示例

```java
import com.whiskerguard.general.model.EmailRequest;
import com.whiskerguard.general.model.NotificationResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

// 创建邮件请求
EmailRequest request = new EmailRequest();
request.setRecipient("<EMAIL>");  // 收件人邮箱
request.setSubject("测试邮件");  // 邮件主题
request.setContent("<h1>这是一封测试邮件</h1><p>Hello World!</p>");  // 邮件内容
request.setHtml(true);  // 使用HTML格式

// 添加抄送和密送
List<String> ccList = new ArrayList<>();
ccList.add("<EMAIL>");
ccList.add("<EMAIL>");
request.setCc(ccList);

List<String> bccList = new ArrayList<>();
bccList.add("<EMAIL>");
request.setBcc(bccList);

// 发送HTTP请求
RestTemplate restTemplate = new RestTemplate();
ResponseEntity<NotificationResponse> response = restTemplate.postForEntity(
    "http://localhost:8187/api/notifications/email",
    request,
    NotificationResponse.class
);
```

### 极光推送服务调用示例

```java
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.PushRequest;
import com.whiskerguard.general.model.PushTargetType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 创建推送请求
PushRequest request = new PushRequest();
request.setTitle("推送标题");  // 推送标题
request.setContent("这是一条推送消息内容");  // 推送内容

// 设置推送目标类型和目标值
request.setTargetType(PushTargetType.ALIAS);  // 按别名推送
List<String> targets = new ArrayList<>();
targets.add("user123");  // 用户别名
targets.add("user456");
request.setTargets(targets);

// 设置额外数据
Map<String, Object> extras = new HashMap<>();
extras.put("type", "notification");
extras.put("actionId", 10001);
request.setExtras(extras);

// 发送HTTP请求
RestTemplate restTemplate = new RestTemplate();
ResponseEntity<NotificationResponse> response = restTemplate.postForEntity(
    "http://localhost:8187/api/notifications/push",
    request,
    NotificationResponse.class
);
```

## 项目结构

```
src/main/
├── docker/          # Docker配置文件
├── java/           # Java源代码
│   └── com/whiskerguard/general/
│       ├── config/     # 配置类
│       ├── cos/        # 腾讯云对象存储服务
│       ├── model/      # 数据模型
│       ├── service/    # 业务服务
│       ├── web/        # Web层
│       └── security/   # 安全相关
└── resources/      # 资源文件
    ├── config/    # 应用配置
    └── i18n/      # 国际化资源
```

## 配置说明

### 腾讯云COS配置

```yaml
application:
  cos:
    secret-id: 你的腾讯云SecretId
    secret-key: 你的腾讯云SecretKey
    region: ap-guangzhou
    bucket-name: your-bucket-name
    domain: https://your-bucket-name.cos.ap-guangzhou.myqcloud.com
```

### 短信服务配置

```yaml
application:
  notification:
    sms:
      enabled: true
      default-provider: ALIYUN # 默认短信提供商，可选ALIYUN或TENCENT
      # 阿里云短信配置
      access-key-id: 你的阿里云AccessKeyId
      access-key-secret: 你的阿里云AccessKeySecret
      sign-name: 你的短信签名
      endpoint: dysmsapi.aliyuncs.com
      # 腾讯云短信配置
      tencent:
        enabled: true
        secret-id: 你的腾讯云SecretId
        secret-key: 你的腾讯云SecretKey
        app-id: 你的腾讯云短信应用ID
        sign-name: 你的短信签名
        region: ap-guangzhou
```

### 邮件服务配置

```yaml
application:
  notification:
    email:
      enabled: true
      from: <EMAIL>
```

### 极光推送配置

```yaml
application:
  notification:
    push:
      enabled: true
      app-key: 你的极光推送AppKey
      master-secret: 你的极光推送MasterSecret
      production: false # 是否生产环境
```

## 其他说明

### 代码质量

```bash
# 启动Sonar服务
docker compose -f src/main/docker/sonar.yml up -d

# 运行Sonar分析
./mvnw -Pprod clean verify sonar:sonar
```

### 文档

- API文档访问地址: http://localhost:8187/v3/api-docs
- Swagger UI访问地址: http://localhost:8187/swagger-ui.html

## 许可证

本项目遵循私有许可证。未经授权，不得使用、复制或分发。

## 贡献指南

请联系项目管理员了解如何参与项目开发。

## 联系方式

如有任何问题，请联系项目维护团队。
