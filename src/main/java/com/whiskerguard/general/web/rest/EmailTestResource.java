package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.service.EmailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 邮件配置测试控制器
 */
@RestController
@RequestMapping("/api/email-test")
@Tag(name = "邮件配置测试", description = "用于测试邮件配置是否正确")
public class EmailTestResource {

    private static final Logger log = LoggerFactory.getLogger(EmailTestResource.class);

    private final EmailService emailService;
    private final JavaMailSender javaMailSender;

    @Value("${spring.mail.host:未配置}")
    private String mailHost;

    @Value("${spring.mail.port:未配置}")
    private String mailPort;

    @Value("${spring.mail.username:未配置}")
    private String mailUsername;

    @Value("${application.notification.email.enabled:false}")
    private boolean emailEnabled;

    @Autowired
    public EmailTestResource(
            EmailService emailService,
            @Autowired(required = false) JavaMailSender javaMailSender) {
        this.emailService = emailService;
        this.javaMailSender = javaMailSender;
    }

    /**
     * 检查邮件配置状态
     */
    @GetMapping("/config-status")
    @Operation(summary = "检查邮件配置状态", description = "检查当前邮件服务的配置状态")
    public ResponseEntity<Map<String, Object>> checkConfigStatus() {
        Map<String, Object> status = new HashMap<>();

        try {
            // 检查基本配置
            status.put("emailServiceEnabled", emailEnabled);
            status.put("mailHost", mailHost);
            status.put("mailPort", mailPort);
            status.put("mailUsername", mailUsername);
            status.put("javaMailSenderConfigured", javaMailSender != null);

            // 检查连接
            boolean connectionOk = false;
            String connectionMessage = "";

            if (javaMailSender != null) {
                try {
                    // 简单的连接测试：尝试创建一个消息
                    javaMailSender.createMimeMessage();
                    connectionOk = true;
                    connectionMessage = "连接正常";
                } catch (Exception e) {
                    connectionMessage = "连接测试异常: " + e.getMessage();
                }
            } else {
                connectionMessage = "JavaMailSender未配置";
            }

            status.put("connectionTest", connectionOk);
            status.put("connectionMessage", connectionMessage);

            // 基本统计信息
            if (javaMailSender != null) {
                Map<String, Object> stats = new HashMap<>();
                stats.put("javaMailSenderType", javaMailSender.getClass().getSimpleName());
                stats.put("configurationStatus", "已配置");
                status.put("statistics", stats);
            }

            // 配置建议
            if (javaMailSender == null) {
                status.put("configurationAdvice", getConfigurationAdvice());
            }

            status.put("timestamp", System.currentTimeMillis());
            status.put("success", true);

            return ResponseEntity.ok(status);

        } catch (Exception e) {
            log.error("检查邮件配置状态失败", e);
            status.put("success", false);
            status.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(status);
        }
    }

    /**
     * 发送测试邮件
     */
    @GetMapping("/send-test")
    @Operation(summary = "发送测试邮件", description = "发送一封测试邮件验证配置")
    public ResponseEntity<Map<String, Object>> sendTestEmail() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (javaMailSender == null) {
                result.put("success", false);
                result.put("message", "JavaMailSender未配置，无法发送测试邮件");
                result.put("configurationAdvice", getConfigurationAdvice());
                return ResponseEntity.badRequest().body(result);
            }

            // 发送测试邮件到配置的发件人邮箱（自己给自己发）
            String testEmail = mailUsername;
            if ("未配置".equals(testEmail) || testEmail.isEmpty()) {
                result.put("success", false);
                result.put("message", "邮件用户名未配置，无法发送测试邮件");
                return ResponseEntity.badRequest().body(result);
            }

            var response = emailService.sendSimpleEmail(
                testEmail,
                "邮件服务测试",
                "这是一封测试邮件，用于验证邮件服务配置是否正确。\n\n" +
                "如果您收到这封邮件，说明邮件服务配置成功！\n\n" +
                "发送时间: " + new java.util.Date()
            );

            result.put("success", response.isSuccess());
            result.put("message", response.getMessage());
            result.put("messageId", response.getMessageId());
            result.put("testEmailSentTo", testEmail);
            result.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("发送测试邮件失败", e);
            result.put("success", false);
            result.put("message", "发送测试邮件失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取配置建议
     */
    private Map<String, Object> getConfigurationAdvice() {
        Map<String, Object> advice = new HashMap<>();

        advice.put("title", "邮件服务配置指南");
        advice.put("description", "请按照以下步骤配置邮件服务");

        Map<String, String> steps = new HashMap<>();
        steps.put("step1", "在application-dev.yml中配置spring.mail相关属性");
        steps.put("step2", "设置环境变量：MAIL_HOST, MAIL_PORT, MAIL_USERNAME, MAIL_PASSWORD");
        steps.put("step3", "确保application.notification.email.enabled=true");
        steps.put("step4", "重启应用使配置生效");

        advice.put("steps", steps);

        Map<String, String> examples = new HashMap<>();
        examples.put("gmail", "host: smtp.gmail.com, port: 587, 需要应用专用密码");
        examples.put("qq", "host: smtp.qq.com, port: 587, 需要授权码");
        examples.put("163", "host: smtp.163.com, port: 25, 需要授权码");
        examples.put("企业邮箱", "host: smtp.exmail.qq.com, port: 587");

        advice.put("examples", examples);

        Map<String, String> envVars = new HashMap<>();
        envVars.put("MAIL_HOST", "邮件服务器地址");
        envVars.put("MAIL_PORT", "邮件服务器端口");
        envVars.put("MAIL_USERNAME", "邮箱用户名");
        envVars.put("MAIL_PASSWORD", "邮箱密码或授权码");

        advice.put("environmentVariables", envVars);

        return advice;
    }

    /**
     * 获取详细的配置信息
     */
    @GetMapping("/config-details")
    @Operation(summary = "获取详细配置信息", description = "获取当前邮件服务的详细配置信息")
    public ResponseEntity<Map<String, Object>> getConfigDetails() {
        Map<String, Object> details = new HashMap<>();

        try {
            // 基本配置信息
            Map<String, Object> basicConfig = new HashMap<>();
            basicConfig.put("host", mailHost);
            basicConfig.put("port", mailPort);
            basicConfig.put("username", mailUsername);
            basicConfig.put("passwordConfigured", !"未配置".equals(System.getProperty("MAIL_PASSWORD", "未配置")));
            basicConfig.put("enabled", emailEnabled);

            details.put("basicConfiguration", basicConfig);

            // Spring配置状态
            Map<String, Object> springConfig = new HashMap<>();
            springConfig.put("javaMailSenderBean", javaMailSender != null);
            springConfig.put("javaMailSenderClass", javaMailSender != null ? javaMailSender.getClass().getSimpleName() : "未配置");

            details.put("springConfiguration", springConfig);

            // 系统属性
            Map<String, String> systemProps = new HashMap<>();
            systemProps.put("java.version", System.getProperty("java.version"));
            systemProps.put("spring.profiles.active", System.getProperty("spring.profiles.active", "未设置"));

            details.put("systemProperties", systemProps);

            // 环境变量检查
            Map<String, String> envVars = new HashMap<>();
            envVars.put("MAIL_HOST", System.getenv("MAIL_HOST") != null ? "已设置" : "未设置");
            envVars.put("MAIL_PORT", System.getenv("MAIL_PORT") != null ? "已设置" : "未设置");
            envVars.put("MAIL_USERNAME", System.getenv("MAIL_USERNAME") != null ? "已设置" : "未设置");
            envVars.put("MAIL_PASSWORD", System.getenv("MAIL_PASSWORD") != null ? "已设置" : "未设置");

            details.put("environmentVariables", envVars);

            details.put("timestamp", System.currentTimeMillis());
            details.put("success", true);

            return ResponseEntity.ok(details);

        } catch (Exception e) {
            log.error("获取配置详情失败", e);
            details.put("success", false);
            details.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(details);
        }
    }
}
