package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.service.UserWechatBindingService;
import com.whiskerguard.general.service.dto.UserWechatBindingDTO;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import me.chanjar.weixin.mp.api.WxMpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户微信绑定管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/24
 */
@RestController
@RequestMapping("/api/wechat/binding")
public class UserWechatBindingResource {

    private static final Logger LOG = LoggerFactory.getLogger(UserWechatBindingResource.class);

    private static final String ENTITY_NAME = "whiskerguardGeneralServiceUserWechatBinding";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final UserWechatBindingService userWechatBindingService;

    private final WxMpService wxMpService;

    private final ApplicationProperties applicationProperties;

    public UserWechatBindingResource(UserWechatBindingService userWechatBindingService, WxMpService wxMpService,
                                     ApplicationProperties applicationProperties) {
        this.userWechatBindingService = userWechatBindingService;
        this.wxMpService = wxMpService;
        this.applicationProperties = applicationProperties;
    }

    /**
     * 方法名称：createUserWechatBinding
     * 描述：用户微信绑定关系。
     *
     * @param userWechatBindingDTO 用户微信绑定关系DTO
     * @return 用户微信绑定关系DTO
     * @throws URISyntaxException URI语法异常
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<UserWechatBindingDTO> createUserWechatBinding(@Valid @RequestBody UserWechatBindingDTO userWechatBindingDTO)
            throws URISyntaxException {
        LOG.debug("REST request to save UserWechatBinding : {}", userWechatBindingDTO);
        if (userWechatBindingDTO.getId() != null) {
            throw new BadRequestAlertException("A new userWechatBinding cannot already have an ID", ENTITY_NAME, "idexists");
        }
        userWechatBindingDTO = userWechatBindingService.save(userWechatBindingDTO);
        return ResponseEntity.created(new URI("/api/user-wechat-bindings/" + userWechatBindingDTO.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, userWechatBindingDTO.getId().toString()))
                .body(userWechatBindingDTO);
    }

    /**
     * 方法名称：getAllUserWechatBindings
     * 描述：获取所有用户微信绑定关系。
     *
     * @param pageable 分页信息
     * @return 用户微信绑定关系DTO列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<List<UserWechatBindingDTO>> getAllUserWechatBindings(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of UserWechatBindings");
        Page<UserWechatBindingDTO> page = userWechatBindingService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 方法名称：deleteUserWechatBinding
     * 描述：删除用户微信绑定关系。
     *
     * @param id 用户微信绑定关系ID
     * @return 删除成功的响应
     * @since 1.0
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUserWechatBinding(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete UserWechatBinding : {}", id);
        userWechatBindingService.delete(id);
        return ResponseEntity.noContent()
                .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
                .build();
    }

    /**
     * 方法名称：unbindUserWechat
     * 描述：解绑用户微信。
     *
     * @param openId 微信OpenID
     * @return 解绑结果
     * @since 1.0
     */
    @PostMapping("/unbind")
    public ResponseEntity<Map<String, Object>> unbindUser(@RequestParam String openId) {
        LOG.info("解绑用户微信: openId={}", openId);
        try {
            Map<String, Object> result = userWechatBindingService.unbindUserWechat(openId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            LOG.error("解绑用户微信异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "解绑失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 方法名称：buildSilentAuthUrl
     * 描述：构建静默授权URL（只获取OpenID）。
     *
     * @param state 状态参数
     * @return 授权URL
     * @since 1.0
     */
    @GetMapping("/oauth/build/silent")
    public ResponseEntity<Map<String, Object>> buildSilentAuthUrl(@RequestParam(required = false) String state) {
        return buildOauthUrl("snsapi_base", state);
    }

    /**
     * 方法名称：buildUserInfoAuthUrl
     * 描述：构建用户信息授权URL（获取用户详细信息）。
     *
     * @param state 状态参数
     * @return 授权URL
     * @since 1.0
     */
    @GetMapping("/oauth/build/userinfo")
    public ResponseEntity<Map<String, Object>> buildUserInfoAuthUrl(@RequestParam(required = false) String state) {
        return buildOauthUrl("snsapi_userinfo", state);
    }

    /**
     * 构建OAuth2授权URL
     */
    public ResponseEntity<Map<String, Object>> buildOauthUrl(@RequestParam(defaultValue = "snsapi_base") String scope,
                                                             @RequestParam(required = false) String state) {
        String redirectUri = applicationProperties.getNotification().getWechat().getRedirectUri();
        LOG.debug("构建OAuth2授权URL: redirectUri={}, scope={}, state={}", redirectUri, scope, state);

        Map<String, Object> result = new HashMap<>();

        try {
            // 如果没有提供state，生成一个默认的
            if (state == null || state.isEmpty()) {
                state = generateState();
            }

            // 构建授权URL
            String authUrl = wxMpService.getOAuth2Service().buildAuthorizationUrl(redirectUri, scope, state);

            result.put("success", true);
            result.put("authUrl", authUrl);
            result.put("scope", scope);
            result.put("state", state);
            result.put("redirectUri", redirectUri);
            result.put("message", "授权URL构建成功");

            LOG.info("OAuth2授权URL构建成功: scope={}, state={}", scope, state);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            LOG.error("构建OAuth2授权URL失败", e);
            result.put("success", false);
            result.put("message", "构建授权URL失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 生成状态参数
     */
    private String generateState() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomStr = String.valueOf((int) (Math.random() * 10000));
        String state = timestamp + "_" + randomStr;
        return Base64.getEncoder().encodeToString(state.getBytes(StandardCharsets.UTF_8));
    }
}
