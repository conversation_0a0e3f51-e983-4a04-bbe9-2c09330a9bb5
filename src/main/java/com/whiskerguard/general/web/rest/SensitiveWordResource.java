package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.repository.SensitiveWordRepository;
import com.whiskerguard.general.service.SensitiveWordService;
import com.whiskerguard.general.service.dto.SensitiveWordDTO;
import com.whiskerguard.general.service.dto.SensitiveWordFilterRequestDTO;
import com.whiskerguard.general.service.dto.SensitiveWordFilterResponseDTO;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 敏感词管理
 *
 * REST controller for managing {@link com.whiskerguard.general.domain.SensitiveWord}.
 */
@RestController
@RequestMapping("/api/sensitive-words")
public class SensitiveWordResource {

    private static final Logger LOG = LoggerFactory.getLogger(SensitiveWordResource.class);

    private static final String ENTITY_NAME = "whiskerguardGeneralServiceSensitiveWord";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final SensitiveWordService sensitiveWordService;

    private final SensitiveWordRepository sensitiveWordRepository;

    /**
     * 构造函数，通过依赖注入初始化服务
     */
    public SensitiveWordResource(SensitiveWordService sensitiveWordService, SensitiveWordRepository sensitiveWordRepository) {
        this.sensitiveWordService = sensitiveWordService;
        this.sensitiveWordRepository = sensitiveWordRepository;
    }

    /**
     *
     * 创建新的敏感词
     * {@code POST  /sensitive-words} : 。
     *
     * @param sensitiveWordDTO 要创建的敏感词DTO
     * @return {@link ResponseEntity} 状态码 {@code 201 (Created)} 并在响应体中包含新创建的敏感词DTO，
     *         如果敏感词已存在ID则返回状态码 {@code 400 (Bad Request)}。
     * @throws URISyntaxException 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<SensitiveWordDTO> createSensitiveWord(@Valid @RequestBody SensitiveWordDTO sensitiveWordDTO)
        throws URISyntaxException {
        LOG.debug("REST请求创建敏感词 : {}", sensitiveWordDTO);
        if (sensitiveWordDTO.getId() != null) {
            throw new BadRequestAlertException("新建敏感词不能指定ID", ENTITY_NAME, "idexists");
        }
        sensitiveWordDTO = sensitiveWordService.save(sensitiveWordDTO);
        return ResponseEntity.created(new URI("/api/sensitive-words/" + sensitiveWordDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, sensitiveWordDTO.getId().toString()))
            .body(sensitiveWordDTO);
    }

    /**
     * 更新已存在的敏感词
     * {@code PUT  /sensitive-words/:id} : 。
     *
     * @param id 要保存的敏感词DTO的ID
     * @param sensitiveWordDTO 要更新的敏感词DTO
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含更新后的敏感词DTO,
     *         如果敏感词DTO无效则返回状态码 {@code 400 (Bad Request)},
     *         如果敏感词DTO无法更新则返回状态码 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果位置URI语法不正确
     */
    @PutMapping("/{id}")
    public ResponseEntity<SensitiveWordDTO> updateSensitiveWord(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody SensitiveWordDTO sensitiveWordDTO
    ) throws URISyntaxException {
        LOG.debug("REST请求更新敏感词 : {}, {}", id, sensitiveWordDTO);
        if (sensitiveWordDTO.getId() == null) {
            throw new BadRequestAlertException("ID无效", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, sensitiveWordDTO.getId())) {
            throw new BadRequestAlertException("ID不匹配", ENTITY_NAME, "idinvalid");
        }

        if (!sensitiveWordRepository.existsById(id)) {
            throw new BadRequestAlertException("实体不存在", ENTITY_NAME, "idnotfound");
        }

        sensitiveWordDTO = sensitiveWordService.update(sensitiveWordDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, sensitiveWordDTO.getId().toString()))
            .body(sensitiveWordDTO);
    }

    /**
     * 部分更新敏感词的给定字段
     * {@code PATCH  /sensitive-words/:id} :
     *
     * @param id 要保存的敏感词DTO的ID
     * @param sensitiveWordDTO 要更新的敏感词DTO
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含更新后的敏感词DTO,
     *         如果敏感词DTO无效则返回状态码 {@code 400 (Bad Request)},
     *         如果敏感词DTO未找到则返回状态码 {@code 404 (Not Found)},
     *         如果敏感词DTO无法更新则返回状态码 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果位置URI语法不正确
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<SensitiveWordDTO> partialUpdateSensitiveWord(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody SensitiveWordDTO sensitiveWordDTO
    ) throws URISyntaxException {
        LOG.debug("REST请求部分更新敏感词 : {}, {}", id, sensitiveWordDTO);
        if (sensitiveWordDTO.getId() == null) {
            throw new BadRequestAlertException("ID无效", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, sensitiveWordDTO.getId())) {
            throw new BadRequestAlertException("ID不匹配", ENTITY_NAME, "idinvalid");
        }

        if (!sensitiveWordRepository.existsById(id)) {
            throw new BadRequestAlertException("实体不存在", ENTITY_NAME, "idnotfound");
        }

        Optional<SensitiveWordDTO> result = sensitiveWordService.partialUpdate(sensitiveWordDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, sensitiveWordDTO.getId().toString())
        );
    }

    /**
     * 获取所有敏感词
     * {@code GET  /sensitive-words} : 。
     *
     * @param pageable 分页信息
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含敏感词列表
     */
    @GetMapping("")
    public ResponseEntity<List<SensitiveWordDTO>> getAllSensitiveWords(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST请求获取敏感词分页数据");
        Page<SensitiveWordDTO> page = sensitiveWordService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 根据"id"获取敏感词
     * {@code GET  /sensitive-words/:id} : 。
     *
     * @param id 要检索的敏感词DTO的ID
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含敏感词DTO，
     *         如果未找到则返回状态码 {@code 404 (Not Found)}
     */
    @GetMapping("/{id}")
    public ResponseEntity<SensitiveWordDTO> getSensitiveWord(@PathVariable("id") Long id) {
        LOG.debug("REST请求获取敏感词 : {}", id);
        Optional<SensitiveWordDTO> sensitiveWordDTO = sensitiveWordService.findOne(id);
        return ResponseUtil.wrapOrNotFound(sensitiveWordDTO);
    }

    /**
     * 删除"id"敏感词
     * {@code DELETE  /sensitive-words/:id} : 。
     *
     * @param id 要删除的敏感词DTO的ID
     * @return {@link ResponseEntity} 状态码 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSensitiveWord(@PathVariable("id") Long id) {
        LOG.debug("REST请求删除敏感词 : {}", id);
        sensitiveWordService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * 过滤内容中的敏感词
     * {@code POST  /sensitive-words/filter} : 过滤传入内容中的敏感词，将敏感词替换为 *。
     *
     * @param content 需要过滤的内容
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含过滤后的内容
     */
    @PostMapping("/filter")
    public ResponseEntity<String> filterContent(@RequestBody String content) {
        LOG.debug("REST请求过滤内容中的敏感词");
        String filteredContent = sensitiveWordService.filterSensitiveWords(content);
        return ResponseEntity.ok(filteredContent);
    }

    /**
     * 过滤内容中的敏感词并返回详细信息
     * {@code POST  /sensitive-words/filter-detailed} : 过滤传入内容中的敏感词，并返回过滤后的内容和发现的敏感词列表。
     *
     * @param content 需要过滤的内容
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含过滤结果详情
     */
    @PostMapping("/filter-detailed")
    public ResponseEntity<SensitiveWordFilterResponseDTO> filterContentDetailed(@RequestBody String content) {
        LOG.debug("REST请求过滤内容中的敏感词并返回详细信息");

        String filteredContent = sensitiveWordService.filterSensitiveWords(content);
        List<String> foundWords = sensitiveWordService.findSensitiveWords(content);

        SensitiveWordFilterResponseDTO response = new SensitiveWordFilterResponseDTO();
        response.setOriginalContent(content);
        response.setFilteredContent(filteredContent);
        response.setFoundSensitiveWords(foundWords);
        response.setContainsSensitiveWords(!foundWords.isEmpty());

        return ResponseEntity.ok(response);
    }

    /**
     * 批量处理多个内容项的敏感词过滤
     * {@code POST  /sensitive-words/batch-filter} : 批量过滤多个内容中的敏感词。
     *
     * @param request 包含多个需要过滤的内容项的请求
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含所有内容项的过滤结果
     */
    @PostMapping("/batch-filter")
    public ResponseEntity<List<SensitiveWordFilterResponseDTO>> batchFilterContent(@RequestBody SensitiveWordFilterRequestDTO request) {
        LOG.debug("REST请求批量过滤内容中的敏感词");

        List<SensitiveWordFilterResponseDTO> results = request
            .getContents()
            .stream()
            .map(content -> {
                String filteredContent = sensitiveWordService.filterSensitiveWords(content);
                List<String> foundWords = sensitiveWordService.findSensitiveWords(content);

                SensitiveWordFilterResponseDTO response = new SensitiveWordFilterResponseDTO();
                response.setOriginalContent(content);
                response.setFilteredContent(filteredContent);
                response.setFoundSensitiveWords(foundWords);
                response.setContainsSensitiveWords(!foundWords.isEmpty());

                return response;
            })
            .collect(Collectors.toList());

        return ResponseEntity.ok(results);
    }

    /**
     * 检查内容是否包含敏感词
     * {@code POST  /sensitive-words/contains} : 检查内容是否包含任何敏感词，不进行过滤。
     *
     * @param content 需要检查的内容
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含布尔值结果
     */
    @PostMapping("/contains")
    public ResponseEntity<Boolean> containsSensitiveWords(@RequestBody String content) {
        LOG.debug("REST请求检查内容是否包含敏感词");
        boolean containsSensitiveWords = !sensitiveWordService.findSensitiveWords(content).isEmpty();
        return ResponseEntity.ok(containsSensitiveWords);
    }
}
