package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.service.UserWechatBindingService;
import com.whiskerguard.general.service.WechatMessageTimeManager;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信公众号回调处理器
 * 用于处理微信服务器推送的消息和事件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
@RestController
@RequestMapping("/api/wechat/callback")
@ConditionalOnProperty(prefix = "application.notification.wechat", name = "enabled", havingValue = "true")
public class WechatCallbackResource {

    private static final Logger log = LoggerFactory.getLogger(WechatCallbackResource.class);

    private final WxMpService wxMpService;
    private final WechatMessageTimeManager timeManager;
    private final UserWechatBindingService userWechatBindingService;

    @Autowired
    public WechatCallbackResource(WxMpService wxMpService, WechatMessageTimeManager timeManager,
                                  UserWechatBindingService userWechatBindingService) {
        this.wxMpService = wxMpService;
        this.timeManager = timeManager;
        this.userWechatBindingService = userWechatBindingService;
    }

    /**
     * 微信服务器验证URL有效性
     */
    @GetMapping
    public String verify(
        @RequestParam("signature") String signature,
        @RequestParam("timestamp") String timestamp,
        @RequestParam("nonce") String nonce,
        @RequestParam("echostr") String echostr) {

        log.debug("微信服务器验证请求: signature={}, timestamp={}, nonce={}, echostr={}",
            signature, timestamp, nonce, echostr);

        try {
            if (wxMpService.checkSignature(timestamp, nonce, signature)) {
                log.info("微信服务器验证成功");
                return echostr;
            } else {
                log.warn("微信服务器验证失败");
                return "验证失败";
            }
        } catch (Exception e) {
            log.error("微信服务器验证异常", e);
            return "验证异常";
        }
    }

    /**
     * 处理微信服务器推送的消息
     */
    @PostMapping
    public String handleMessage(
        @RequestParam("signature") String signature,
        @RequestParam("timestamp") String timestamp,
        @RequestParam("nonce") String nonce,
        @RequestParam(value = "encrypt_type", required = false) String encryptType,
        @RequestParam(value = "msg_signature", required = false) String msgSignature,
        @RequestBody String requestBody) {

        log.debug("接收到微信消息: signature={}, timestamp={}, nonce={}, encryptType={}, msgSignature={}",
            signature, timestamp, nonce, encryptType, msgSignature);
        log.debug("消息内容: {}", requestBody);

        try {
            // 验证签名
            if (!wxMpService.checkSignature(timestamp, nonce, signature)) {
                log.warn("微信消息签名验证失败");
                return "签名验证失败";
            }

            // 解析消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
            log.info("收到微信消息: 类型={}, 发送者={}, 内容={}",
                inMessage.getMsgType(), inMessage.getFromUser(), inMessage.getContent());

            // 记录用户交互时间（重要：这样可以重置48小时限制）
            timeManager.recordUserInteraction(inMessage.getFromUser());

            // 处理消息
            WxMpXmlOutMessage outMessage = handleIncomingMessage(inMessage);

            if (outMessage != null) {
                return outMessage.toXml();
            } else {
                return "success";
            }
        } catch (Exception e) {
            log.error("处理微信消息异常: {}", e.getMessage(), e);
            return "处理异常";
        }
    }

    /**
     * 处理接收到的消息
     */
    private WxMpXmlOutMessage handleIncomingMessage(WxMpXmlMessage inMessage) {
        String msgType = inMessage.getMsgType();

        return switch (msgType) {
            case "text" -> handleTextMessage(inMessage);
            case "image" -> handleImageMessage(inMessage);
            case "voice" -> handleVoiceMessage(inMessage);
            case "video" -> handleVideoMessage(inMessage);
            case "event" -> handleEventMessage(inMessage);
            default -> {
                log.info("收到未处理的消息类型: {}", msgType);
                yield null;
            }
        };
    }

    /**
     * 处理文本消息
     */
    private WxMpXmlOutMessage handleTextMessage(WxMpXmlMessage inMessage) {
        String content = inMessage.getContent();
        log.info("收到文本消息: {}", content);

        // 简单的自动回复逻辑
        String replyContent;
        if ("你好".equals(content) || "hello".equalsIgnoreCase(content)) {
            replyContent = "您好！欢迎关注我们的公众号！";
        } else if ("帮助".equals(content) || "help".equalsIgnoreCase(content)) {
            replyContent = "这里是帮助信息：\n1. 发送'你好'获取欢迎信息\n2. 发送'帮助'获取帮助信息\n3. 发送其他内容获取智能回复";
        } else {
            replyContent = "感谢您的消息：" + content + "\n我们已收到并会尽快处理！";
        }

        return WxMpXmlOutMessage.TEXT()
            .content(replyContent)
            .fromUser(inMessage.getToUser())
            .toUser(inMessage.getFromUser())
            .build();
    }

    /**
     * 处理图片消息
     */
    private WxMpXmlOutMessage handleImageMessage(WxMpXmlMessage inMessage) {
        log.info("收到图片消息: {}", inMessage.getPicUrl());

        return WxMpXmlOutMessage.TEXT()
            .content("收到您发送的图片，谢谢分享！")
            .fromUser(inMessage.getToUser())
            .toUser(inMessage.getFromUser())
            .build();
    }

    /**
     * 处理语音消息
     */
    private WxMpXmlOutMessage handleVoiceMessage(WxMpXmlMessage inMessage) {
        log.info("收到语音消息: {}", inMessage.getMediaId());

        return WxMpXmlOutMessage.TEXT()
            .content("收到您的语音消息，我们正在处理中...")
            .fromUser(inMessage.getToUser())
            .toUser(inMessage.getFromUser())
            .build();
    }

    /**
     * 处理视频消息
     */
    private WxMpXmlOutMessage handleVideoMessage(WxMpXmlMessage inMessage) {
        log.info("收到视频消息: {}", inMessage.getMediaId());

        return WxMpXmlOutMessage.TEXT()
            .content("收到您的视频消息，感谢分享！")
            .fromUser(inMessage.getToUser())
            .toUser(inMessage.getFromUser())
            .build();
    }

    /**
     * 处理事件消息
     */
    private WxMpXmlOutMessage handleEventMessage(WxMpXmlMessage inMessage) {
        String event = inMessage.getEvent();
        log.info("收到事件消息: {}", event);

        return switch (event) {
            case "subscribe" -> handleSubscribeEvent(inMessage);
            case "unsubscribe" -> handleUnsubscribeEvent(inMessage);
            case "CLICK" -> handleMenuClickEvent(inMessage);
            default -> {
                log.info("收到未处理的事件类型: {}", event);
                yield null;
            }
        };
    }

    /**
     * 处理关注事件
     */
    private WxMpXmlOutMessage handleSubscribeEvent(WxMpXmlMessage inMessage) {
        String openId = inMessage.getFromUser();
        log.info("用户关注: {}", openId);

        // 构建欢迎消息
        String welcomeMessage = """
            🎉 欢迎关注我们的公众号！

            这里您可以：
            📰 获取最新资讯和动态
            🎮 参与精彩互动活动
            💬 获得专业客服支持

            💡 发送'帮助'获取更多功能介绍
            🔗 点击菜单探索更多服务""";

        return WxMpXmlOutMessage.TEXT()
            .content(welcomeMessage)
            .fromUser(inMessage.getToUser())
            .toUser(inMessage.getFromUser())
            .build();
    }

    /**
     * 处理取消关注事件
     */
    private WxMpXmlOutMessage handleUnsubscribeEvent(WxMpXmlMessage inMessage) {
        String openId = inMessage.getFromUser();
        log.info("用户取消关注: {}", openId);

        try {
            //处理用户取消关注事件，解绑用户微信
            userWechatBindingService.unbindUserWechatByOpenId(openId);
            log.info("用户取消关注事件处理完成: openId={}", openId);
        } catch (Exception e) {
            log.error("处理用户取消关注事件失败: openId={}", openId, e);
        }
        return null;
    }

    /**
     * 处理菜单点击事件
     */
    private WxMpXmlOutMessage handleMenuClickEvent(WxMpXmlMessage inMessage) {
        String eventKey = inMessage.getEventKey();
        log.info("菜单点击事件: {}", eventKey);

        String replyContent = "您点击了菜单：" + eventKey;

        return WxMpXmlOutMessage.TEXT()
            .content(replyContent)
            .fromUser(inMessage.getToUser())
            .toUser(inMessage.getFromUser())
            .build();
    }


    /**
     * 处理OAuth2授权回调，获取OpenID和用户信息
     */
    @GetMapping("/oauth")
    public ResponseEntity<Map<String, Object>> handleOauthCallback(@RequestParam String code, @RequestParam(required = false) String state) {
        log.info("处理OAuth2授权回调: code={}, state={}", code, state);

        Map<String, Object> result = new HashMap<>();
        try {
            // 通过code获取access_token
            WxOAuth2AccessToken accessToken = wxMpService.getOAuth2Service().getAccessToken(code);

            String openId = accessToken.getOpenId();
            String scope = accessToken.getScope();

            log.info("OAuth2授权成功: openId={}, scope={}", openId, scope);

            result.put("success", true);
            result.put("openId", openId);
            result.put("scope", scope);
            result.put("accessToken", accessToken.getAccessToken());
            result.put("refreshToken", accessToken.getRefreshToken());
            result.put("expiresIn", accessToken.getExpiresIn());
            result.put("state", state);
            result.put("message", "授权成功");

            // 如果是snsapi_userinfo授权，获取用户详细信息
            if ("snsapi_userinfo".equals(scope)) {
                try {
                    WxOAuth2UserInfo userInfo = wxMpService.getOAuth2Service().getUserInfo(accessToken, null);

                    Map<String, Object> userDetails = new HashMap<>();
                    userDetails.put("openId", userInfo.getOpenid());
                    userDetails.put("nickname", userInfo.getNickname());
                    userDetails.put("sex", userInfo.getSex());
                    userDetails.put("city", userInfo.getCity());
                    userDetails.put("country", userInfo.getCountry());
                    userDetails.put("province", userInfo.getProvince());
                    userDetails.put("language", "zh_CN");
                    userDetails.put("headImgUrl", userInfo.getHeadImgUrl());
                    userDetails.put("unionId", userInfo.getUnionId());

                    result.put("userInfo", userDetails);
                    log.info("获取用户详细信息成功: nickname={}, city={}", userInfo.getNickname(), userInfo.getCity());
                } catch (Exception e) {
                    log.warn("获取用户详细信息失败: {}", e.getMessage());
                    result.put("userInfoError", "获取用户详细信息失败: " + e.getMessage());
                }
            }
            return ResponseEntity.ok(result);
        } catch (WxErrorException e) {
            log.error("OAuth2授权失败: 错误代码={}, 错误信息={}", e.getError().getErrorCode(), e.getError().getErrorMsg());
            result.put("success", false);
            result.put("errorCode", e.getError().getErrorCode());
            result.put("errorMsg", e.getError().getErrorMsg());
            result.put("message", "授权失败: " + e.getError().getErrorMsg());
            return ResponseEntity.badRequest().body(result);
        } catch (Exception e) {
            log.error("处理OAuth2授权回调异常", e);
            result.put("success", false);
            result.put("message", "处理授权回调异常: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

}
