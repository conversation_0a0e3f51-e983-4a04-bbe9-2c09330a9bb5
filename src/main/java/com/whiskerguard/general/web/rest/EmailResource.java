package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.model.EmailRequest;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.service.EmailService;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 邮件服务REST控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/30
 */
@RestController
@RequestMapping("/api/email")
public class EmailResource {

    private static final Logger log = LoggerFactory.getLogger(EmailResource.class);

    private final EmailService emailService;

    @Autowired
    public EmailResource(EmailService emailService) {
        this.emailService = emailService;
    }

    /**
     * 发送邮件
     */
    @PostMapping("/send")
    public ResponseEntity<NotificationResponse> sendEmail(@Valid @RequestBody EmailRequest emailRequest) {
        log.info("收到邮件发送请求: to={}, subject={}", emailRequest.getTo(), emailRequest.getSubject());

        try {
            NotificationResponse response = emailService.send(emailRequest);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("邮件发送失败", e);
            return ResponseEntity.badRequest().body(
                NotificationResponse.failure("邮件发送失败: " + e.getMessage())
            );
        }
    }

    /**
     * 异步发送邮件
     */
    @PostMapping("/send/async")
    public ResponseEntity<Map<String, Object>> sendEmailAsync(@Valid @RequestBody EmailRequest emailRequest) {
        log.info("收到异步邮件发送请求: to={}, subject={}", emailRequest.getTo(), emailRequest.getSubject());

        try {
            CompletableFuture<NotificationResponse> future = emailService.sendEmailAsync(emailRequest);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "邮件已提交异步发送");
            response.put("taskId", "async-" + System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("异步邮件发送失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "异步邮件发送失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量发送邮件
     */
    @PostMapping("/send/batch")
    public ResponseEntity<List<NotificationResponse>> sendBatchEmails(@Valid @RequestBody List<EmailRequest> emailRequests) {
        log.info("收到批量邮件发送请求: count={}", emailRequests.size());

        try {
            List<NotificationResponse> responses = emailService.sendBatchEmails(emailRequests);
            return ResponseEntity.ok(responses);
        } catch (Exception e) {
            log.error("批量邮件发送失败", e);
            return ResponseEntity.badRequest().body(List.of(
                NotificationResponse.failure("批量邮件发送失败: " + e.getMessage())
            ));
        }
    }

    /**
     * 发送简单文本邮件
     */
    @PostMapping("/send/simple")
    public ResponseEntity<NotificationResponse> sendSimpleEmail(
        @Parameter(description = "收件人邮箱") @RequestParam String to,
        @Parameter(description = "邮件主题") @RequestParam String subject,
        @Parameter(description = "邮件内容") @RequestParam String content) {

        log.info("收到简单邮件发送请求: to={}, subject={}", to, subject);

        try {
            NotificationResponse response = emailService.sendSimpleEmail(to, subject, content);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("简单邮件发送失败", e);
            return ResponseEntity.badRequest().body(
                NotificationResponse.failure("简单邮件发送失败: " + e.getMessage())
            );
        }
    }

    /**
     * 发送HTML邮件
     */
    @PostMapping("/send/html")
    public ResponseEntity<NotificationResponse> sendHtmlEmail(
        @Parameter(description = "收件人邮箱") @RequestParam String to,
        @Parameter(description = "邮件主题") @RequestParam String subject,
        @Parameter(description = "HTML内容") @RequestParam String htmlContent) {

        log.info("收到HTML邮件发送请求: to={}, subject={}", to, subject);

        try {
            NotificationResponse response = emailService.sendHtmlEmail(to, subject, htmlContent);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("HTML邮件发送失败", e);
            return ResponseEntity.badRequest().body(
                NotificationResponse.failure("HTML邮件发送失败: " + e.getMessage())
            );
        }
    }

    /**
     * 验证邮箱地址
     */
    @GetMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateEmail(
        @Parameter(description = "邮箱地址") @RequestParam String email) {

        Map<String, Object> response = new HashMap<>();
        boolean isValid = emailService.isValidEmail(email);

        response.put("email", email);
        response.put("valid", isValid);
        response.put("message", isValid ? "邮箱地址格式正确" : "邮箱地址格式不正确");

        return ResponseEntity.ok(response);
    }

}
