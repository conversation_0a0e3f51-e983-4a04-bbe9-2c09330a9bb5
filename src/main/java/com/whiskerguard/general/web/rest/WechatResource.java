package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.WechatRequest;
import com.whiskerguard.general.service.NotificationService;
import com.whiskerguard.general.service.WechatAccessTokenService;
import com.whiskerguard.general.service.WechatService;
import com.whiskerguard.general.service.dto.BatchTextRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信公众号相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
@RestController
@RequestMapping("/api/wechat")
public class WechatResource {

    private static final Logger log = LoggerFactory.getLogger(WechatResource.class);

    private final NotificationService notificationService;
    private final WechatService wechatService;
    private final WechatAccessTokenService accessTokenService;

    @Autowired
    public WechatResource(NotificationService notificationService, WechatService wechatService,
                          WechatAccessTokenService accessTokenService) {
        this.notificationService = notificationService;
        this.wechatService = wechatService;
        this.accessTokenService = accessTokenService;
    }

    /**
     * 方法名称：sendMessage
     * 描述：发送微信公众号消息。
     *
     * @param request 请求参数
     * @return 发送结果
     * @since 1.0
     */
    @PostMapping("/send")
    public ResponseEntity<NotificationResponse> sendMessage(@Valid @RequestBody WechatRequest request) {
        log.debug("REST request to send wechat message: {}", request);

        NotificationResponse response = notificationService.sendWechat(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 方法名称：sendCustomMessage
     * 描述：发送客服消息。
     *
     * @param request 请求参数
     * @return 发送结果
     * @since 1.0
     */
    @PostMapping("/custom/message")
    public ResponseEntity<NotificationResponse> sendCustomMessage(@Valid @RequestBody WechatRequest request) {
        log.debug("REST request to send wechat custom message: {}", request);

        NotificationResponse response = wechatService.sendCustomMessage(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 方法名称：sendTemplateMessage
     * 描述：发送模板消息。
     *
     * @param request 请求参数
     * @return 发送结果
     * @since 1.0
     */
    @PostMapping("/template/message")
    public ResponseEntity<NotificationResponse> sendTemplateMessage(@Valid @RequestBody WechatRequest request) {
        log.debug("REST request to send wechat template message: {}", request);

        NotificationResponse response = wechatService.sendTemplateMessage(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 方法名称：getUserInfo
     * 描述：获取用户信息。
     *
     * @param openId 用户OpenID
     * @return 用户信息
     * @since 1.0
     */
    @GetMapping("/user/{openId}")
    public ResponseEntity<String> getUserInfo(@Parameter(description = "用户OpenID") @PathVariable String openId) {
        log.debug("REST request to get wechat user info: {}", openId);
        String userInfo = wechatService.getUserInfo(openId);
        return ResponseEntity.ok(userInfo);
    }


    /**
     * 方法名称：getFollowers
     * 描述：获取关注者列表。
     *
     * @param nextOpenId 下一个OpenID，用于分页
     * @return 关注者列表
     * @since 1.0
     */
    @GetMapping("/followers")
    public ResponseEntity<String> getFollowers(
        @Parameter(description = "下一个OpenID，用于分页") @RequestParam(required = false) String nextOpenId) {
        log.debug("REST request to get wechat followers, nextOpenId: {}", nextOpenId);

        String followers = wechatService.getFollowers(nextOpenId);
        return ResponseEntity.ok(followers);
    }

    /**
     * 方法名称：sendTextMessage
     * 描述：快速发送文本消息。
     *
     * @param openId  用户OpenID
     * @param content 消息内容
     * @return 发送结果
     * @since 1.0
     */
    @PostMapping("/send/text")
    public ResponseEntity<NotificationResponse> sendTextMessage(
        @Parameter(description = "用户OpenID") @RequestParam String openId,
        @Parameter(description = "消息内容") @RequestParam String content) {
        log.debug("REST request to send text message to: {}, content: {}", openId, content);

        WechatRequest request = new WechatRequest();
        request.setMessageType("text");
        request.getToUsers().add(openId);
        request.setContent(content);

        NotificationResponse response = wechatService.sendCustomMessage(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 方法名称：sendTextMessageBatch
     * 描述：批量发送文本消息。
     *
     * @param request 请求参数
     * @return 发送结果
     * @since 1.0
     */
    @PostMapping("/send/text/batch")
    public ResponseEntity<NotificationResponse> sendTextMessageBatch(@Valid @RequestBody BatchTextRequest request) {
        log.debug("REST request to send batch text message to: {}, content: {}",
            request.getOpenIds(), request.getContent());

        WechatRequest wechatRequest = new WechatRequest();
        wechatRequest.setMessageType("text");
        wechatRequest.setToUsers(request.getOpenIds());
        wechatRequest.setContent(request.getContent());

        NotificationResponse response = wechatService.sendCustomMessage(wechatRequest);
        return ResponseEntity.ok(response);
    }

    /**
     * 方法名称：getAccessTokenInfo
     * 描述：获取Access Token的详细信息。
     *
     * @return Access Token信息
     * @since 1.0
     */
    @GetMapping("/access/token/info")
    @Operation(summary = "获取Access Token信息", description = "获取当前Access Token的详细信息")
    public ResponseEntity<Map<String, Object>> getAccessTokenInfo() {
        log.debug("REST request to get access token info");

        Map<String, Object> info = accessTokenService.getAccessTokenInfo();
        return ResponseEntity.ok(info);
    }

    /**
     * 方法名称：refreshAccessToken
     * 描述：强制刷新Access Token。
     *
     * @return 刷新结果
     * @since 1.0
     */
    @PostMapping("/access/token/refresh")
    @Operation(summary = "刷新Access Token", description = "强制刷新Access Token")
    public ResponseEntity<Map<String, Object>> refreshAccessToken() {
        log.debug("REST request to refresh access token");

        String newToken = accessTokenService.refreshAccessToken();
        Map<String, Object> response = new HashMap<>();

        if (newToken != null) {
            response.put("success", true);
            response.put("message", "Access Token刷新成功");
            response.put("accessToken", newToken.substring(0, 6) + "***" + newToken.substring(newToken.length() - 4));
            response.put("remainingTime", accessTokenService.getAccessTokenRemainingTime());
        } else {
            response.put("success", false);
            response.put("message", "Access Token刷新失败");
        }

        return ResponseEntity.ok(response);
    }
}
