package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.repository.SignatureDocumentRepository;
import com.whiskerguard.general.service.SignatureDocumentService;
import com.whiskerguard.general.service.dto.SignatureDocumentDTO;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 管理签名文档
 * REST controller for managing {@link com.whiskerguard.general.domain.SignatureDocument}.
 */
@RestController
@RequestMapping("/api/signature-documents")
public class SignatureDocumentResource {

    private static final Logger LOG = LoggerFactory.getLogger(SignatureDocumentResource.class);

    private static final String ENTITY_NAME = "whiskerguardGeneralServiceSignatureDocument";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final SignatureDocumentService signatureDocumentService;

    private final SignatureDocumentRepository signatureDocumentRepository;

    public SignatureDocumentResource(
        SignatureDocumentService signatureDocumentService,
        SignatureDocumentRepository signatureDocumentRepository
    ) {
        this.signatureDocumentService = signatureDocumentService;
        this.signatureDocumentRepository = signatureDocumentRepository;
    }

    /**
     * {@code POST  /signature-documents} : Create a new signatureDocument.
     * 创建一个新的签名文档
     *
     * @param signatureDocumentDTO the signatureDocumentDTO to create. 要创建的签名文档DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new signatureDocumentDTO, or with status {@code 400 (Bad Request)} if the signatureDocument has already an ID.
     *         返回状态码为201(已创建)的响应实体，包含新创建的签名文档DTO，如果签名文档已有ID则返回状态码400(错误请求)
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确则抛出异常
     */
    @PostMapping("")
    public ResponseEntity<SignatureDocumentDTO> createSignatureDocument(@Valid @RequestBody SignatureDocumentDTO signatureDocumentDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save SignatureDocument : {}", signatureDocumentDTO);
        if (signatureDocumentDTO.getId() != null) {
            throw new BadRequestAlertException("A new signatureDocument cannot already have an ID", ENTITY_NAME, "idexists");
        }
        signatureDocumentDTO = signatureDocumentService.save(signatureDocumentDTO);
        return ResponseEntity.created(new URI("/api/signature-documents/" + signatureDocumentDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, signatureDocumentDTO.getId().toString()))
            .body(signatureDocumentDTO);
    }

    /**
     * {@code PUT  /signature-documents/:id} : Updates an existing signatureDocument.
     * 更新现有的签名文档
     *
     * @param id the id of the signatureDocumentDTO to save. 要保存的签名文档DTO的ID
     * @param signatureDocumentDTO the signatureDocumentDTO to update. 要更新的签名文档DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated signatureDocumentDTO,
     * or with status {@code 400 (Bad Request)} if the signatureDocumentDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the signatureDocumentDTO couldn't be updated.
     *         返回状态码为200(成功)的响应实体，包含更新后的签名文档DTO；
     *         如果签名文档DTO无效则返回状态码400(错误请求)；
     *         如果无法更新签名文档DTO则返回状态码500(服务器内部错误)
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确则抛出异常
     */
    @PutMapping("/{id}")
    public ResponseEntity<SignatureDocumentDTO> updateSignatureDocument(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody SignatureDocumentDTO signatureDocumentDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update SignatureDocument : {}, {}", id, signatureDocumentDTO);
        if (signatureDocumentDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, signatureDocumentDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!signatureDocumentRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        signatureDocumentDTO = signatureDocumentService.update(signatureDocumentDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, signatureDocumentDTO.getId().toString()))
            .body(signatureDocumentDTO);
    }

    /**
     * {@code PATCH  /signature-documents/:id} : Partial updates given fields of an existing signatureDocument, field will ignore if it is null
     * 部分更新现有签名文档的指定字段，如果字段为null则忽略
     *
     * @param id the id of the signatureDocumentDTO to save. 要保存的签名文档DTO的ID
     * @param signatureDocumentDTO the signatureDocumentDTO to update. 要更新的签名文档DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated signatureDocumentDTO,
     * or with status {@code 400 (Bad Request)} if the signatureDocumentDTO is not valid,
     * or with status {@code 404 (Not Found)} if the signatureDocumentDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the signatureDocumentDTO couldn't be updated.
     *         返回状态码为200(成功)的响应实体，包含更新后的签名文档DTO；
     *         如果签名文档DTO无效则返回状态码400(错误请求)；
     *         如果找不到签名文档DTO则返回状态码404(未找到)；
     *         如果无法更新签名文档DTO则返回状态码500(服务器内部错误)
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确则抛出异常
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<SignatureDocumentDTO> partialUpdateSignatureDocument(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody SignatureDocumentDTO signatureDocumentDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update SignatureDocument partially : {}, {}", id, signatureDocumentDTO);
        if (signatureDocumentDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, signatureDocumentDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!signatureDocumentRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<SignatureDocumentDTO> result = signatureDocumentService.partialUpdate(signatureDocumentDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, signatureDocumentDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /signature-documents} : get all the signatureDocuments.
     * 获取所有签名文档
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of signatureDocuments in body.
     *         返回状态码为200(成功)的响应实体，包含签名文档列表
     */
    @GetMapping("")
    public ResponseEntity<List<SignatureDocumentDTO>> getAllSignatureDocuments(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of SignatureDocuments");
        Page<SignatureDocumentDTO> page = signatureDocumentService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /signature-documents/:id} : get the "id" signatureDocument.
     * 获取指定ID的签名文档
     *
     * @param id the id of the signatureDocumentDTO to retrieve. 要检索的签名文档DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the signatureDocumentDTO, or with status {@code 404 (Not Found)}.
     *         返回状态码为200(成功)的响应实体，包含签名文档DTO，如果未找到则返回状态码404(未找到)
     */
    @GetMapping("/{id}")
    public ResponseEntity<SignatureDocumentDTO> getSignatureDocument(@PathVariable("id") Long id) {
        LOG.debug("REST request to get SignatureDocument : {}", id);
        Optional<SignatureDocumentDTO> signatureDocumentDTO = signatureDocumentService.findOne(id);
        return ResponseUtil.wrapOrNotFound(signatureDocumentDTO);
    }

    /**
     * {@code DELETE  /signature-documents/:id} : delete the "id" signatureDocument.
     * 删除指定ID的签名文档
     *
     * @param id the id of the signatureDocumentDTO to delete. 要删除的签名文档DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     *         返回状态码为204(无内容)的响应实体
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSignatureDocument(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete SignatureDocument : {}", id);
        signatureDocumentService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
