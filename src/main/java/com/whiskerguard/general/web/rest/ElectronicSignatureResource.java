package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.sensitive.matcher.MatchResult;
import com.whiskerguard.general.service.SensitiveContentService;
import com.whiskerguard.general.service.SignatureDocumentService;
import com.whiskerguard.general.service.dto.SignatureCallbackDTO;
import com.whiskerguard.general.service.dto.SignatureDocumentDTO;
import com.whiskerguard.general.service.dto.SignatureRequestDTO;
import com.whiskerguard.general.service.dto.SignatureResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 电子签名管理
 * <p>
 * 提供电子签名相关的API接口，包括创建签名任务、查询签名状态、处理回调等功能。
 * </p>
 *
 * <AUTHOR> Yan
 */
@RestController
@RequestMapping("/api/signature")
@Tag(name = "电子签名", description = "电子签名相关接口")
public class ElectronicSignatureResource {

    private static final Logger log = LoggerFactory.getLogger(ElectronicSignatureResource.class);

    private final SignatureDocumentService signatureDocumentService;

    public ElectronicSignatureResource(SignatureDocumentService signatureDocumentService) {
        this.signatureDocumentService = signatureDocumentService;
    }

    /**
     * POST /api/signature/create : 创建签名任务
     *
     * @param request 签名请求参数
     * @return 签名结果
     */
    @PostMapping("/create")
    @Operation(
        summary = "创建签名任务",
        description = "根据请求参数创建一个新的签名任务，并返回签名结果，包括签名URL、任务ID等信息",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "成功创建签名任务",
                content = @Content(schema = @Schema(implementation = SignatureResultDTO.class))
            ),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<SignatureResultDTO> createSignatureTask(@Valid @RequestBody SignatureRequestDTO request) {
        log.debug("REST请求创建签名任务: {}", request);
        SignatureResultDTO result = signatureDocumentService.createSignatureTask(request);
        return ResponseEntity.ok(result);
    }

    /**
     * GET /api/signature/{id}/status : 查询签名状态
     *
     * @param id 签名文档ID
     * @return 签名文档
     */
    @GetMapping("/{id}/status")
    @Operation(
        summary = "查询签名状态",
        description = "根据签名文档ID查询当前签名任务的状态",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "成功查询签名状态",
                content = @Content(schema = @Schema(implementation = SignatureDocumentDTO.class))
            ),
            @ApiResponse(responseCode = "404", description = "签名文档不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<SignatureDocumentDTO> querySignatureStatus(@PathVariable Long id) {
        log.debug("REST请求查询签名状态: {}", id);
        SignatureDocumentDTO result = signatureDocumentService.querySignatureStatus(id);
        if (result == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(result);
    }

    /**
     * POST /api/signature/callback/{provider} : 处理签名回调
     *
     * @param provider 签名服务提供商
     * @param callbackDTO 回调数据
     * @param request HTTP请求
     * @return 处理结果
     */
    @PostMapping("/callback/{provider}")
    @Operation(
        summary = "处理签名回调",
        description = "处理签名服务商的回调请求，更新签名文档状态",
        responses = {
            @ApiResponse(responseCode = "200", description = "成功处理回调"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<Map<String, Object>> handleCallback(
        @Parameter(description = "签名服务提供商", required = true) @PathVariable String provider,
        @RequestBody(required = false) SignatureCallbackDTO callbackDTO,
        HttpServletRequest request
    ) {
        log.debug("REST请求处理签名回调: {}, {}", provider, callbackDTO);

        // 获取所有请求参数
        Map<String, String> params = new HashMap<>();
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String name = paramNames.nextElement();
            params.put(name, request.getParameter(name));
        }

        // 获取所有请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            params.put("header_" + name, request.getHeader(name));
        }

        Map<String, Object> result = signatureDocumentService.handleCallback(provider, callbackDTO, params);
        return ResponseEntity.ok(result);
    }

    /**
     * GET /api/signature/{id}/document : 获取签名后的文档
     *
     * @param id 签名文档ID
     * @return 签名后的文档URL
     */
    @GetMapping("/{id}/document")
    @Operation(
        summary = "获取签名后的文档",
        description = "根据签名文档ID获取签名后的文档URL",
        responses = {
            @ApiResponse(responseCode = "200", description = "成功获取签名后的文档"),
            @ApiResponse(responseCode = "404", description = "签名文档不存在或未签署"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<Map<String, String>> getSignedDocument(@PathVariable Long id) {
        log.debug("REST请求获取签名后的文档: {}", id);
        String documentUrl = signatureDocumentService.getSignedDocument(id);
        if (documentUrl == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(Map.of("documentUrl", documentUrl));
    }

    /**
     * POST /api/signature/{id}/cancel : 取消签名任务
     *
     * @param id 签名文档ID
     * @return 取消结果
     */
    @PostMapping("/{id}/cancel")
    @Operation(
        summary = "取消签名任务",
        description = "根据签名文档ID取消正在进行的签名任务",
        responses = {
            @ApiResponse(responseCode = "200", description = "成功取消签名任务"),
            @ApiResponse(responseCode = "404", description = "签名文档不存在"),
            @ApiResponse(responseCode = "400", description = "签名任务无法取消"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<Map<String, Boolean>> cancelSignatureTask(@PathVariable Long id) {
        log.debug("REST请求取消签名任务: {}", id);
        boolean result = signatureDocumentService.cancelSignatureTask(id);
        if (!result) {
            return ResponseEntity.badRequest().body(Map.of("success", false));
        }
        return ResponseEntity.ok(Map.of("success", true));
    }

    /**
     * 敏感内容操作的 REST 控制器
     * REST controller for sensitive content operations
     */
    @Slf4j
    @RestController
    @RequestMapping("/api/sensitive")
    @RequiredArgsConstructor
    public static class SensitiveContentResource {

        private final SensitiveContentService service;

        /**
         * 检查文本中是否包含敏感内容
         * Check text for sensitive content
         *
         * @param tenantId 来自请求头的租户ID
         * @param text 要检查的文本内容
         * @return 包含任何发现的敏感内容信息的匹配结果
         */
        @PostMapping("/check")
        public ResponseEntity<MatchResult> check(
            @RequestHeader(value = "X-Tenant-Id", defaultValue = "0") Long tenantId,
            @RequestBody String text
        ) {
            log.debug("REST请求：检查租户{}的文本中的敏感词 | REST request to check text for sensitive words for tenant {}", tenantId);
            return ResponseEntity.ok(service.check(tenantId, text));
        }

        /**
         * 对文本中的敏感内容进行掩码处理
         * Mask sensitive content in text
         *
         * @param tenantId 来自请求头的租户ID
         * @param text 要掩码处理的文本内容
         * @return 敏感词被替换为星号的掩码处理后文本
         */
        @PostMapping("/mask")
        public ResponseEntity<String> mask(
            @RequestHeader(value = "X-Tenant-Id", defaultValue = "0") Long tenantId,
            @RequestBody String text
        ) {
            log.debug("REST请求：掩码处理租户{}的文本中的敏感词 | REST request to mask sensitive words in text for tenant {}", tenantId);
            return ResponseEntity.ok(service.mask(tenantId, text));
        }
    }
}
