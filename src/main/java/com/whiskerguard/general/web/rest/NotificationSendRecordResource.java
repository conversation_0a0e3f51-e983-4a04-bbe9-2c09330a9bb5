package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.repository.NotificationSendRecordRepository;
import com.whiskerguard.general.service.NotificationSendRecordService;
import com.whiskerguard.general.service.dto.NotificationSendRecordDTO;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 通知发送记录
 * REST controller for managing {@link com.whiskerguard.general.domain.NotificationSendRecord}.
 */
@RestController
@RequestMapping("/api/notification-send-records")
public class NotificationSendRecordResource {

    private static final Logger LOG = LoggerFactory.getLogger(NotificationSendRecordResource.class);

    private static final String ENTITY_NAME = "whiskerguardGeneralServiceNotificationSendRecord";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NotificationSendRecordService notificationSendRecordService;

    private final NotificationSendRecordRepository notificationSendRecordRepository;

    public NotificationSendRecordResource(
        NotificationSendRecordService notificationSendRecordService,
        NotificationSendRecordRepository notificationSendRecordRepository
    ) {
        this.notificationSendRecordService = notificationSendRecordService;
        this.notificationSendRecordRepository = notificationSendRecordRepository;
    }

    /**
     * 创建新的通知发送记录
     * {@code POST  /notification-send-records} : Create a new notificationSendRecord.
     *
     * @param notificationSendRecordDTO 要创建的通知发送记录DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new notificationSendRecordDTO, or with status {@code 400 (Bad Request)} if the notificationSendRecord has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<NotificationSendRecordDTO> createNotificationSendRecord(
        @Valid @RequestBody NotificationSendRecordDTO notificationSendRecordDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save NotificationSendRecord : {}", notificationSendRecordDTO);
        if (notificationSendRecordDTO.getId() != null) {
            throw new BadRequestAlertException("A new notificationSendRecord cannot already have an ID", ENTITY_NAME, "idexists");
        }
        notificationSendRecordDTO = notificationSendRecordService.save(notificationSendRecordDTO);
        return ResponseEntity.created(new URI("/api/notification-send-records/" + notificationSendRecordDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, notificationSendRecordDTO.getId().toString()))
            .body(notificationSendRecordDTO);
    }

    /**
     * 更新现有的通知发送记录
     * {@code PUT  /notification-send-records/:id} : Updates an existing notificationSendRecord.
     *
     * @param id 要保存的通知发送记录DTO的ID
     * @param notificationSendRecordDTO 要更新的通知发送记录DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated notificationSendRecordDTO,
     * or with status {@code 400 (Bad Request)} if the notificationSendRecordDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the notificationSendRecordDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<NotificationSendRecordDTO> updateNotificationSendRecord(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NotificationSendRecordDTO notificationSendRecordDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NotificationSendRecord : {}, {}", id, notificationSendRecordDTO);
        if (notificationSendRecordDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, notificationSendRecordDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!notificationSendRecordRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        notificationSendRecordDTO = notificationSendRecordService.update(notificationSendRecordDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, notificationSendRecordDTO.getId().toString()))
            .body(notificationSendRecordDTO);
    }

    /**
     * 部分更新通知发送记录的指定字段，如果字段为空则忽略
     * {@code PATCH  /notification-send-records/:id} : Partial updates given fields of an existing notificationSendRecord, field will ignore if it is null
     *
     * @param id 要保存的通知发送记录DTO的ID
     * @param notificationSendRecordDTO 要更新的通知发送记录DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated notificationSendRecordDTO,
     * or with status {@code 400 (Bad Request)} if the notificationSendRecordDTO is not valid,
     * or with status {@code 404 (Not Found)} if the notificationSendRecordDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the notificationSendRecordDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NotificationSendRecordDTO> partialUpdateNotificationSendRecord(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody NotificationSendRecordDTO notificationSendRecordDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NotificationSendRecord partially : {}, {}", id, notificationSendRecordDTO);
        if (notificationSendRecordDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, notificationSendRecordDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!notificationSendRecordRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<NotificationSendRecordDTO> result = notificationSendRecordService.partialUpdate(notificationSendRecordDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, notificationSendRecordDTO.getId().toString())
        );
    }

    /**
     * 获取所有通知发送记录
     * {@code GET  /notification-send-records} : get all the notificationSendRecords.
     *
     * @param pageable 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of notificationSendRecords in body.
     */
    @GetMapping("")
    public ResponseEntity<List<NotificationSendRecordDTO>> getAllNotificationSendRecords(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of NotificationSendRecords");
        Page<NotificationSendRecordDTO> page = notificationSendRecordService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 获取指定ID的通知发送记录
     * {@code GET  /notification-send-records/:id} : get the "id" notificationSendRecord.
     *
     * @param id 要检索的通知发送记录DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the notificationSendRecordDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<NotificationSendRecordDTO> getNotificationSendRecord(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NotificationSendRecord : {}", id);
        Optional<NotificationSendRecordDTO> notificationSendRecordDTO = notificationSendRecordService.findOne(id);
        return ResponseUtil.wrapOrNotFound(notificationSendRecordDTO);
    }

    /**
     * 删除指定ID的通知发送记录
     * {@code DELETE  /notification-send-records/:id} : delete the "id" notificationSendRecord.
     *
     * @param id 要删除的通知发送记录DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteNotificationSendRecord(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NotificationSendRecord : {}", id);
        notificationSendRecordService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
