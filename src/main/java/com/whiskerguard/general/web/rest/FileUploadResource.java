package com.whiskerguard.general.web.rest;

/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-general-service
 * 文件名称：FileUploadResource.java
 * 包    名：com.whiskerguard.general.web.rest
 * 描    述：猫伯伯合规管家公共微服务：提供通用工具类和帮助模块、文件上传与COS操作服务
 * 作    者：[xuk]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [2025/5/6] [xuk] - 创建文件，实现基本文件上传功能
 * 2. [2025/6/24] [yanhaishui] - 新增三个接口方法用于文件操作：
 *                               readFileContent - 读取COS中的文件内容
 *                               checkFileExists - 检查COS中的文件是否存在
 *                               getFileInfo - 获取COS中文件的基本信息
 * =============================================================================
 */

import com.whiskerguard.general.config.Constants;
import com.whiskerguard.general.cos.CosService;
import com.whiskerguard.general.service.FileUploadService;
import com.whiskerguard.general.service.dto.FileOperationRequestDTO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传相关
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/6
 */
@RestController
@RequestMapping("/api/file")
public class FileUploadResource {

    private static final Logger LOG = LoggerFactory.getLogger(FileUploadResource.class);

    private final FileUploadService fileUploadService;
    private final CosService cosService;

    public FileUploadResource(FileUploadService fileUploadService, CosService cosService) {
        this.fileUploadService = fileUploadService;
        this.cosService = cosService;
    }

    /**
     * 处理文件上传请求的方法。
     *
     * @param file         要上传的文件，通过请求参数 "file" 传递。
     * @param tenantId     租户ID，通过请求参数 "tenantId" 传递。
     * @param serviceName  服务名称，用以生成buket。
     * @param categoryName 以"-"组成的类别
     * @return 如果文件上传成功，返回包含文件URL的200 OK响应；
     * 如果文件为空，返回400 Bad Request响应；
     * 如果文件类型不被允许，返回400 Bad Request响应；
     * 如果上传过程中发生异常，返回500 Internal Server Error响应。
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
        @RequestParam(name = "file") MultipartFile file,
        @RequestParam(name = "tenantId") Long tenantId,
        @RequestParam(name = "serviceName") String serviceName,
        @RequestParam(name = "categoryName", required = false) String categoryName
    ) {
        LOG.info("Receiving file upload request for file: {}", file.getOriginalFilename());
        Map<String, Object> result;
        // 验证文件是否为空
        if (file.isEmpty()) {
            LOG.warn("Attempted to upload empty file");
            result = new HashMap<>();
            result.put("message", "文件不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isAllowedFileType(contentType)) {
            LOG.warn("Invalid file type: {}", contentType);
            result = new HashMap<>();
            result.put("message", "不支持的文件类型");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            result = fileUploadService.uploadFile(file, tenantId, categoryName, serviceName);
            LOG.info("Successfully uploaded file: {}", file.getOriginalFilename());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            LOG.error("Failed to upload file: {}", file.getOriginalFilename(), e);
            result = new HashMap<>();
            result.put("message", "文件上传失败" + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取指定目录下的文件列表。
     *
     * @param tenantId     租户ID，通过请求参数 "tenantId" 传递。
     * @param serviceName  服务名称，用以生成buket。
     * @param categoryName 以"-"组成的类别
     * @param uploadTime   上传文件的时间
     * @return 包含文件URL列表的200 OK响应；
     * 如果获取过程中发生异常，返回500 Internal Server Error响应。
     */
    @GetMapping("/list")
    public ResponseEntity<List<String>> getFilesInDirectory(
        @RequestParam(name = "tenantId") Long tenantId,
        @RequestParam(name = "serviceName") String serviceName,
        @RequestParam(name = "categoryName") String categoryName,
        @RequestParam(name = "uploadTime") String uploadTime
    ) {
        LOG.info(
            "Receiving file list request for tenantId: {}, categoryName: {}, uploadTime: {},serviceName:{}",
            tenantId,
            categoryName,
            uploadTime,
            serviceName
        );
        try {
            List<String> fileUrls = fileUploadService.getFilesInDirectory(tenantId, categoryName, uploadTime, serviceName);
            return ResponseEntity.ok(fileUrls);
        } catch (Exception e) {
            LOG.error(
                "Failed to retrieve file list for tenantId: {}, categoryName: {}, uploadTime: {},serviceName:{}",
                tenantId,
                categoryName,
                uploadTime,
                serviceName,
                e
            );
            return ResponseEntity.internalServerError().body(null);
        }
    }

    private boolean isAllowedFileType(String contentType) {
        for (String allowedType : Constants.ALLOWED_FILE_TYPES) {
            if (allowedType.equalsIgnoreCase(contentType)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取指定文件在COS中的URL。
     *
     * @param key 文件的Key
     * @return 包含文件URL的200 OK响应；
     * 如果获取过程中发生异常，返回500 Internal Server Error响应。
     */
    @GetMapping("/getFileUrl")
    public ResponseEntity<String> getFileUrl(@RequestParam(name = "key", required = false) String key) {
        LOG.info("Receiving file list request for key: {}", key);
        try {
            String fileUrl = fileUploadService.getFileUrl(key);
            return ResponseEntity.ok().body(fileUrl);
        } catch (Exception e) {
            LOG.error("Failed to retrieve file list for key: {}", key, e);
            return ResponseEntity.internalServerError().body(null);
        }
    }

    /**
     * 根据文件名读取腾讯云COS中的文件内容
     * <p>
     * 通过文件名从腾讯云COS读取文件内容，支持文本文件、PDF、Word等格式。
     * 返回的内容已经过格式转换，可以直接用于AI分析。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件的文本内容，如果是二进制文件会转换为文本格式
     */
    @PostMapping("/cos/content")
    public ResponseEntity<String> readFileContent(@RequestBody FileOperationRequestDTO request) {
        LOG.info("接收到读取文件内容请求，文件名: {}, 租户ID: {}", request.getCosFileName(), request.getTenantId());

        try {
            String content = cosService.readFileContent(request);
            if ("文件不存在".equals(content)) {
                LOG.warn("文件不存在: {}", request.getCosFileName());
                return ResponseEntity.notFound().build();
            }
            LOG.info("成功读取文件内容，文件名: {}", request.getCosFileName());
            return ResponseEntity.ok(content);
        } catch (Exception e) {
            LOG.error("读取文件内容失败，文件名: {}", request.getCosFileName(), e);
            return ResponseEntity.internalServerError().body("读取文件内容失败: " + e.getMessage());
        }
    }

    /**
     * 检查腾讯云COS中的文件是否存在
     * <p>
     * 验证指定的文件名在腾讯云COS中是否存在，
     * 用于在读取文件内容前进行预检查。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件是否存在的布尔值
     */
    @PostMapping("/cos/exists")
    public ResponseEntity<Boolean> checkFileExists(@RequestBody FileOperationRequestDTO request) {
        LOG.info("接收到检查文件是否存在请求，文件名: {}, 租户ID: {}", request.getCosFileName(), request.getTenantId());

        try {
            Boolean exists = cosService.checkFileExists(request);
            LOG.info("成功检查文件是否存在，文件名: {}, 存在: {}", request.getCosFileName(), exists);
            return ResponseEntity.ok(exists);
        } catch (Exception e) {
            LOG.error("检查文件是否存在失败，文件名: {}", request.getCosFileName(), e);
            return ResponseEntity.internalServerError().body(false);
        }
    }

    /**
     * 获取文件的基本信息
     * <p>
     * 获取文件的元数据信息，包括文件大小、类型、创建时间等。
     * 用于在处理文件前了解文件的基本属性。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件信息的JSON字符串
     */
    @PostMapping("/cos/info")
    public ResponseEntity<String> getFileInfo(@RequestBody FileOperationRequestDTO request) {
        LOG.info("接收到获取文件信息请求，文件名: {}, 租户ID: {}", request.getCosFileName(), request.getTenantId());

        try {
            String fileInfo = cosService.getFileInfo(request);
            if (fileInfo.contains("\"error\"")) {
                LOG.warn("获取文件信息失败: {}", fileInfo);
                return ResponseEntity.notFound().build();
            }
            LOG.info("成功获取文件信息，文件名: {}", request.getCosFileName());
            return ResponseEntity.ok(fileInfo);
        } catch (Exception e) {
            LOG.error("获取文件信息失败，文件名: {}", request.getCosFileName(), e);
            return ResponseEntity.internalServerError().body("{\"error\": \"" + e.getMessage() + "\"}");
        }
    }
}
