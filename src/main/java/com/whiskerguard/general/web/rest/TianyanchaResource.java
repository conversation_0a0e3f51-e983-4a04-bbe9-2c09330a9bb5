package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.service.TianyanchaQueryService;
import com.whiskerguard.general.service.dto.CompanyDTO;
import com.whiskerguard.general.service.dto.CompanyVerificationResultDTO;
import com.whiskerguard.general.service.exception.TianyanchaApiException;
import jakarta.validation.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 天眼查企业查询
 * REST controller for managing Tianyancha company queries.
 */
@RestController
@RequestMapping("/api/tianyancha")
public class TianyanchaResource {

    private static final Logger log = LoggerFactory.getLogger(TianyanchaResource.class);

    private final TianyanchaQueryService tianyanchaQueryService;

    public TianyanchaResource(TianyanchaQueryService tianyanchaQueryService) {
        this.tianyanchaQueryService = tianyanchaQueryService;
    }

    /**
     * GET /api/tianyancha/company/basic-info : Get company basic information.
     *
     * @param keyword the search keyword (company name, ID, registration number, or credit code)
     * @return the ResponseEntity with status 200 (OK) and the company information in body,
     * or with status 404 (Not Found) if the company is not found,
     * or with status 500 (Internal Server Error) if there's an API error
     *
     *  获取企业基本信息
     * GET /api/tianyancha/company/basic-info : 。
     *
     * @param keyword 搜索关键词（企业名称、ID、注册号或统一社会信用代码）
     * @return 状态为200（OK）的ResponseEntity，包含企业信息，
     * 或者如果未找到企业，则为状态404（Not Found），
     * 或者如果出现API错误，则为状态500（Internal Server Error）
     */
    @GetMapping("/company/basic-info")
    public ResponseEntity<CompanyDTO> getCompanyBasicInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company basic info for keyword: {}", keyword);

        try {
            CompanyDTO company = tianyanchaQueryService.getCompanyBasicInfo(keyword);
            return ResponseEntity.ok(company);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company basic info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("Unexpected error getting company basic info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * POST /api/tianyancha/company/verify : Verify company three elements.
     *
     * @param request the verification request containing company name, credit code, and legal person name
     * @return the ResponseEntity with status 200 (OK) and the verification result in body,
     * or with status 400 (Bad Request) if the request is invalid,
     * or with status 500 (Internal Server Error) if there's an API error
     *
     * 验证企业三要素
     *
     * POST /api/tianyancha/company/verify : 。
     *
     * @param request 包含企业名称、统一社会信用代码和法人姓名的验证请求
     * @return 状态为200（OK）的ResponseEntity，包含验证结果，
     * 或者如果请求无效，则为状态400（Bad Request），
     * 或者如果出现API错误，则为状态500（Internal Server Error）
     */
    @PostMapping("/company/verify")
    public ResponseEntity<CompanyVerificationResultDTO> verifyCompanyThreeElements(@RequestBody CompanyVerificationRequest request) {
        log.debug("REST request to verify company three elements: {}", request);

        if (
            request.getCompanyName() == null ||
            request.getCompanyName().trim().isEmpty() ||
            request.getCreditCode() == null ||
            request.getCreditCode().trim().isEmpty() ||
            request.getLegalPersonName() == null ||
            request.getLegalPersonName().trim().isEmpty()
        ) {
            return ResponseEntity.badRequest().build();
        }

        try {
            CompanyVerificationResultDTO result = tianyanchaQueryService.verifyCompanyThreeElements(
                request.getCompanyName().trim(),
                request.getCreditCode().trim(),
                request.getLegalPersonName().trim()
            );
            return ResponseEntity.ok(result);
        } catch (TianyanchaApiException e) {
            log.error("Error verifying company three elements: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("Unexpected error verifying company three elements: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * GET /api/tianyancha/company/risk : Get company risk information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the risk information in body
     *
     * 获取企业风险信息
     * GET /api/tianyancha/company/risk : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含风险信息
     */
    @GetMapping("/company/risk")
    public ResponseEntity<String> getCompanyRiskInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company risk info for keyword: {}", keyword);

        try {
            String riskInfo = tianyanchaQueryService.getCompanyRiskInfo(keyword);
            return ResponseEntity.ok(riskInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company risk info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company risk info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/contact : Get company contact information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the contact information in body
     *
     * 获取企业联系方式信息
     * GET /api/tianyancha/company/contact : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含联系方式信息
     */
    @GetMapping("/company/contact")
    public ResponseEntity<String> getCompanyContactInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company contact info for keyword: {}", keyword);

        try {
            String contactInfo = tianyanchaQueryService.getCompanyContactInfo(keyword);
            return ResponseEntity.ok(contactInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company contact info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company contact info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/change-records : Get company change records.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the change records in body
     *
     * 获取企业变更记录
     * GET /api/tianyancha/company/change-records : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含变更记录
     */
    @GetMapping("/company/change-records")
    public ResponseEntity<String> getCompanyChangeRecords(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company change records for keyword: {}", keyword);

        try {
            String changeRecords = tianyanchaQueryService.getCompanyChangeRecords(keyword);
            return ResponseEntity.ok(changeRecords);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company change records for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company change records for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/type : Get company type information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the company type information in body
     *
     * 获取企业类型信息
     * GET /api/tianyancha/company/type : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含企业类型信息
     */
    @GetMapping("/company/type")
    public ResponseEntity<String> getCompanyTypeInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company type info for keyword: {}", keyword);

        try {
            String typeInfo = tianyanchaQueryService.getCompanyTypeInfo(keyword);
            return ResponseEntity.ok(typeInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company type info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company type info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/business : Get company industrial and commercial information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the business information in body
     *
     * 获取企业工商信息
     * GET /api/tianyancha/company/business : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含工商信息
     */
    @GetMapping("/company/business")
    public ResponseEntity<String> getCompanyBusinessInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company business info for keyword: {}", keyword);

        try {
            String businessInfo = tianyanchaQueryService.getCompanyBusinessInfo(keyword);
            return ResponseEntity.ok(businessInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company business info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company business info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/dishonest-persons : Get company dishonest person records.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the dishonest person records in body
     *
     * 获取企业失信人记录
     * GET /api/tianyancha/company/dishonest-persons : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）ResponseEntity，包含失信人记录
     */
    @GetMapping("/company/dishonest-persons")
    public ResponseEntity<String> getCompanyDishonestPersons(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company dishonest persons for keyword: {}", keyword);

        try {
            String dishonestPersons = tianyanchaQueryService.getCompanyDishonestPersons(keyword);
            return ResponseEntity.ok(dishonestPersons);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company dishonest persons for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company dishonest persons for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/case-filings : Get company case filing information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the case filing information in body
     *
     * 获取企业立案信息
     * GET /api/tianyancha/company/case-filings : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含立案信息
     */
    @GetMapping("/company/case-filings")
    public ResponseEntity<String> getCompanyCaseFilings(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company case filings for keyword: {}", keyword);

        try {
            String caseFilings = tianyanchaQueryService.getCompanyCaseFilings(keyword);
            return ResponseEntity.ok(caseFilings);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company case filings for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company case filings for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * POST /api/tianyancha/company/refresh : Force refresh company data from Tianyancha API.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the refreshed company information in body
     *
     * 强制从天眼查API刷新企业数据
     * POST /api/tianyancha/company/refresh : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含刷新后的企业信息
     */
    @PostMapping("/company/refresh")
    public ResponseEntity<CompanyDTO> refreshCompanyData(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to force refresh company data for keyword: {}", keyword);

        try {
            CompanyDTO company = tianyanchaQueryService.refreshCompanyData(keyword);
            return ResponseEntity.ok(company);
        } catch (TianyanchaApiException e) {
            log.error("Error refreshing company data for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("Unexpected error refreshing company data for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * GET /api/tianyancha/company/lawsuit : Get company lawsuit information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the lawsuit information in body
     *
     * 获取企业诉讼信息
     * GET /api/tianyancha/company/lawsuit : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含诉讼信息
     */
    @GetMapping("/company/lawsuit")
    public ResponseEntity<String> getCompanyLawsuitInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company lawsuit info for keyword: {}", keyword);

        try {
            String lawsuitInfo = tianyanchaQueryService.getCompanyLawsuitInfo(keyword);
            return ResponseEntity.ok(lawsuitInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company lawsuit info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company lawsuit info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/credit : Get company credit information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the credit information in body
     *
     * 获取企业信用信息
     * GET /api/tianyancha/company/credit : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含信用信息
     */
    @GetMapping("/company/credit")
    public ResponseEntity<String> getCompanyCreditInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company credit info for keyword: {}", keyword);

        try {
            String creditInfo = tianyanchaQueryService.getCompanyCreditInfo(keyword);
            return ResponseEntity.ok(creditInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company credit info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company credit info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/shareholders : Get company shareholder information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the shareholder information in body
     *
     * 获取企业股东信息
     * GET /api/tianyancha/company/shareholders : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含股东信息
     */
    @GetMapping("/company/shareholders")
    public ResponseEntity<String> getCompanyShareholderInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company shareholder info for keyword: {}", keyword);

        try {
            String shareholderInfo = tianyanchaQueryService.getCompanyShareholderInfo(keyword);
            return ResponseEntity.ok(shareholderInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company shareholder info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company shareholder info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/batch : Batch get company information.
     *
     * @param companyNames the comma-separated company names
     * @return the ResponseEntity with status 200 (OK) and the batch company information in body
     *
     * 批量查询企业信息
     * GET /api/tianyancha/company/batch : 。
     *
     * @param companyNames 逗号分隔的企业名称列表
     * @return 状态为200（OK）的ResponseEntity，包含批量企业信息
     */
    @GetMapping("/company/batch")
    public ResponseEntity<String> batchGetCompanyInfo(@RequestParam @NotBlank String companyNames) {
        log.debug("REST request to batch get company info for companies: {}", companyNames);

        try {
            String batchInfo = tianyanchaQueryService.batchGetCompanyInfo(companyNames);
            return ResponseEntity.ok(batchInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error batch getting company info for companies {}: {}", companyNames, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error batch getting company info for companies {}: {}", companyNames, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/exists : Check if company exists.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the existence result in body
     *
     * 检查企业是否存在
     * GET /api/tianyancha/company/exists : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含存在性检查结果
     */
    @GetMapping("/company/exists")
    public ResponseEntity<Boolean> checkCompanyExists(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to check if company exists for keyword: {}", keyword);

        try {
            Boolean exists = tianyanchaQueryService.checkCompanyExists(keyword);
            return ResponseEntity.ok(exists);
        } catch (TianyanchaApiException e) {
            log.error("Error checking company existence for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        } catch (Exception e) {
            log.error("Unexpected error checking company existence for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        }
    }

    /**
     * GET /api/tianyancha/company/execution : Get company execution information.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the execution information in body
     *
     * 获取企业被执行人信息
     * GET /api/tianyancha/company/execution : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含被执行人信息
     */
    @GetMapping("/company/execution")
    public ResponseEntity<String> getCompanyExecutionInfo(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company execution info for keyword: {}", keyword);

        try {
            String executionInfo = tianyanchaQueryService.getCompanyExecutionInfo(keyword);
            return ResponseEntity.ok(executionInfo);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company execution info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company execution info for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * GET /api/tianyancha/company/penalties : Get company administrative penalties.
     *
     * @param keyword the search keyword
     * @return the ResponseEntity with status 200 (OK) and the penalties information in body
     *
     * 获取企业行政处罚信息
     * GET /api/tianyancha/company/penalties : 。
     *
     * @param keyword 搜索关键词
     * @return 状态为200（OK）的ResponseEntity，包含行政处罚信息
     */
    @GetMapping("/company/penalties")
    public ResponseEntity<String> getCompanyPenalties(@RequestParam @NotBlank String keyword) {
        log.debug("REST request to get company penalties for keyword: {}", keyword);

        try {
            String penalties = tianyanchaQueryService.getCompanyPenalties(keyword);
            return ResponseEntity.ok(penalties);
        } catch (TianyanchaApiException e) {
            log.error("Error getting company penalties for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"" + e.getMessage() + "\"}");
        } catch (Exception e) {
            log.error("Unexpected error getting company penalties for keyword {}: {}", keyword, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("{\"error\":\"Internal server error\"}");
        }
    }

    /**
     * Request DTO for company three elements verification.
     *
     * 企业三要素验证的请求DTO。
     */
    public static class CompanyVerificationRequest {

        private String companyName; // 企业名称
        private String creditCode; // 统一社会信用代码
        private String legalPersonName; // 法人姓名

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        public String getCreditCode() {
            return creditCode;
        }

        public void setCreditCode(String creditCode) {
            this.creditCode = creditCode;
        }

        public String getLegalPersonName() {
            return legalPersonName;
        }

        public void setLegalPersonName(String legalPersonName) {
            this.legalPersonName = legalPersonName;
        }

        @Override
        public String toString() {
            return (
                "CompanyVerificationRequest{" +
                "companyName='" +
                companyName +
                '\'' +
                ", creditCode='" +
                creditCode +
                '\'' +
                ", legalPersonName='" +
                legalPersonName +
                '\'' +
                '}'
            );
        }
    }
}
