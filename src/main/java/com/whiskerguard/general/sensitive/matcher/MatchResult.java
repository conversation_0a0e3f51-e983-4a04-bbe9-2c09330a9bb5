package com.whiskerguard.general.sensitive.matcher;

import com.whiskerguard.general.domain.enumeration.SeverityType;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 敏感内容匹配结果数据传输对象
 * DTO representing the results of a sensitive content match operation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchResult {

    /**
     * 内容是否应该被屏蔽（基于匹配结果）
     * Whether the content should be blocked based on matches
     */
    private boolean blocked;

    /**
     * 匹配到的敏感词列表
     * List of matched sensitive terms
     */
    @Builder.Default
    private List<String> terms = new ArrayList<>();

    /**
     * 匹配结果中的最高严重级别
     * The highest severity level found among matches
     */
    private SeverityType severity;
}
