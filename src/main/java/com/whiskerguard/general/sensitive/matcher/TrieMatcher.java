package com.whiskerguard.general.sensitive.matcher;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.whiskerguard.general.domain.SensitiveWord;
import com.whiskerguard.general.domain.enumeration.SeverityType;
import com.whiskerguard.general.repository.SensitiveWordRepository;
import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.ahocorasick.trie.Emit;
import org.ahocorasick.trie.Trie;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 高效匹配字符串中敏感词的服务
 * 使用Aho-Corasick算法实现高效的字符串匹配
 *
 * Service to efficiently match text against a dictionary of sensitive words.
 * Uses Aho-Corasick algorithm for efficient string matching.
 */
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "sensitiveTrie")
public class TrieMatcher {

    private static final Logger log = LoggerFactory.getLogger(TrieMatcher.class);

    private final SensitiveWordRepository repo;

    /**
     * 平台级字典树（AC自动机）
     * Platform-level trie (AC automaton)
     */
    private volatile Trie trie;

    /**
     * 用于匹配后快速查找敏感词对象的内存映射
     * In-memory cache mapping terms to sensitive word objects for fast lookup after matching
     */
    private Map<String, SensitiveWord> wordMap = new HashMap<>();

    /**
     * 租户特定字典树的缓存
     * Cache of tenant-specific tries
     */
    private LoadingCache<Long, Trie> tenantTries;

    /**
     * 初始化方法，在服务启动时装配字典树
     * Initialization method, constructs the trie when service starts
     */
    @PostConstruct
    public void init() {
        // 初始化租户字典树缓存
        // Initialize tenant trie cache
        tenantTries = Caffeine.newBuilder().maximumSize(50).expireAfterWrite(30, TimeUnit.SECONDS).build(this::buildTenantTrie);

        // 初始化平台级字典树（租户ID为0）
        // Initialize platform-level trie (tenant ID 0)
        rebuild(0L);
        log.info("初始化平台级敏感词字典树完成 | Initialized platform-level sensitive word trie");
    }

    /**
     * 构建或重建特定租户的字典树
     * Builds or rebuilds the trie for a specific tenant
     *
     * @param tenantId 租户ID
     */
    @Cacheable(key = "#tenantId")
    public void rebuild(Long tenantId) {
        log.debug("为租户{}构建敏感词字典树 | Building sensitive word trie for tenant {}", tenantId);

        // 获取该租户的所有有效敏感词
        // Get all valid sensitive words for the tenant
        List<SensitiveWord> words = repo.findValidByTenant(tenantId);

        // 构建词条到敏感词对象的映射
        // Build map of terms to sensitive word objects
        Map<String, SensitiveWord> map = words
            .stream()
            .collect(
                Collectors.toMap(SensitiveWord::getTerm, Function.identity(), (existing, replacement) -> {
                    // 如果存在重复词条，保留级别更高的那个
                    // If duplicate terms exist, keep the one with higher severity
                    return existing.getSeverity().ordinal() >= replacement.getSeverity().ordinal() ? existing : replacement;
                })
            );

        // 更新该租户的词映射
        // Update the word map for this tenant
        synchronized (this) {
            wordMap.putAll(map);
        }

        // 构建字典树
        // Build the trie
        Trie newTrie = buildTrie(map.keySet());

        // 更新volatile引用以确保线程安全
        // Update the volatile reference to ensure thread safety
        if (tenantId == 0L) {
            trie = newTrie;
        }

        log.debug(
            "已为租户{}构建包含{}个词条的敏感词字典树 | Built sensitive word trie with {} terms for tenant {}",
            tenantId,
            map.size(),
            map.size(),
            tenantId
        );
    }

    /**
     * 构建租户特定的字典树
     * Build a tenant-specific trie
     *
     * @param tenantId 租户ID
     * @return 构建的字典树
     */
    private Trie buildTenantTrie(Long tenantId) {
        List<SensitiveWord> words = repo.findValidByTenant(tenantId);
        Set<String> terms = words.stream().map(SensitiveWord::getTerm).collect(Collectors.toSet());

        // 更新该租户的词映射
        // Update the word map for this tenant
        Map<String, SensitiveWord> map = words
            .stream()
            .collect(
                Collectors.toMap(SensitiveWord::getTerm, Function.identity(), (existing, replacement) -> {
                    return existing.getSeverity().ordinal() >= replacement.getSeverity().ordinal() ? existing : replacement;
                })
            );
        synchronized (this) {
            wordMap.putAll(map);
        }

        return buildTrie(terms);
    }

    /**
     * 从词条集合构建字典树
     * Build a trie from a set of terms
     *
     * @param terms 敏感词条集合
     * @return 构建的字典树
     */
    private Trie buildTrie(Set<String> terms) {
        return Trie.builder().addKeywords(terms).ignoreCase().build();
    }

    /**
     * 检查文本中的敏感词（使用平台级字典树）
     * Check a text for sensitive words using platform-level trie
     *
     * @param text 待检查文本
     * @return 匹配结果，包含检测到的敏感词和最高严重级别
     */
    public MatchResult check(String text) {
        if (text == null || text.isBlank()) {
            return new MatchResult(false, new ArrayList<>(), null);
        }

        // 使用平台级字典树（默认）
        // Use the platform-level trie (default)
        Collection<Emit> emits = trie.parseText(text);
        return processEmits(emits, text);
    }

    /**
     * 使用租户特定字典树检查文本中的敏感词
     * Check a text for sensitive words using a tenant-specific trie
     *
     * @param tenantId 租户ID
     * @param text 待检查文本
     * @return 匹配结果，包含检测到的敏感词和最高严重级别
     */
    public MatchResult check(Long tenantId, String text) {
        if (text == null || text.isBlank()) {
            return new MatchResult(false, new ArrayList<>(), null);
        }

        // 使用租户特定的字典树（如可用），否则使用平台级字典树
        // Use tenant-specific trie if available, otherwise use platform trie
        Trie tenantTrie = tenantTries.get(tenantId);
        Collection<Emit> emits = tenantTrie.parseText(text);
        return processEmits(emits, text);
    }

    /**
     * 处理字典树匹配结果，生成匹配结果对象
     * Process the emits from the trie to create a MatchResult
     *
     * @param emits 字典树匹配结果
     * @param text 原始文本，用于检查词边界
     * @return 处理后的匹配结果对象
     */
    private MatchResult processEmits(Collection<Emit> emits, String text) {
        if (emits.isEmpty()) {
            return new MatchResult(false, new ArrayList<>(), null);
        }

        List<String> terms = new ArrayList<>();
        SeverityType highestSeverity = SeverityType.REPLACE; // 默认使用最低级别

        for (Emit emit : emits) {
            String term = emit.getKeyword();

            // 检查词边界，确保这是一个完整的词而不是部分匹配
            // Check word boundaries to ensure this is a complete word, not a partial match
            if (isWholeWordMatch(text, emit.getStart(), emit.getEnd(), term)) {
                terms.add(term);

                // 查找敏感级别
                // Look up the sensitivity level
                SensitiveWord word = wordMap.get(term);
                if (word != null) {
                    // 判断严重级别，BLOCK > REVIEW > REPLACE
                    SeverityType wordSeverity = word.getSeverity();
                    if (
                        wordSeverity == SeverityType.BLOCK ||
                        (wordSeverity == SeverityType.REVIEW && highestSeverity == SeverityType.REPLACE)
                    ) {
                        highestSeverity = wordSeverity;
                    }
                }
            }
        }

        // 确定内容是否应该被屏蔽（BLOCK级别）
        // Determine if content should be blocked (BLOCK severity)
        boolean blocked = highestSeverity == SeverityType.BLOCK;

        return new MatchResult(blocked, terms, highestSeverity);
    }

    /**
     * 检查匹配是否为完整词匹配
     * Check if the match is a whole word match
     *
     * @param text 原始文本
     * @param start 匹配开始位置
     * @param end 匹配结束位置
     * @param term 匹配的词
     * @return 是否为完整词匹配
     */
    private boolean isWholeWordMatch(String text, int start, int end, String term) {
        // 检查前一个字符是否为词边界
        // Check if the character before is a word boundary
        if (start > 0) {
            char prevChar = text.charAt(start - 1);
            if (Character.isLetterOrDigit(prevChar)) {
                return false;
            }
        }

        // 检查后一个字符是否为词边界
        // Check if the character after is a word boundary
        if (end < text.length() - 1) {
            char nextChar = text.charAt(end + 1);
            if (Character.isLetterOrDigit(nextChar)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 将文本中的敏感词替换为指定的掩码字符
     * Mask sensitive words in text with the specified character
     *
     * @param text 待掩码文本
     * @param maskChar 掩码字符
     * @return 掩码处理后的文本
     */
    public String mask(String text, char maskChar) {
        if (text == null || text.isBlank()) {
            return text;
        }

        Collection<Emit> emits = trie.parseText(text);
        if (emits.isEmpty()) {
            return text;
        }

        // 创建原始文本的字符数组副本
        // Create a char array copy of the original text
        char[] chars = text.toCharArray();

        // 对每个匹配，用掩码字符替换（只处理完整词匹配）
        // For each match, replace characters with mask character (only for whole word matches)
        for (Emit emit : emits) {
            String term = emit.getKeyword();
            int start = emit.getStart();
            int end = emit.getEnd();

            // 只处理完整词匹配
            // Only process whole word matches
            if (isWholeWordMatch(text, start, end, term)) {
                for (int i = start; i <= end; i++) {
                    chars[i] = maskChar;
                }
            }
        }

        return new String(chars);
    }

    /**
     * 使用特定租户的字典树对文本中的敏感词进行掩码处理
     * Mask sensitive words in text for a specific tenant
     *
     * @param tenantId 租户ID
     * @param text 待掩码文本
     * @param maskChar 掩码字符
     * @return 掩码处理后的文本
     */
    public String mask(Long tenantId, String text, char maskChar) {
        if (text == null || text.isBlank()) {
            return text;
        }

        Trie tenantTrie = tenantTries.get(tenantId);
        Collection<Emit> emits = tenantTrie.parseText(text);

        if (emits.isEmpty()) {
            return text;
        }

        // 创建原始文本的字符数组副本
        // Create a char array copy of the original text
        char[] chars = text.toCharArray();

        // 对每个匹配，用掩码字符替换（只处理完整词匹配）
        // For each match, replace characters with mask character (only for whole word matches)
        for (Emit emit : emits) {
            String term = emit.getKeyword();
            int start = emit.getStart();
            int end = emit.getEnd();

            // 只处理完整词匹配
            // Only process whole word matches
            if (isWholeWordMatch(text, start, end, term)) {
                for (int i = start; i <= end; i++) {
                    chars[i] = maskChar;
                }
            }
        }

        return new String(chars);
    }

    /**
     * 重新加载特定租户的敏感词字典树
     * 这将清除缓存的字典树
     *
     * Reload the sensitive word trie for a specific tenant.
     * This will evict the cached trie.
     *
     * @param tenantId 要重新加载的租户ID
     */
    @CacheEvict(key = "#tenantId")
    public void reload(Long tenantId) {
        log.info("重新加载租户{}的敏感词字典树 | Reloading sensitive word trie for tenant {}", tenantId);
        rebuild(tenantId);
    }
}
