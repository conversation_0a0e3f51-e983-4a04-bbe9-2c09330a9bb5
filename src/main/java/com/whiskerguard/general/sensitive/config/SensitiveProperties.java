package com.whiskerguard.general.sensitive.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 敏感词配置属性类
 * 用于绑定 application.yml 中的 sensitive 前缀的配置项
 */
@Data
@ConfigurationProperties(prefix = "sensitive")
public class SensitiveProperties {

    /**
     * 敏感词库重载定时表达式
     * Cron expression for reloading sensitive word library
     */
    private String reloadCron = "0 0/5 * * * ?";

    /**
     * 默认的敏感词掩码字符
     * Default character used to mask sensitive words
     */
    private char defaultMaskChar = '*';
}
