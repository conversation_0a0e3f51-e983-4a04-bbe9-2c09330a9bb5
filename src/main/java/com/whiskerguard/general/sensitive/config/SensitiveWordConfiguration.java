package com.whiskerguard.general.sensitive.config;

import com.whiskerguard.general.sensitive.matcher.TrieMatcher;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 敏感词处理配置类
 * Configuration class for sensitive word handling
 */
@Configuration
@EnableScheduling
@EnableCaching
@EnableConfigurationProperties(SensitiveProperties.class)
@RequiredArgsConstructor
public class SensitiveWordConfiguration {

    private static final Logger log = LoggerFactory.getLogger(SensitiveWordConfiguration.class);

    private final TrieMatcher matcher;
    private final SensitiveProperties props;
    private final CacheManager cacheManager;

    /**
     * 定时重新加载平台级敏感词字典树
     * Scheduled reload of the platform-level sensitive word trie
     */
    @Scheduled(cron = "${sensitive.reload-cron}")
    public void scheduledReload() {
        log.debug("执行定时重载平台级敏感词库 | Executing scheduled reload of platform-level sensitive words");
        matcher.reload(0L);
    }

    /**
     * 清除所有敏感词缓存
     * Clear all sensitive word caches
     * @return 缓存是否已清除
     */
    public boolean clearAllCaches() {
        log.info("清除所有敏感词缓存 | Clearing all sensitive word caches");
        cacheManager
            .getCacheNames()
            .stream()
            .filter(name -> name.startsWith("sensitive"))
            .forEach(cacheName -> cacheManager.getCache(cacheName).clear());
        return true;
    }
}
