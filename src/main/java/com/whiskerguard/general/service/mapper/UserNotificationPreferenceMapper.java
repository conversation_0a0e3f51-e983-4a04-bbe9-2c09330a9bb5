package com.whiskerguard.general.service.mapper;

import com.whiskerguard.general.domain.UserNotificationPreference;
import com.whiskerguard.general.service.dto.UserNotificationPreferenceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link UserNotificationPreference} and its DTO {@link UserNotificationPreferenceDTO}.
 */
@Mapper(componentModel = "spring")
public interface UserNotificationPreferenceMapper extends EntityMapper<UserNotificationPreferenceDTO, UserNotificationPreference> {}
