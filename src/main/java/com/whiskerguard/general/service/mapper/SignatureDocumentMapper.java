package com.whiskerguard.general.service.mapper;

import com.whiskerguard.general.domain.SignatureDocument;
import com.whiskerguard.general.service.dto.SignatureDocumentDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SignatureDocument} and its DTO {@link SignatureDocumentDTO}.
 */
@Mapper(componentModel = "spring")
public interface SignatureDocumentMapper extends EntityMapper<SignatureDocumentDTO, SignatureDocument> {}
