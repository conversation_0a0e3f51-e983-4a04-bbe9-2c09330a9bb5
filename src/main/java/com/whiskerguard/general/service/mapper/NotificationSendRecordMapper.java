package com.whiskerguard.general.service.mapper;

import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.NotificationSendRecord;
import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import com.whiskerguard.general.service.dto.NotificationSendRecordDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link NotificationSendRecord} and its DTO {@link NotificationSendRecordDTO}.
 */
@Mapper(componentModel = "spring")
public interface NotificationSendRecordMapper extends EntityMapper<NotificationSendRecordDTO, NotificationSendRecord> {
    @Mapping(target = "notification", source = "notification", qualifiedByName = "notificationRecordId")
    NotificationSendRecordDTO toDto(NotificationSendRecord s);

    @Named("notificationRecordId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    NotificationRecordDTO toDtoNotificationRecordId(NotificationRecord notificationRecord);
}
