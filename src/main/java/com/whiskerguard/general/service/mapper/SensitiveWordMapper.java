package com.whiskerguard.general.service.mapper;

import com.whiskerguard.general.domain.SensitiveWord;
import com.whiskerguard.general.service.dto.SensitiveWordDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link SensitiveWord} and its DTO {@link SensitiveWordDTO}.
 */
@Mapper(componentModel = "spring")
public interface SensitiveWordMapper extends EntityMapper<SensitiveWordDTO, SensitiveWord> {}
