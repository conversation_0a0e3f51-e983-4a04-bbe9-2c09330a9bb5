package com.whiskerguard.general.service.mapper;

import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.NotificationTemplate;
import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import com.whiskerguard.general.service.dto.NotificationTemplateDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link NotificationRecord} and its DTO {@link NotificationRecordDTO}.
 */
@Mapper(componentModel = "spring")
public interface NotificationRecordMapper extends EntityMapper<NotificationRecordDTO, NotificationRecord> {
    @Mapping(target = "template", source = "template", qualifiedByName = "notificationTemplateId")
    NotificationRecordDTO toDto(NotificationRecord s);

    @Named("notificationTemplateId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    NotificationTemplateDTO toDtoNotificationTemplateId(NotificationTemplate notificationTemplate);
}
