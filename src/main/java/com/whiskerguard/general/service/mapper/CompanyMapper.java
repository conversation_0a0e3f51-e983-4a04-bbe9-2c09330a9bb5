package com.whiskerguard.general.service.mapper;

import com.whiskerguard.general.domain.Company;
import com.whiskerguard.general.service.dto.CompanyDTO;
import com.whiskerguard.general.service.dto.TianyanchaCompanyBasicInfoResponseDTO;
import java.time.Instant;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Company} and its DTO {@link CompanyDTO}.
 *
 * 实体 {@link Company} 与其 DTO {@link CompanyDTO} 之间的映射器。
 */
@Mapper(componentModel = "spring")
public interface CompanyMapper {
    CompanyDTO toDto(Company company);

    @Mapping(target = "contact", ignore = true)
    @Mapping(target = "risks", ignore = true)
    @Mapping(target = "changeRecords", ignore = true)
    @Mapping(target = "dishonestPersons", ignore = true)
    @Mapping(target = "caseFilings", ignore = true)
    @Mapping(target = "removeRisk", ignore = true)
    @Mapping(target = "removeChangeRecord", ignore = true)
    @Mapping(target = "removeDishonestPerson", ignore = true)
    @Mapping(target = "removeCaseFiling", ignore = true)
    Company toEntity(CompanyDTO companyDTO);

    /**
     * Partially update a Company entity from a DTO.
     * Only non-null fields in the DTO will be updated in the entity.
     *
     * 根据DTO部分更新Company实体。
     * 只有DTO中非空的字段才会更新到实体中。
     *
     * @param entity the entity to update
     * @param dto    the DTO containing the updates
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget Company entity, CompanyDTO dto);

    /**
     * Maps Tianyancha API response to Company entity.
     *
     * 将天眼查API响应映射到Company实体。
     */
    @Mapping(source = "result.id", target = "tianyanchaId")
    @Mapping(source = "result.name", target = "name")
    @Mapping(source = "result.creditCode", target = "unifiedSocialCreditCode")
    @Mapping(source = "result.legalPersonName", target = "legalPersonName")
    @Mapping(source = "result.regStatus", target = "regStatus")
    @Mapping(source = "result.regCapital", target = "regCapital")
    @Mapping(source = "result.regCapitalCurrency", target = "regCapitalCurrency")
    @Mapping(source = "result.estiblishTime", target = "establishTime", qualifiedByName = "timestampToInstant")
    @Mapping(source = "result.companyOrgType", target = "companyOrgType")
    @Mapping(source = "result.regNumber", target = "regNumber")
    @Mapping(source = "result.taxNumber", target = "taxNumber")
    @Mapping(source = "result.orgNumber", target = "orgNumber")
    @Mapping(source = "result.industry", target = "industry")
    @Mapping(source = "result.regLocation", target = "regLocation")
    @Mapping(source = "result.businessScope", target = "businessScope")
    @Mapping(source = "result.updateTimes", target = "tianyanchaUpdateTime", qualifiedByName = "timestampToInstant")
    @Mapping(source = "result.historyNames", target = "historyNames")
    @Mapping(source = "result.cancelDate", target = "cancelDate", qualifiedByName = "timestampToInstant")
    @Mapping(source = "result.revokeDate", target = "revokeDate", qualifiedByName = "timestampToInstant")
    @Mapping(source = "result.revokeReason", target = "revokeReason")
    @Mapping(source = "result.cancelReason", target = "cancelReason")
    @Mapping(source = "result.approvedTime", target = "approvedTime", qualifiedByName = "timestampToInstant")
    @Mapping(source = "result.fromTime", target = "fromTime", qualifiedByName = "timestampToInstant")
    @Mapping(source = "result.toTime", target = "toTime", qualifiedByName = "timestampToInstant")
    @Mapping(source = "result.actualCapital", target = "actualCapital")
    @Mapping(source = "result.actualCapitalCurrency", target = "actualCapitalCurrency")
    @Mapping(source = "result.regInstitute", target = "regInstitute")
    @Mapping(source = "result.city", target = "city")
    @Mapping(source = "result.district", target = "district")
    @Mapping(source = "result.staffNumRange", target = "staffNumRange")
    @Mapping(source = "result.socialStaffNum", target = "socialStaffNum")
    @Mapping(source = "result.bondNum", target = "bondNum")
    @Mapping(source = "result.bondName", target = "bondName")
    @Mapping(source = "result.bondType", target = "bondType")
    @Mapping(source = "result.usedBondName", target = "usedBondName")
    @Mapping(source = "result.alias", target = "alias")
    @Mapping(source = "result.property3", target = "property3")
    @Mapping(source = "result.tags", target = "tags")
    @Mapping(source = "result.percentileScore", target = "percentileScore")
    @Mapping(source = "result.isMicroEnt", target = "isMicroEnt")
    @Mapping(source = "result.base", target = "base")
    @Mapping(source = "result.type", target = "type")
    @Mapping(source = "result.compForm", target = "compForm")
    @Mapping(target = "industryCategory", expression = "java(extractIndustryCategory(response.getResult()))")
    @Mapping(target = "industryCategoryBig", expression = "java(extractIndustryCategoryBig(response.getResult()))")
    @Mapping(target = "industryCategoryMiddle", expression = "java(extractIndustryCategoryMiddle(response.getResult()))")
    @Mapping(target = "industryCategorySmall", expression = "java(extractIndustryCategorySmall(response.getResult()))")
    @Mapping(target = "cacheTime", expression = "java(java.time.Instant.now())")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "contact", ignore = true)
    @Mapping(target = "risks", ignore = true)
    @Mapping(target = "changeRecords", ignore = true)
    @Mapping(target = "dishonestPersons", ignore = true)
    @Mapping(target = "caseFilings", ignore = true)
    @Mapping(target = "removeRisk", ignore = true)
    @Mapping(target = "removeChangeRecord", ignore = true)
    @Mapping(target = "removeDishonestPerson", ignore = true)
    @Mapping(target = "removeCaseFiling", ignore = true)
    Company fromTianyanchaResponse(TianyanchaCompanyBasicInfoResponseDTO response);

    @Named("timestampToInstant")
    default Instant timestampToInstant(Long timestamp) {
        return timestamp != null ? Instant.ofEpochMilli(timestamp) : null;
    }

    // Helper methods for industry information extraction
    default String extractIndustryCategory(TianyanchaCompanyBasicInfoResponseDTO.CompanyBasicInfo result) {
        return result != null && result.getIndustryAll() != null ? result.getIndustryAll().getCategory() : null;
    }

    default String extractIndustryCategoryBig(TianyanchaCompanyBasicInfoResponseDTO.CompanyBasicInfo result) {
        return result != null && result.getIndustryAll() != null ? result.getIndustryAll().getCategoryBig() : null;
    }

    default String extractIndustryCategoryMiddle(TianyanchaCompanyBasicInfoResponseDTO.CompanyBasicInfo result) {
        return result != null && result.getIndustryAll() != null ? result.getIndustryAll().getCategoryMiddle() : null;
    }

    default String extractIndustryCategorySmall(TianyanchaCompanyBasicInfoResponseDTO.CompanyBasicInfo result) {
        return result != null && result.getIndustryAll() != null ? result.getIndustryAll().getCategorySmall() : null;
    }

    default Company fromId(Long id) {
        if (id == null) {
            return null;
        }
        Company company = new Company();
        company.setId(id);
        return company;
    }
}
