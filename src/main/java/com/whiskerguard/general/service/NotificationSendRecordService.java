package com.whiskerguard.general.service;

import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.service.dto.NotificationSendRecordDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.general.domain.NotificationSendRecord}.
 */
public interface NotificationSendRecordService {
    /**
     * Save a notificationSendRecord.
     *
     * @param notificationSendRecordDTO the entity to save.
     * @return the persisted entity.
     */
    NotificationSendRecordDTO save(NotificationSendRecordDTO notificationSendRecordDTO);

    /**
     * Updates a notificationSendRecord.
     *
     * @param notificationSendRecordDTO the entity to update.
     * @return the persisted entity.
     */
    NotificationSendRecordDTO update(NotificationSendRecordDTO notificationSendRecordDTO);

    /**
     * Partially updates a notificationSendRecord.
     *
     * @param notificationSendRecordDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<NotificationSendRecordDTO> partialUpdate(NotificationSendRecordDTO notificationSendRecordDTO);

    /**
     * Get all the notificationSendRecords.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NotificationSendRecordDTO> findAll(Pageable pageable);

    /**
     * Get the "id" notificationSendRecord.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<NotificationSendRecordDTO> findOne(Long id);

    /**
     * Delete the "id" notificationSendRecord.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * Get all the notificationSendRecords by category.
     *
     * @param category the category to filter by.
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NotificationSendRecordDTO> findByCategory(NotificationCategory category, Pageable pageable);

    /**
     * Get all the notificationSendRecords by userId and recipientType.
     *
     * @param userId the user ID to filter by.
     * @param recipientType the recipient type to filter by.
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NotificationSendRecordDTO> findByUserIdAndCategory(Long userId, RecipientType recipientType, Pageable pageable);
}
