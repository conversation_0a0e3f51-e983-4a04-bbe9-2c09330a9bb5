package com.whiskerguard.general.service;

import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.WechatRequest;

/**
 * 微信公众号服务接口
 */
public interface WechatService {

    /**
     * 发送客服消息
     *
     * @param request 微信推送请求
     * @return 通知响应
     */
    NotificationResponse sendCustomMessage(WechatRequest request);

    /**
     * 发送模板消息
     *
     * @param request 微信推送请求
     * @return 通知响应
     */
    NotificationResponse sendTemplateMessage(WechatRequest request);

    /**
     * 获取用户信息
     *
     * @param openId 用户OpenID
     * @return 用户信息JSON字符串
     */
    String getUserInfo(String openId);

    /**
     * 获取关注者列表
     *
     * @param nextOpenId 下一个OpenID（用于分页）
     * @return 关注者列表JSON字符串
     */
    String getFollowers(String nextOpenId);

}
