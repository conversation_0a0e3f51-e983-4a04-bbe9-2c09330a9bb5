package com.whiskerguard.general.service.dto;

import java.io.Serializable;

/**
 * 文件操作请求DTO
 * <p>
 * 用于通用服务的文件操作接口，包括文件读取、存在性检查和信息获取等操作。
 *
 * <AUTHOR>
 * @since 1.0
 */
public class FileOperationRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 腾讯云COS中的文件名（包含路径）
     */
    private String cosFileName;

    /**
     * 租户ID，用于多租户数据隔离和权限控制
     */
    private Long tenantId;

    /**
     * 默认构造函数
     */
    public FileOperationRequestDTO() {}

    /**
     * 构造函数
     *
     * @param cosFileName 腾讯云COS中的文件名
     * @param tenantId 租户ID
     */
    public FileOperationRequestDTO(String cosFileName, Long tenantId) {
        this.cosFileName = cosFileName;
        this.tenantId = tenantId;
    }

    /**
     * 获取文件名
     *
     * @return 腾讯云COS中的文件名
     */
    public String getCosFileName() {
        return cosFileName;
    }

    /**
     * 设置文件名
     *
     * @param cosFileName 腾讯云COS中的文件名
     */
    public void setCosFileName(String cosFileName) {
        this.cosFileName = cosFileName;
    }

    /**
     * 获取租户ID
     *
     * @return 租户ID
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     * 设置租户ID
     *
     * @param tenantId 租户ID
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "FileOperationRequestDTO{" + "cosFileName='" + cosFileName + '\'' + ", tenantId=" + tenantId + '}';
    }
}
