package com.whiskerguard.general.service.dto;

import com.whiskerguard.general.domain.enumeration.SignatureDocumentStatus;
import com.whiskerguard.general.domain.enumeration.SignatureProvider;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.general.domain.SignatureDocument} entity.
 */
@Schema(description = "存储电子签名文档的相关信息")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class SignatureDocumentDTO implements Serializable {

    @NotNull
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @NotNull
    @Schema(description = "文档标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @Schema(description = "文档描述")
    private String description;

    @NotNull
    @Schema(description = "文档URL地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String documentUrl;

    @NotNull
    @Schema(description = "文档状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private SignatureDocumentStatus status;

    @NotNull
    @Schema(description = "签名服务提供商", requiredMode = Schema.RequiredMode.REQUIRED)
    private SignatureProvider provider;

    @Schema(description = "外部系统文档ID")
    private String externalId;

    @Schema(description = "事务ID")
    private String transactionId;

    @NotNull
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;

    @Schema(description = "过期时间")
    private Instant expireTime;

    @Schema(description = "签署时间")
    private Instant signedTime;

    @Schema(description = "签署后文档URL")
    private String signedDocumentUrl;

    @Schema(description = "扩展元数据（JSON格式）")
    private String metadata;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @NotNull
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDocumentUrl() {
        return documentUrl;
    }

    public void setDocumentUrl(String documentUrl) {
        this.documentUrl = documentUrl;
    }

    public SignatureDocumentStatus getStatus() {
        return status;
    }

    public void setStatus(SignatureDocumentStatus status) {
        this.status = status;
    }

    public SignatureProvider getProvider() {
        return provider;
    }

    public void setProvider(SignatureProvider provider) {
        this.provider = provider;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Instant getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public Instant getSignedTime() {
        return signedTime;
    }

    public void setSignedTime(Instant signedTime) {
        this.signedTime = signedTime;
    }

    public String getSignedDocumentUrl() {
        return signedDocumentUrl;
    }

    public void setSignedDocumentUrl(String signedDocumentUrl) {
        this.signedDocumentUrl = signedDocumentUrl;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SignatureDocumentDTO)) {
            return false;
        }

        SignatureDocumentDTO signatureDocumentDTO = (SignatureDocumentDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, signatureDocumentDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "SignatureDocumentDTO{" +
            "id=" + getId() +
            ", title='" + getTitle() + "'" +
            ", description='" + getDescription() + "'" +
            ", documentUrl='" + getDocumentUrl() + "'" +
            ", status='" + getStatus() + "'" +
            ", provider='" + getProvider() + "'" +
            ", externalId='" + getExternalId() + "'" +
            ", transactionId='" + getTransactionId() + "'" +
            ", userId='" + getUserId() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", signedTime='" + getSignedTime() + "'" +
            ", signedDocumentUrl='" + getSignedDocumentUrl() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
