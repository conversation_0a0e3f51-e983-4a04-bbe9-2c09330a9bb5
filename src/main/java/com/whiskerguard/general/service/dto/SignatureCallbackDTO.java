package com.whiskerguard.general.service.dto;

import com.whiskerguard.general.domain.enumeration.SignatureDocumentStatus;
import com.whiskerguard.general.domain.enumeration.SignatureProvider;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.Instant;
import java.util.Map;

/**
 * 签名回调数据传输对象
 * <p>
 * 用于接收签名服务商的回调数据，包括签名状态、签名时间、签名后的文档URL等信息。
 * </p>
 *
 * <AUTHOR> Yan
 */
@Schema(description = "签名回调数据")
public class SignatureCallbackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部系统文档ID
     */
    @Schema(description = "外部系统文档ID")
    private String externalId;

    /**
     * 事务ID
     */
    @Schema(description = "事务ID")
    private String transactionId;

    /**
     * 签名服务提供商
     */
    @Schema(description = "签名服务提供商")
    private SignatureProvider provider;

    /**
     * 签名状态
     */
    @Schema(description = "签名状态")
    private SignatureDocumentStatus status;

    /**
     * 签名时间
     */
    @Schema(description = "签名时间")
    private Instant signedTime;

    /**
     * 签名后的文档URL
     */
    @Schema(description = "签名后的文档URL")
    private String signedDocumentUrl;

    /**
     * 原始回调数据
     */
    @Schema(description = "原始回调数据")
    private Map<String, Object> rawData;

    // Getters and Setters

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public SignatureProvider getProvider() {
        return provider;
    }

    public void setProvider(SignatureProvider provider) {
        this.provider = provider;
    }

    public SignatureDocumentStatus getStatus() {
        return status;
    }

    public void setStatus(SignatureDocumentStatus status) {
        this.status = status;
    }

    public Instant getSignedTime() {
        return signedTime;
    }

    public void setSignedTime(Instant signedTime) {
        this.signedTime = signedTime;
    }

    public String getSignedDocumentUrl() {
        return signedDocumentUrl;
    }

    public void setSignedDocumentUrl(String signedDocumentUrl) {
        this.signedDocumentUrl = signedDocumentUrl;
    }

    public Map<String, Object> getRawData() {
        return rawData;
    }

    public void setRawData(Map<String, Object> rawData) {
        this.rawData = rawData;
    }

    @Override
    public String toString() {
        return (
            "SignatureCallbackDTO{" +
            "externalId='" +
            externalId +
            '\'' +
            ", transactionId='" +
            transactionId +
            '\'' +
            ", provider=" +
            provider +
            ", status=" +
            status +
            ", signedTime=" +
            signedTime +
            ", signedDocumentUrl='" +
            signedDocumentUrl +
            '\'' +
            ", rawData=" +
            rawData +
            '}'
        );
    }
}
