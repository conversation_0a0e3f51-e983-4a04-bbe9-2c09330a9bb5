package com.whiskerguard.general.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.general.domain.UserWechatBinding} entity.
 */
@Schema(description = "用户微信绑定实体JDL定义")
@Data
public class UserWechatBindingDTO implements Serializable {

    private Long id;

    @Schema(description = "用户ID（员工ID）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long employeeId;

    @NotNull
    @Size(max = 64)
    @Schema(description = "微信OpenID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String openId;

    @Size(max = 64)
    @Schema(description = "微信UnionID")
    private String unionId;

    @Size(max = 64)
    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Size(max = 64)
    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UserWechatBindingDTO userWechatBindingDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, userWechatBindingDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

}
