package com.whiskerguard.general.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.general.domain.Company} entity.
 *
 * 用于{@link com.whiskerguard.general.domain.Company}实体的数据传输对象。
 */
public class CompanyDTO implements Serializable {

    /**
     * Primary key ID
     * 主键ID
     */
    private Long id;

    /**
     * Tianyancha company ID
     * 天眼查企业ID
     */
    private Long tianyanchaId;

    /**
     * Company name
     * 企业名称
     */
    @NotNull
    @Size(max = 500)
    private String name;

    /**
     * Unified social credit code
     * 统一社会信用代码
     */
    @Size(max = 50)
    private String unifiedSocialCreditCode;

    /**
     * Legal representative name
     * 法定代表人姓名
     */
    @Size(max = 200)
    private String legalPersonName;

    /**
     * Registration status
     * 登记状态
     */
    @Size(max = 50)
    private String regStatus;

    /**
     * Registered capital amount
     * 注册资本金额
     */
    @Size(max = 100)
    private String regCapital;

    /**
     * Currency of registered capital
     * 注册资本币种
     */
    @Size(max = 20)
    private String regCapitalCurrency;

    /**
     * Time of establishment
     * 成立时间
     */
    private Instant establishTime;

    /**
     * Company organization type
     * 企业组织类型
     */
    @Size(max = 200)
    private String companyOrgType;

    /**
     * Registration number
     * 工商注册号
     */
    @Size(max = 50)
    private String regNumber;

    /**
     * Tax registration number
     * 税务登记号
     */
    @Size(max = 50)
    private String taxNumber;

    /**
     * Organization code
     * 组织机构代码
     */
    @Size(max = 50)
    private String orgNumber;

    /**
     * Industry category
     * 行业类别
     */
    @Size(max = 200)
    private String industry;

    /**
     * Registered address
     * 注册地址
     */
    @Size(max = 1000)
    private String regLocation;

    /**
     * Business scope
     * 经营范围
     */
    private String businessScope;

    /**
     * Time of the last update in Tianyancha API
     * 天眼查API中最后更新时间
     */
    private Instant tianyanchaUpdateTime;

    /**
     * Time when data was cached locally
     * 数据本地缓存时间
     */
    private Instant cacheTime;

    /**
     * Historical names of the company
     * 企业历史名称
     */
    @Size(max = 1000)
    private String historyNames;

    /**
     * Cancellation date
     * 注销日期
     */
    private Instant cancelDate;

    /**
     * Revocation date
     * 吊销日期
     */
    private Instant revokeDate;

    /**
     * Reason for revocation
     * 吊销原因
     */
    @Size(max = 500)
    private String revokeReason;

    /**
     * Reason for cancellation
     * 注销原因
     */
    @Size(max = 500)
    private String cancelReason;

    /**
     * Approval time
     * 核准时间
     */
    private Instant approvedTime;

    /**
     * Start time of business term
     * 营业期限起始时间
     */
    private Instant fromTime;

    /**
     * End time of business term
     * 营业期限结束时间
     */
    private Instant toTime;

    /**
     * Actual paid-in capital
     * 实缴资本
     */
    @Size(max = 100)
    private String actualCapital;

    /**
     * Currency of actual paid-in capital
     * 实缴资本币种
     */
    @Size(max = 20)
    private String actualCapitalCurrency;

    /**
     * Registration authority
     * 登记机关
     */
    @Size(max = 500)
    private String regInstitute;

    /**
     * City
     * 城市
     */
    @Size(max = 100)
    private String city;

    /**
     * District
     * 区县
     */
    @Size(max = 100)
    private String district;

    /**
     * Range of staff numbers
     * 员工人数范围
     */
    @Size(max = 50)
    private String staffNumRange;

    /**
     * Number of employees with social insurance
     * 参保人数
     */
    private Integer socialStaffNum;

    /**
     * Bond number
     * 债券代码
     */
    @Size(max = 50)
    private String bondNum;

    /**
     * Bond name
     * 债券名称
     */
    @Size(max = 200)
    private String bondName;

    /**
     * Bond type
     * 债券类型
     */
    @Size(max = 50)
    private String bondType;

    /**
     * Used bond name
     * 曾用债券名称
     */
    @Size(max = 200)
    private String usedBondName;

    /**
     * Company alias
     * 企业别名
     */
    @Size(max = 500)
    private String alias;

    /**
     * Additional property
     * 额外属性
     */
    @Size(max = 500)
    private String property3;

    /**
     * Company tags
     * 企业标签
     */
    @Size(max = 1000)
    private String tags;

    /**
     * Percentile score of the company
     * 企业百分位评分
     */
    private Integer percentileScore;

    /**
     * Whether it's a micro enterprise (1: yes, 0: no)
     * 是否为微型企业（1：是，0：否）
     */
    private Integer isMicroEnt;

    /**
     * Base information
     * 基本信息
     */
    @Size(max = 50)
    private String base;

    /**
     * Company type code
     * 企业类型代码
     */
    private Integer type;

    /**
     * Company form
     * 企业形态
     */
    @Size(max = 200)
    private String compForm;

    /**
     * Industry category
     * 行业类别
     */
    @Size(max = 100)
    private String industryCategory;

    /**
     * Big industry category
     * 大行业类别
     */
    @Size(max = 100)
    private String industryCategoryBig;

    /**
     * Middle industry category
     * 中行业类别
     */
    @Size(max = 100)
    private String industryCategoryMiddle;

    /**
     * Small industry category
     * 小行业类别
     */
    @Size(max = 100)
    private String industryCategorySmall;

    // Constructors - 构造函数

    public CompanyDTO() {}

    // Getters and Setters - getter和setter方法

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTianyanchaId() {
        return tianyanchaId;
    }

    public void setTianyanchaId(Long tianyanchaId) {
        this.tianyanchaId = tianyanchaId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnifiedSocialCreditCode() {
        return unifiedSocialCreditCode;
    }

    public void setUnifiedSocialCreditCode(String unifiedSocialCreditCode) {
        this.unifiedSocialCreditCode = unifiedSocialCreditCode;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getRegStatus() {
        return regStatus;
    }

    public void setRegStatus(String regStatus) {
        this.regStatus = regStatus;
    }

    public String getRegCapital() {
        return regCapital;
    }

    public void setRegCapital(String regCapital) {
        this.regCapital = regCapital;
    }

    public String getRegCapitalCurrency() {
        return regCapitalCurrency;
    }

    public void setRegCapitalCurrency(String regCapitalCurrency) {
        this.regCapitalCurrency = regCapitalCurrency;
    }

    public Instant getEstablishTime() {
        return establishTime;
    }

    public void setEstablishTime(Instant establishTime) {
        this.establishTime = establishTime;
    }

    public String getCompanyOrgType() {
        return companyOrgType;
    }

    public void setCompanyOrgType(String companyOrgType) {
        this.companyOrgType = companyOrgType;
    }

    public String getRegNumber() {
        return regNumber;
    }

    public void setRegNumber(String regNumber) {
        this.regNumber = regNumber;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getOrgNumber() {
        return orgNumber;
    }

    public void setOrgNumber(String orgNumber) {
        this.orgNumber = orgNumber;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getRegLocation() {
        return regLocation;
    }

    public void setRegLocation(String regLocation) {
        this.regLocation = regLocation;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public Instant getTianyanchaUpdateTime() {
        return tianyanchaUpdateTime;
    }

    public void setTianyanchaUpdateTime(Instant tianyanchaUpdateTime) {
        this.tianyanchaUpdateTime = tianyanchaUpdateTime;
    }

    public Instant getCacheTime() {
        return cacheTime;
    }

    public void setCacheTime(Instant cacheTime) {
        this.cacheTime = cacheTime;
    }

    public String getHistoryNames() {
        return historyNames;
    }

    public void setHistoryNames(String historyNames) {
        this.historyNames = historyNames;
    }

    public Instant getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(Instant cancelDate) {
        this.cancelDate = cancelDate;
    }

    public Instant getRevokeDate() {
        return revokeDate;
    }

    public void setRevokeDate(Instant revokeDate) {
        this.revokeDate = revokeDate;
    }

    public String getRevokeReason() {
        return revokeReason;
    }

    public void setRevokeReason(String revokeReason) {
        this.revokeReason = revokeReason;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public Instant getApprovedTime() {
        return approvedTime;
    }

    public void setApprovedTime(Instant approvedTime) {
        this.approvedTime = approvedTime;
    }

    public Instant getFromTime() {
        return fromTime;
    }

    public void setFromTime(Instant fromTime) {
        this.fromTime = fromTime;
    }

    public Instant getToTime() {
        return toTime;
    }

    public void setToTime(Instant toTime) {
        this.toTime = toTime;
    }

    public String getActualCapital() {
        return actualCapital;
    }

    public void setActualCapital(String actualCapital) {
        this.actualCapital = actualCapital;
    }

    public String getActualCapitalCurrency() {
        return actualCapitalCurrency;
    }

    public void setActualCapitalCurrency(String actualCapitalCurrency) {
        this.actualCapitalCurrency = actualCapitalCurrency;
    }

    public String getRegInstitute() {
        return regInstitute;
    }

    public void setRegInstitute(String regInstitute) {
        this.regInstitute = regInstitute;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getStaffNumRange() {
        return staffNumRange;
    }

    public void setStaffNumRange(String staffNumRange) {
        this.staffNumRange = staffNumRange;
    }

    public Integer getSocialStaffNum() {
        return socialStaffNum;
    }

    public void setSocialStaffNum(Integer socialStaffNum) {
        this.socialStaffNum = socialStaffNum;
    }

    public String getBondNum() {
        return bondNum;
    }

    public void setBondNum(String bondNum) {
        this.bondNum = bondNum;
    }

    public String getBondName() {
        return bondName;
    }

    public void setBondName(String bondName) {
        this.bondName = bondName;
    }

    public String getBondType() {
        return bondType;
    }

    public void setBondType(String bondType) {
        this.bondType = bondType;
    }

    public String getUsedBondName() {
        return usedBondName;
    }

    public void setUsedBondName(String usedBondName) {
        this.usedBondName = usedBondName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getProperty3() {
        return property3;
    }

    public void setProperty3(String property3) {
        this.property3 = property3;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Integer getPercentileScore() {
        return percentileScore;
    }

    public void setPercentileScore(Integer percentileScore) {
        this.percentileScore = percentileScore;
    }

    public Integer getIsMicroEnt() {
        return isMicroEnt;
    }

    public void setIsMicroEnt(Integer isMicroEnt) {
        this.isMicroEnt = isMicroEnt;
    }

    public String getBase() {
        return base;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCompForm() {
        return compForm;
    }

    public void setCompForm(String compForm) {
        this.compForm = compForm;
    }

    public String getIndustryCategory() {
        return industryCategory;
    }

    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory;
    }

    public String getIndustryCategoryBig() {
        return industryCategoryBig;
    }

    public void setIndustryCategoryBig(String industryCategoryBig) {
        this.industryCategoryBig = industryCategoryBig;
    }

    public String getIndustryCategoryMiddle() {
        return industryCategoryMiddle;
    }

    public void setIndustryCategoryMiddle(String industryCategoryMiddle) {
        this.industryCategoryMiddle = industryCategoryMiddle;
    }

    public String getIndustryCategorySmall() {
        return industryCategorySmall;
    }

    public void setIndustryCategorySmall(String industryCategorySmall) {
        this.industryCategorySmall = industryCategorySmall;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CompanyDTO)) {
            return false;
        }

        CompanyDTO companyDTO = (CompanyDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, companyDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    @Override
    public String toString() {
        return (
            "CompanyDTO{" +
            "id=" +
            getId() +
            ", tianyanchaId=" +
            getTianyanchaId() +
            ", name=" +
            getName() +
            ", unifiedSocialCreditCode=" +
            getUnifiedSocialCreditCode() +
            ", legalPersonName=" +
            getLegalPersonName() +
            ", regStatus=" +
            getRegStatus() +
            ", regCapital=" +
            getRegCapital() +
            ", establishTime=" +
            getEstablishTime() +
            ", companyOrgType=" +
            getCompanyOrgType() +
            ", industry=" +
            getIndustry() +
            ", cacheTime=" +
            getCacheTime() +
            "}"
        );
    }
}
