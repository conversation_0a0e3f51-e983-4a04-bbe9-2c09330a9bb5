package com.whiskerguard.general.service.dto;

import java.time.Instant;

/**
 * 批量通知响应DTO
 *
 * <AUTHOR> @version 1.0
 * @date 2025-06-23
 */
public class BatchNotificationResponseDTO {

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 状态
     */
    private String status;

    /**
     * 消息
     */
    private String message;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 提交时间
     */
    private Instant submittedTime;

    // 默认构造函数
    public BatchNotificationResponseDTO() {
        this.submittedTime = Instant.now();
    }

    // 构造函数
    public BatchNotificationResponseDTO(String batchId, String status, String message) {
        this.batchId = batchId;
        this.status = status;
        this.message = message;
        this.submittedTime = Instant.now();
    }

    // Getters and Setters
    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Instant getSubmittedTime() {
        return submittedTime;
    }

    public void setSubmittedTime(Instant submittedTime) {
        this.submittedTime = submittedTime;
    }

    @Override
    public String toString() {
        return (
            "BatchNotificationResponseDTO{" +
            "batchId='" +
            batchId +
            '\'' +
            ", status='" +
            status +
            '\'' +
            ", message='" +
            message +
            '\'' +
            ", totalCount=" +
            totalCount +
            ", submittedTime=" +
            submittedTime +
            '}'
        );
    }
}
