package com.whiskerguard.general.service.dto;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户ID和分类请求DTO
 */
public class UserCategoryRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull
    private Long userId;

    /**
     * 通知分类名称
     */
    @NotNull
    private String categoryName;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    @Override
    public String toString() {
        return "UserCategoryRequestDTO{" + "userId=" + userId + ", categoryName='" + categoryName + '\'' + '}';
    }
}
