package com.whiskerguard.general.service.dto;

import java.io.Serializable;

/**
 * DTO for company three elements verification result.
 *
 * 企业三要素验证结果的数据传输对象。
 */
public class CompanyVerificationResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Verification result: "一致" (match) or "不一致" (mismatch)
     *
     * 验证结果："一致"或"不一致"
     */
    private String verifyResult;

    /**
     * Company name used for verification
     *
     * 用于验证的企业名称
     */
    private String companyName;

    /**
     * Unified social credit code used for verification
     *
     * 用于验证的统一社会信用代码
     */
    private String creditCode;

    /**
     * Legal person name used for verification
     *
     * 用于验证的法定代表人姓名
     */
    private String legalPersonName;

    /**
     * Additional message about verification result
     *
     * 关于验证结果的额外信息
     */
    private String message;

    /**
     * Default constructor
     *
     * 默认构造函数
     */
    public CompanyVerificationResultDTO() {}

    /**
     * Constructor with parameters
     *
     * 带参数的构造函数
     *
     * @param verifyResult Verification result
     * @param companyName Company name
     * @param creditCode Unified social credit code
     * @param legalPersonName Legal person name
     */
    public CompanyVerificationResultDTO(String verifyResult, String companyName, String creditCode, String legalPersonName) {
        this.verifyResult = verifyResult;
        this.companyName = companyName;
        this.creditCode = creditCode;
        this.legalPersonName = legalPersonName;
    }

    /**
     * Get verification result
     *
     * 获取验证结果
     */
    public String getVerifyResult() {
        return verifyResult;
    }

    /**
     * Set verification result
     *
     * 设置验证结果
     */
    public void setVerifyResult(String verifyResult) {
        this.verifyResult = verifyResult;
    }

    /**
     * Get company name
     *
     * 获取企业名称
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     * Set company name
     *
     * 设置企业名称
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    /**
     * Get unified social credit code
     *
     * 获取统一社会信用代码
     */
    public String getCreditCode() {
        return creditCode;
    }

    /**
     * Set unified social credit code
     *
     * 设置统一社会信用代码
     */
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    /**
     * Get legal person name
     *
     * 获取法定代表人姓名
     */
    public String getLegalPersonName() {
        return legalPersonName;
    }

    /**
     * Set legal person name
     *
     * 设置法定代表人姓名
     */
    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    /**
     * Get additional message
     *
     * 获取额外信息
     */
    public String getMessage() {
        return message;
    }

    /**
     * Set additional message
     *
     * 设置额外信息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * Check if verification is a match
     *
     * 检查验证是否匹配
     *
     * @return true if the verification result is "一致" or "match"
     */
    public boolean isMatch() {
        return "一致".equals(verifyResult) || "match".equalsIgnoreCase(verifyResult);
    }

    @Override
    public String toString() {
        return (
            "CompanyVerificationResultDTO{" +
            "verifyResult='" +
            verifyResult +
            '\'' +
            ", companyName='" +
            companyName +
            '\'' +
            ", creditCode='" +
            creditCode +
            '\'' +
            ", legalPersonName='" +
            legalPersonName +
            '\'' +
            ", message='" +
            message +
            '\'' +
            '}'
        );
    }
}
