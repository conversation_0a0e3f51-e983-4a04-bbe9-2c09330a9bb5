package com.whiskerguard.general.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 签署人信息数据传输对象
 * <p>
 * 用于描述签署人的基本信息，包括姓名、手机号、身份证号等。
 * </p>
 *
 * <AUTHOR>
 */
@Schema(description = "签署人信息")
public class SignerInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 签署人姓名
     */
    @NotBlank
    @Schema(description = "签署人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    /**
     * 签署人手机号
     */
    @NotBlank
    @Schema(description = "签署人手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    /**
     * 签署人身份证号
     */
    @Schema(description = "签署人身份证号")
    private String idCard;

    /**
     * 签署人邮箱
     */
    @Schema(description = "签署人邮箱")
    private String email;

    /**
     * 签署顺序
     */
    @Schema(description = "签署顺序")
    private Integer order;

    /**
     * 签署人企业名称（如果是企业签署）
     */
    @Schema(description = "签署人企业名称")
    private String companyName;

    /**
     * 签署人职位（如果是企业签署）
     */
    @Schema(description = "签署人职位")
    private String position;

    // Getters and Setters

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    @Override
    public String toString() {
        return (
            "SignerInfoDTO{" +
            "name='" +
            name +
            '\'' +
            ", mobile='" +
            mobile +
            '\'' +
            ", idCard='" +
            idCard +
            '\'' +
            ", email='" +
            email +
            '\'' +
            ", order=" +
            order +
            ", companyName='" +
            companyName +
            '\'' +
            ", position='" +
            position +
            '\'' +
            '}'
        );
    }
}
