package com.whiskerguard.general.service.dto;

import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationPriority;
import com.whiskerguard.general.domain.enumeration.NotificationScope;
import com.whiskerguard.general.domain.enumeration.NotificationSubType;
import com.whiskerguard.general.domain.enumeration.NotificationType;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import jakarta.validation.constraints.*;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 通知请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-23
 */
public class NotificationRequestDTO {

    /**
     * 通知分类
     */
    @NotNull
    private NotificationCategory category;

    /**
     * 通知子类型
     */
    @NotNull
    private NotificationSubType subType;

    /**
     * 通知范围
     */
    @NotNull
    private NotificationScope scope;

    /**
     * 通知标题
     */
    @NotBlank
    @Size(max = 200)
    private String title;

    /**
     * 通知内容
     */
    @Size(max = 2000)
    private String content;

    /**
     * 接收者类型
     */
    @NotNull
    private RecipientType recipientType;

    /**
     * 接收者ID列表
     */
    private List<Long> recipientIds;

    /**
     * 发送渠道列表
     */
    private List<NotificationType> channels;

    /**
     * 优先级
     */
    @NotNull
    private NotificationPriority priority;

    /**
     * 计划发送时间
     */
    private Instant scheduledTime;

    /**
     * 关联业务ID
     */
    @Size(max = 100)
    private String businessId;

    /**
     * 业务类型
     */
    @Size(max = 50)
    private String businessType;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;

    // 默认构造函数
    public NotificationRequestDTO() {}

    // Getters and Setters
    public NotificationCategory getCategory() {
        return category;
    }

    public void setCategory(NotificationCategory category) {
        this.category = category;
    }

    public NotificationSubType getSubType() {
        return subType;
    }

    public void setSubType(NotificationSubType subType) {
        this.subType = subType;
    }

    public NotificationScope getScope() {
        return scope;
    }

    public void setScope(NotificationScope scope) {
        this.scope = scope;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public RecipientType getRecipientType() {
        return recipientType;
    }

    public void setRecipientType(RecipientType recipientType) {
        this.recipientType = recipientType;
    }

    public List<Long> getRecipientIds() {
        return recipientIds;
    }

    public void setRecipientIds(List<Long> recipientIds) {
        this.recipientIds = recipientIds;
    }

    public List<NotificationType> getChannels() {
        return channels;
    }

    public void setChannels(List<NotificationType> channels) {
        this.channels = channels;
    }

    public NotificationPriority getPriority() {
        return priority;
    }

    public void setPriority(NotificationPriority priority) {
        this.priority = priority;
    }

    public Instant getScheduledTime() {
        return scheduledTime;
    }

    public void setScheduledTime(Instant scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Map<String, Object> getTemplateParams() {
        return templateParams;
    }

    public void setTemplateParams(Map<String, Object> templateParams) {
        this.templateParams = templateParams;
    }

    @Override
    public String toString() {
        return (
            "NotificationRequestDTO{" +
            "category=" +
            category +
            ", subType=" +
            subType +
            ", scope=" +
            scope +
            ", title='" +
            title +
            '\'' +
            ", content='" +
            content +
            '\'' +
            ", recipientType=" +
            recipientType +
            ", recipientIds=" +
            recipientIds +
            ", channels=" +
            channels +
            ", priority=" +
            priority +
            ", scheduledTime=" +
            scheduledTime +
            ", businessId='" +
            businessId +
            '\'' +
            ", businessType='" +
            businessType +
            '\'' +
            ", templateId=" +
            templateId +
            ", templateParams=" +
            templateParams +
            '}'
        );
    }
}
