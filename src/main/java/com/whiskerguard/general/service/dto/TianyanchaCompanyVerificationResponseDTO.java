package com.whiskerguard.general.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * DTO for Tianyancha Company Three Elements Verification API response.
 * Maps to the response from /baseinfo/verify endpoint.
 *
 * 天眼查企业三要素验证API响应的DTO。
 * 映射/baseinfo/verify端点的响应。
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TianyanchaCompanyVerificationResponseDTO extends TianyanchaBaseResponseDTO {

    /**
     * The verification result object.
     *
     * 验证结果对象。
     */
    @JsonProperty("result")
    private VerificationResult result;

    public VerificationResult getResult() {
        return result;
    }

    public void setResult(VerificationResult result) {
        this.result = result;
    }

    /**
     * Inner class representing verification result details.
     *
     * 表示验证结果详情的内部类。
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VerificationResult implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * Verification result status: "一致" (match) or "不一致" (mismatch).
         *
         * 验证结果状态："一致"或"不一致"。
         */
        @JsonProperty("verifyResult")
        private String verifyResult;

        /**
         * Company name used for verification.
         *
         * 用于验证的企业名称。
         */
        @JsonProperty("companyName")
        private String companyName;

        /**
         * Unified social credit code used for verification.
         *
         * 用于验证的统一社会信用代码。
         */
        @JsonProperty("creditCode")
        private String creditCode;

        /**
         * Legal person name used for verification.
         *
         * 用于验证的法定代表人姓名。
         */
        @JsonProperty("legalPersonName")
        private String legalPersonName;

        public String getVerifyResult() {
            return verifyResult;
        }

        public void setVerifyResult(String verifyResult) {
            this.verifyResult = verifyResult;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        public String getCreditCode() {
            return creditCode;
        }

        public void setCreditCode(String creditCode) {
            this.creditCode = creditCode;
        }

        public String getLegalPersonName() {
            return legalPersonName;
        }

        public void setLegalPersonName(String legalPersonName) {
            this.legalPersonName = legalPersonName;
        }
    }
}
