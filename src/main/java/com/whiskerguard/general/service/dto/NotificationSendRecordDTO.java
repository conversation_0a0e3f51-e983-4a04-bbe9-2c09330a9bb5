package com.whiskerguard.general.service.dto;

import com.whiskerguard.general.domain.enumeration.NotificationType;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.domain.enumeration.SendStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.general.domain.NotificationSendRecord} entity.
 */
@Schema(description = "存储每个接收者的通知发送详细记录")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class NotificationSendRecordDTO implements Serializable {

    @NotNull
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @NotNull
    @Schema(description = "租户ID（0 = 平台级）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @NotNull
    @Schema(description = "接收者ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long recipientId;

    @NotNull
    @Schema(description = "接收者类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private RecipientType recipientType;

    @NotNull
    @Schema(description = "发送渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    private NotificationType channel;

    @NotNull
    @Schema(description = "发送状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private SendStatus status;

    @Schema(description = "发送时间")
    private Instant sentTime;

    @Schema(description = "阅读时间")
    private Instant readTime;

    @Size(max = 1000)
    @Schema(description = "错误信息")
    private String errorMessage;

    @Size(max = 100)
    @Schema(description = "第三方服务返回的ID")
    private String externalId;

    @NotNull
    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Size(max = 50)
    @Schema(description = "创建者")
    private String createdBy;

    @NotNull
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Size(max = 50)
    @Schema(description = "更新者")
    private String updatedBy;

    @NotNull
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @NotNull
    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private NotificationRecordDTO notification;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getRecipientId() {
        return recipientId;
    }

    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }

    public RecipientType getRecipientType() {
        return recipientType;
    }

    public void setRecipientType(RecipientType recipientType) {
        this.recipientType = recipientType;
    }

    public NotificationType getChannel() {
        return channel;
    }

    public void setChannel(NotificationType channel) {
        this.channel = channel;
    }

    public SendStatus getStatus() {
        return status;
    }

    public void setStatus(SendStatus status) {
        this.status = status;
    }

    public Instant getSentTime() {
        return sentTime;
    }

    public void setSentTime(Instant sentTime) {
        this.sentTime = sentTime;
    }

    public Instant getReadTime() {
        return readTime;
    }

    public void setReadTime(Instant readTime) {
        this.readTime = readTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public NotificationRecordDTO getNotification() {
        return notification;
    }

    public void setNotification(NotificationRecordDTO notification) {
        this.notification = notification;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof NotificationSendRecordDTO)) {
            return false;
        }

        NotificationSendRecordDTO notificationSendRecordDTO = (NotificationSendRecordDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, notificationSendRecordDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "NotificationSendRecordDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", recipientId=" + getRecipientId() +
            ", recipientType='" + getRecipientType() + "'" +
            ", channel='" + getChannel() + "'" +
            ", status='" + getStatus() + "'" +
            ", sentTime='" + getSentTime() + "'" +
            ", readTime='" + getReadTime() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", externalId='" + getExternalId() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", notification=" + getNotification() +
            "}";
    }
}
