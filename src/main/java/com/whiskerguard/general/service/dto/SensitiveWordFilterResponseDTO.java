package com.whiskerguard.general.service.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 敏感词过滤响应DTO，包含过滤结果和发现的敏感词。
 * Sensitive word filter response DTO containing filtering results and found sensitive words.
 */
public class SensitiveWordFilterResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 原始内容
     * Original content
     */
    private String originalContent;

    /**
     * 过滤后的内容
     * Filtered content
     */
    private String filteredContent;

    /**
     * 是否包含敏感词
     * Whether content contains sensitive words
     */
    private boolean containsSensitiveWords;

    /**
     * 发现的敏感词列表
     * List of found sensitive words
     */
    private List<String> foundSensitiveWords = new ArrayList<>();

    public SensitiveWordFilterResponseDTO() {}

    public String getOriginalContent() {
        return originalContent;
    }

    public void setOriginalContent(String originalContent) {
        this.originalContent = originalContent;
    }

    public String getFilteredContent() {
        return filteredContent;
    }

    public void setFilteredContent(String filteredContent) {
        this.filteredContent = filteredContent;
    }

    public boolean isContainsSensitiveWords() {
        return containsSensitiveWords;
    }

    public void setContainsSensitiveWords(boolean containsSensitiveWords) {
        this.containsSensitiveWords = containsSensitiveWords;
    }

    public List<String> getFoundSensitiveWords() {
        return foundSensitiveWords;
    }

    public void setFoundSensitiveWords(List<String> foundSensitiveWords) {
        this.foundSensitiveWords = foundSensitiveWords;
    }
}
