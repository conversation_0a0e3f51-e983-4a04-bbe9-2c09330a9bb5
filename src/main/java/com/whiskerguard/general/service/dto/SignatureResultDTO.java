package com.whiskerguard.general.service.dto;

import com.whiskerguard.general.domain.enumeration.SignatureDocumentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Map;

/**
 * 签名结果数据传输对象
 * <p>
 * 用于返回签名任务创建的结果，包括签名URL、任务ID、状态等信息。
 * </p>
 *
 * <AUTHOR>
 */
@Schema(description = "签名结果")
public class SignatureResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private boolean success;

    /**
     * 错误消息（如果失败）
     */
    @Schema(description = "错误消息")
    private String errorMessage;

    /**
     * 签名文档ID
     */
    @Schema(description = "签名文档ID")
    private Long documentId;

    /**
     * 外部系统文档ID
     */
    @Schema(description = "外部系统文档ID")
    private String externalId;

    /**
     * 事务ID
     */
    @Schema(description = "事务ID")
    private String transactionId;

    /**
     * 签名URL
     */
    @Schema(description = "签名URL")
    private String signUrl;

    /**
     * 文档状态
     */
    @Schema(description = "文档状态")
    private SignatureDocumentStatus status;

    /**
     * 扩展数据
     */
    @Schema(description = "扩展数据")
    private Map<String, Object> extraData;

    // Getters and Setters

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getSignUrl() {
        return signUrl;
    }

    public void setSignUrl(String signUrl) {
        this.signUrl = signUrl;
    }

    public SignatureDocumentStatus getStatus() {
        return status;
    }

    public void setStatus(SignatureDocumentStatus status) {
        this.status = status;
    }

    public Map<String, Object> getExtraData() {
        return extraData;
    }

    public void setExtraData(Map<String, Object> extraData) {
        this.extraData = extraData;
    }

    /**
     * 创建成功结果
     *
     * @param documentId    文档ID
     * @param externalId    外部系统文档ID
     * @param transactionId 事务ID
     * @param signUrl       签名URL
     * @param status        文档状态
     * @return 签名结果
     */
    public static SignatureResultDTO success(
        Long documentId,
        String externalId,
        String transactionId,
        String signUrl,
        SignatureDocumentStatus status
    ) {
        SignatureResultDTO result = new SignatureResultDTO();
        result.setSuccess(true);
        result.setDocumentId(documentId);
        result.setExternalId(externalId);
        result.setTransactionId(transactionId);
        result.setSignUrl(signUrl);
        result.setStatus(status);
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param errorMessage 错误消息
     * @return 签名结果
     */
    public static SignatureResultDTO failure(String errorMessage) {
        SignatureResultDTO result = new SignatureResultDTO();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }

    @Override
    public String toString() {
        return (
            "SignatureResultDTO{" +
            "success=" +
            success +
            ", errorMessage='" +
            errorMessage +
            '\'' +
            ", documentId=" +
            documentId +
            ", externalId='" +
            externalId +
            '\'' +
            ", transactionId='" +
            transactionId +
            '\'' +
            ", signUrl='" +
            signUrl +
            '\'' +
            ", status=" +
            status +
            ", extraData=" +
            extraData +
            '}'
        );
    }
}
