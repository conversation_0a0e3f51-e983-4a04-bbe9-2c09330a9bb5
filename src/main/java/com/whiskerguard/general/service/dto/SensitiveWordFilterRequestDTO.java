package com.whiskerguard.general.service.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 敏感词过滤请求DTO，用于批量处理多个内容项。
 * Sensitive word filter request DTO for batch processing multiple content items.
 */
public class SensitiveWordFilterRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要过滤的内容列表
     * List of content items to be filtered
     */
    private List<String> contents = new ArrayList<>();

    /**
     * 替换敏感词的字符（默认为*）
     * Character used to replace sensitive words (default is *)
     */
    private String replacementChar = "*";

    /**
     * 租户ID，用于特定租户的敏感词过滤
     * Tenant ID for tenant-specific sensitive word filtering
     */
    private Long tenantId;

    public SensitiveWordFilterRequestDTO() {}

    public List<String> getContents() {
        return contents;
    }

    public void setContents(List<String> contents) {
        this.contents = contents;
    }

    public String getReplacementChar() {
        return replacementChar;
    }

    public void setReplacementChar(String replacementChar) {
        this.replacementChar = replacementChar;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
}
