package com.whiskerguard.general.service.dto;

import com.whiskerguard.general.domain.enumeration.SignatureProvider;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 签名请求数据传输对象
 * <p>
 * 用于创建签名任务时传递必要的参数，包括文档信息、签署人信息、签名服务提供商等。
 * </p>
 *
 * <AUTHOR>
 */
@Schema(description = "签名请求参数")
public class SignatureRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文档标题
     */
    @NotBlank
    @Schema(description = "文档标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    /**
     * 文档描述
     */
    @Schema(description = "文档描述")
    private String description;

    /**
     * 文档URL地址
     */
    @NotBlank
    @Schema(description = "文档URL地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String documentUrl;

    /**
     * 签名服务提供商
     */
    @NotNull
    @Schema(description = "签名服务提供商", requiredMode = Schema.RequiredMode.REQUIRED)
    private SignatureProvider provider;

    /**
     * 用户ID
     */
    @NotBlank
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userId;

    /**
     * 签署人信息列表
     */
    @NotNull
    @Schema(description = "签署人信息列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<SignerInfoDTO> signers;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private Instant expireTime;

    /**
     * 回调URL
     */
    @Schema(description = "回调URL")
    private String callbackUrl;

    /**
     * 扩展元数据（JSON格式）
     */
    @Schema(description = "扩展元数据")
    private Map<String, Object> metadata;

    // Getters and Setters

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDocumentUrl() {
        return documentUrl;
    }

    public void setDocumentUrl(String documentUrl) {
        this.documentUrl = documentUrl;
    }

    public SignatureProvider getProvider() {
        return provider;
    }

    public void setProvider(SignatureProvider provider) {
        this.provider = provider;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<SignerInfoDTO> getSigners() {
        return signers;
    }

    public void setSigners(List<SignerInfoDTO> signers) {
        this.signers = signers;
    }

    public Instant getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    @Override
    public String toString() {
        return (
            "SignatureRequestDTO{" +
            "title='" +
            title +
            '\'' +
            ", description='" +
            description +
            '\'' +
            ", documentUrl='" +
            documentUrl +
            '\'' +
            ", provider=" +
            provider +
            ", userId='" +
            userId +
            '\'' +
            ", signers=" +
            signers +
            ", expireTime=" +
            expireTime +
            ", callbackUrl='" +
            callbackUrl +
            '\'' +
            ", metadata=" +
            metadata +
            '}'
        );
    }
}
