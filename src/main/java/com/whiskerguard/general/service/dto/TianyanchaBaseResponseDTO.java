package com.whiskerguard.general.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/**
 * Base DTO for all Tianyancha API responses.
 * Contains common fields present in all API responses.
 *
 * 所有天眼查API响应的基础DTO。
 * 包含所有API响应中存在的公共字段。
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TianyanchaBaseResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * API response error code. 0 means success.
     *
     * API响应错误代码。0表示成功。
     */
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * Error message or response reason.
     *
     * 错误信息或响应原因。
     */
    @JsonProperty("reason")
    private String reason;

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * Check if the API call was successful.
     *
     * 检查API调用是否成功。
     */
    public boolean isSuccess() {
        return errorCode != null && errorCode == 0;
    }
}
