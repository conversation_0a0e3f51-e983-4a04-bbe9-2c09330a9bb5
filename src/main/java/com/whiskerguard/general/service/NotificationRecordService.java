package com.whiskerguard.general.service;

import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.general.domain.NotificationRecord}.
 */
public interface NotificationRecordService {
    /**
     * Save a notificationRecord.
     *
     * @param notificationRecordDTO the entity to save.
     * @return the persisted entity.
     */
    NotificationRecordDTO save(NotificationRecordDTO notificationRecordDTO);

    /**
     * Updates a notificationRecord.
     *
     * @param notificationRecordDTO the entity to update.
     * @return the persisted entity.
     */
    NotificationRecordDTO update(NotificationRecordDTO notificationRecordDTO);

    /**
     * Partially updates a notificationRecord.
     *
     * @param notificationRecordDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<NotificationRecordDTO> partialUpdate(NotificationRecordDTO notificationRecordDTO);

    /**
     * Get all the notificationRecords.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NotificationRecordDTO> findAll(Pageable pageable);

    /**
     * Get the "id" notificationRecord.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<NotificationRecordDTO> findOne(Long id);

    /**
     * Delete the "id" notificationRecord.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
