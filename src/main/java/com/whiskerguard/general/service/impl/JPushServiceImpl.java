package com.whiskerguard.general.service.impl;

import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Message;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import com.google.gson.JsonObject;
import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.NotificationType;
import com.whiskerguard.general.model.PushRequest;
import com.whiskerguard.general.model.PushTargetType;
import com.whiskerguard.general.service.PushService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 极光推送服务实现
 */
@Service
public class JPushServiceImpl implements PushService {

    private static final Logger log = LoggerFactory.getLogger(JPushServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private JPushClient jpushClient;

    @Autowired
    public JPushServiceImpl(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @PostConstruct
    public void init() {
        try {
            ApplicationProperties.Notification.Push pushProperties = applicationProperties.getNotification().getPush();
            if (!pushProperties.isEnabled()) {
                log.warn("极光推送服务未启用");
                return;
            }

            jpushClient = new JPushClient(pushProperties.getMasterSecret(), pushProperties.getAppKey());
            log.info("极光推送客户端初始化成功");
        } catch (Exception e) {
            log.error("极光推送客户端初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (jpushClient != null) {
            jpushClient.close();
            log.info("极光推送客户端已关闭");
        }
    }

    @Override
    public NotificationResponse send(PushRequest request) {
        if (jpushClient == null) {
            return NotificationResponse.failure("极光推送服务未初始化");
        }

        try {
            // 构建推送负载
            PushPayload payload = buildPushPayload(request);

            // 发送推送
            PushResult result = jpushClient.sendPush(payload);

            // 处理响应
            if (result.isResultOK()) {
                NotificationResponse response = NotificationResponse.success("推送发送成功", String.valueOf(result.msg_id));
                response.setType(NotificationType.PUSH);
                return response;
            } else {
                log.error("极光推送发送失败: {}", result);
                return NotificationResponse.failure("推送发送失败: " + result);
            }
        } catch (APIConnectionException e) {
            log.error("极光推送连接异常", e);
            return NotificationResponse.failure("推送连接异常: " + e.getMessage());
        } catch (APIRequestException e) {
            log.error("极光推送请求异常: {}, {}", e.getErrorCode(), e.getErrorMessage());
            return NotificationResponse.failure(e.getErrorMessage(), String.valueOf(e.getErrorCode()));
        }
    }

    /**
     * 构建推送负载
     */
    private PushPayload buildPushPayload(PushRequest request) {
        PushPayload.Builder builder = PushPayload.newBuilder();

        // 设置平台（所有平台）
        builder.setPlatform(Platform.all());

        // 设置推送目标
        builder.setAudience(buildAudience(request));

        // 设置通知
        builder.setNotification(buildNotification(request));

        // 设置消息
        if (!request.isSilent() && request.getContent() != null) {
            builder.setMessage(Message.content(request.getContent()));
        }

        // 设置选项
        Options options = Options.newBuilder().setApnsProduction(applicationProperties.getNotification().getPush().isProduction()).build();
        builder.setOptions(options);

        return builder.build();
    }

    /**
     * 构建推送目标
     */
    private Audience buildAudience(PushRequest request) {
        PushTargetType targetType = request.getTargetType();

        if (targetType == PushTargetType.BROADCAST) {
            return Audience.all();
        }

        if (request.getTargets() == null || request.getTargets().isEmpty()) {
            throw new IllegalArgumentException("推送目标不能为空");
        }

        return switch (targetType) {
            case ALIAS -> Audience.alias(request.getTargets());
            case TAG -> Audience.tag(request.getTargets());
            case REGISTRATION_ID -> Audience.registrationId(request.getTargets());
            default -> throw new IllegalArgumentException("不支持的推送目标类型: " + targetType);
        };
    }

    /**
     * 构建通知
     */
    private Notification buildNotification(PushRequest request) {
        Notification.Builder builder = Notification.newBuilder();

        // 构建 Android 通知
        AndroidNotification.Builder androidBuilder = AndroidNotification.newBuilder()
            .setTitle(request.getTitle())
            .setAlert(request.getContent());

        if (request.getNotificationStyleId() != null) {
            androidBuilder.setStyle(request.getNotificationStyleId());
        }

        // 处理额外数据
        addExtras(request.getExtras(), androidBuilder);
        builder.addPlatformNotification(androidBuilder.build());

        // 构建 iOS 通知
        IosNotification.Builder iosBuilder = IosNotification.newBuilder().setAlert(request.getTitle()).setBadge(1).setSound("default");

        // 处理额外数据
        addExtras(request.getExtras(), iosBuilder);
        builder.addPlatformNotification(iosBuilder.build());

        return builder.build();
    }

    private void addExtras(Map<String, Object> extras, AndroidNotification.Builder builder) {
        if (extras == null || extras.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Object> entry : extras.entrySet()) {
            String value;
            if (entry.getValue() instanceof Map || entry.getValue() instanceof List) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("value", entry.getValue().toString());
                value = jsonObject.toString();
            } else {
                value = String.valueOf(entry.getValue());
            }
            builder.addExtra(entry.getKey(), value);
        }
    }

    private void addExtras(Map<String, Object> extras, IosNotification.Builder builder) {
        if (extras == null || extras.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Object> entry : extras.entrySet()) {
            String value;
            if (entry.getValue() instanceof Map || entry.getValue() instanceof List) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("value", entry.getValue().toString());
                value = jsonObject.toString();
            } else {
                value = String.valueOf(entry.getValue());
            }
            builder.incrBadge(1).addExtra(entry.getKey(), value);
        }
    }
}
