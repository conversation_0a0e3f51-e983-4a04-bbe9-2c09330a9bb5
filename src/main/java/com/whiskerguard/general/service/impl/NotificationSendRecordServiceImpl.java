package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.domain.NotificationSendRecord;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.repository.NotificationSendRecordRepository;
import com.whiskerguard.general.service.NotificationSendRecordService;
import com.whiskerguard.general.service.dto.NotificationSendRecordDTO;
import com.whiskerguard.general.service.mapper.NotificationSendRecordMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.general.domain.NotificationSendRecord}.
 */
@Service
@Transactional
public class NotificationSendRecordServiceImpl implements NotificationSendRecordService {

    private static final Logger LOG = LoggerFactory.getLogger(NotificationSendRecordServiceImpl.class);

    private final NotificationSendRecordRepository notificationSendRecordRepository;

    private final NotificationSendRecordMapper notificationSendRecordMapper;

    public NotificationSendRecordServiceImpl(
        NotificationSendRecordRepository notificationSendRecordRepository,
        NotificationSendRecordMapper notificationSendRecordMapper
    ) {
        this.notificationSendRecordRepository = notificationSendRecordRepository;
        this.notificationSendRecordMapper = notificationSendRecordMapper;
    }

    @Override
    public NotificationSendRecordDTO save(NotificationSendRecordDTO notificationSendRecordDTO) {
        LOG.debug("Request to save NotificationSendRecord : {}", notificationSendRecordDTO);
        NotificationSendRecord notificationSendRecord = notificationSendRecordMapper.toEntity(notificationSendRecordDTO);
        notificationSendRecord = notificationSendRecordRepository.save(notificationSendRecord);
        return notificationSendRecordMapper.toDto(notificationSendRecord);
    }

    @Override
    public NotificationSendRecordDTO update(NotificationSendRecordDTO notificationSendRecordDTO) {
        LOG.debug("Request to update NotificationSendRecord : {}", notificationSendRecordDTO);
        NotificationSendRecord notificationSendRecord = notificationSendRecordMapper.toEntity(notificationSendRecordDTO);
        notificationSendRecord = notificationSendRecordRepository.save(notificationSendRecord);
        return notificationSendRecordMapper.toDto(notificationSendRecord);
    }

    @Override
    public Optional<NotificationSendRecordDTO> partialUpdate(NotificationSendRecordDTO notificationSendRecordDTO) {
        LOG.debug("Request to partially update NotificationSendRecord : {}", notificationSendRecordDTO);

        return notificationSendRecordRepository
            .findById(notificationSendRecordDTO.getId())
            .map(existingNotificationSendRecord -> {
                notificationSendRecordMapper.partialUpdate(existingNotificationSendRecord, notificationSendRecordDTO);

                return existingNotificationSendRecord;
            })
            .map(notificationSendRecordRepository::save)
            .map(notificationSendRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NotificationSendRecordDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NotificationSendRecords");
        return notificationSendRecordRepository.findAll(pageable).map(notificationSendRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NotificationSendRecordDTO> findOne(Long id) {
        LOG.debug("Request to get NotificationSendRecord : {}", id);
        return notificationSendRecordRepository.findById(id).map(notificationSendRecordMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NotificationSendRecord : {}", id);
        notificationSendRecordRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NotificationSendRecordDTO> findByCategory(NotificationCategory category, Pageable pageable) {
        LOG.debug("Request to get NotificationSendRecords by category: {}", category);
        return notificationSendRecordRepository.findByCategory(category, pageable).map(notificationSendRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NotificationSendRecordDTO> findByUserIdAndCategory(Long userId, RecipientType recipientType, Pageable pageable) {
        LOG.debug("Request to get NotificationSendRecords by userId: {} and recipientType: {}", userId, recipientType);
        return notificationSendRecordRepository
            .findByRecipientIdAndRecipientType(userId, recipientType, pageable)
            .map(notificationSendRecordMapper::toDto);
    }
}
