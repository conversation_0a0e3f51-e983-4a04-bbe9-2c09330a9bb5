package com.whiskerguard.general.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.general.config.Constants;
import com.whiskerguard.general.cos.CosProperties;
import com.whiskerguard.general.cos.CosService;
import com.whiskerguard.general.service.FileUploadService;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 描述：文件上传的服务接口实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/6
 */
@Service
public class FileUploadServiceImpl implements FileUploadService {

    private static final Logger log = LoggerFactory.getLogger(FileUploadServiceImpl.class);

    private final CosService cosService;

    @Autowired
    private CosProperties cosProperties;

    private final HttpServletRequest httpServletRequest;

    @Autowired
    public FileUploadServiceImpl(CosService cosService, HttpServletRequest httpServletRequest) {
        this.cosService = cosService;
        this.httpServletRequest = httpServletRequest;
    }

    @Override
    public Map<String, Object> uploadFile(MultipartFile file, String categoryName, String serviceName) throws Exception {
        File tempFile = null;
        Long tenantId = Long.parseLong(HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_TENANT_ID));
        try {
            String originalFilename = Objects.requireNonNull(file.getOriginalFilename());
            // 创建临时文件
            tempFile = createTempFile(file);
            log.info("临时文件创建成功: {}", tempFile.getAbsolutePath());

            // 上传到腾讯云COS
            StringBuilder pathBuilder = new StringBuilder(Constants.DEFAULT_UPLOAD_DIR + serviceName).append("/");
            pathBuilder.append(tenantId).append("/");
            if (StringUtils.isNotBlank(categoryName)) {
                String[] split = categoryName.split(Constants.SPILT_MEDIAN);
                for (String part : split) {
                    pathBuilder.append(part.trim()).append("/");
                }
            }
            pathBuilder.append(LocalDate.now().format(Constants.DATE_FORMATTER)).append("/").append(originalFilename);

            String fileUrl = cosService.upload(tempFile, pathBuilder.toString());
            log.info("文件上传成功: {}", fileUrl);
            Map<String, Object> result = new HashMap<>();
            result.put("message", "success");
            result.put("key", pathBuilder.toString());
            result.put("url", fileUrl);
            result.put("buketName", cosProperties.getBucketName());
            return result;
        } catch (IOException e) {
            log.error("文件处理失败", e);
            throw new Exception("文件上传失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    @Override
    public List<String> getFilesInDirectory(Long tenantId, String categoryName, String uploadTime, String serviceName) {
        try {
            StringBuilder pathBuilder = new StringBuilder()
                .append(Constants.DEFAULT_UPLOAD_DIR)
                .append(serviceName)
                .append("/")
                .append(tenantId)
                .append("/");
            if (StringUtils.isNotBlank(categoryName)) {
                String[] split = categoryName.split(Constants.SPILT_MEDIAN);
                for (String part : split) {
                    pathBuilder.append(part).append("/");
                }
            }
            if (StringUtils.isNotBlank(uploadTime)) {
                pathBuilder.append(uploadTime).append("/");
            }
            List<String> fileUrls = cosService.listDirectoryFiles(pathBuilder.toString());
            log.info("Successfully retrieved file list for tenantId: {}", tenantId);
            return fileUrls;
        } catch (Exception e) {
            log.error(
                "Failed to retrieve file list for tenantId: {}, categoryName: {}, uploadTime: {},serviceName：{}",
                tenantId,
                categoryName,
                uploadTime,
                serviceName,
                e
            );
        }
        return List.of();
    }

    @Override
    public String getFileUrl(String key) {
        try {
            String fileUrl = cosService.generatePresignedUrl(key);
            log.info("Successfully retrieved file url for key: {}", key);
            return fileUrl;
        } catch (Exception e) {
            log.error("Failed to retrieve file url for key: {}", key, e);
        }
        return "";
    }

    private File createTempFile(MultipartFile file) throws IOException {
        // 创建临时文件
        File tempFile = File.createTempFile("upload_", "_temp");
        try (OutputStream os = Files.newOutputStream(tempFile.toPath())) {
            Files.copy(file.getInputStream(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
        }
        return tempFile;
    }
}
