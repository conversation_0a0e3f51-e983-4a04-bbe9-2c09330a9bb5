package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.NotificationType;
import com.whiskerguard.general.model.WechatRequest;
import com.whiskerguard.general.service.WechatService;
import com.whiskerguard.general.service.WechatMessageTimeManager;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 微信公众号服务实现
 */
@Service
public class WechatServiceImpl implements WechatService {

    private static final Logger log = LoggerFactory.getLogger(WechatServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private final WechatMessageTimeManager timeManager;
    private WxMpService wxMpService;

    @Autowired
    public WechatServiceImpl(ApplicationProperties applicationProperties,
                           WechatMessageTimeManager timeManager) {
        this.applicationProperties = applicationProperties;
        this.timeManager = timeManager;
    }

    @PostConstruct
    public void init() {
        try {
            ApplicationProperties.Notification.Wechat wechatProperties =
                applicationProperties.getNotification().getWechat();

            if (!wechatProperties.isEnabled()) {
                log.warn("微信公众号服务未启用");
                return;
            }

            WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
            config.setAppId(wechatProperties.getAppId());
            config.setSecret(wechatProperties.getAppSecret());
            config.setToken(wechatProperties.getToken());
            config.setAesKey(wechatProperties.getAesKey());

            wxMpService = new WxMpServiceImpl();
            wxMpService.setWxMpConfigStorage(config);

            log.info("微信公众号服务初始化成功，AppId: {}", wechatProperties.getAppId());
        } catch (Exception e) {
            log.error("微信公众号服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("微信公众号服务已关闭");
    }

    @Override
    public NotificationResponse sendCustomMessage(WechatRequest request) {
        if (wxMpService == null) {
            return NotificationResponse.failure("微信公众号服务未初始化");
        }

        try {
            List<String> successUsers = new ArrayList<>();
            List<String> failedUsers = new ArrayList<>();
            List<String> timeoutUsers = new ArrayList<>();

            for (String toUser : request.getToUsers()) {
                try {
                    // 检查是否可以发送客服消息（48小时限制）
                    if (!timeManager.canSendCustomerServiceMessage(toUser)) {
                        long remainingHours = timeManager.getRemainingHours(toUser);
                        log.warn("用户超过客服消息时间限制，用户: {}, 剩余时间: {}小时", toUser, remainingHours);
                        timeoutUsers.add(toUser);
                        continue;
                    }

                    WxMpKefuMessage message = buildCustomMessage(request, toUser);
                    boolean success = wxMpService.getKefuService().sendKefuMessage(message);

                    if (success) {
                        successUsers.add(toUser);
                        log.info("客服消息发送成功，用户: {}", toUser);
                    } else {
                        failedUsers.add(toUser);
                        log.warn("客服消息发送失败，用户: {}", toUser);
                    }
                } catch (WxErrorException e) {
                    log.error("发送客服消息失败，用户: {}, 错误代码：{}, 错误信息：{}, 微信原始报文：{}",
                             toUser, e.getError().getErrorCode(), e.getError().getErrorMsg(), e.getMessage());

                    // 特殊处理45015错误（回复时间超过限制）
                    if (e.getError().getErrorCode() == 45015) {
                        timeoutUsers.add(toUser);
                        // 清理过期的交互记录
                        timeManager.clearUserInteraction(toUser);
                    } else {
                        failedUsers.add(toUser);
                    }
                } catch (Exception e) {
                    log.error("发送客服消息异常，用户: {}, 异常: {}", toUser, e.getMessage());
                    failedUsers.add(toUser);
                }
            }

            // 构建响应消息
            StringBuilder messageBuilder = new StringBuilder();
            NotificationResponse response;

            if (!successUsers.isEmpty()) {
                messageBuilder.append(String.format("成功发送: %d人", successUsers.size()));
            }
            if (!failedUsers.isEmpty()) {
                if (!messageBuilder.isEmpty()) {
                    messageBuilder.append(", ");
                }
                messageBuilder.append(String.format("发送失败: %d人", failedUsers.size()));
            }
            if (!timeoutUsers.isEmpty()) {
                if (!messageBuilder.isEmpty()) {
                    messageBuilder.append(", ");
                }
                messageBuilder.append(String.format("超时限制: %d人", timeoutUsers.size()));
            }

            if (failedUsers.isEmpty() && timeoutUsers.isEmpty()) {
                response = NotificationResponse.success(
                    messageBuilder.toString(),
                    String.join(",", successUsers)
                );
            } else {
                String failureMessage = messageBuilder.toString();
                // 添加超时用户详细信息到消息中
                if (!timeoutUsers.isEmpty()) {
                    failureMessage += " (超时用户: " + String.join(",", timeoutUsers) +
                                    ", 建议使用模板消息或等待用户重新交互)";
                }
                response = NotificationResponse.failure(failureMessage);
            }

            response.setType(NotificationType.WECHAT);
            return response;

        } catch (Exception e) {
            log.error("发送微信客服消息异常", e);
            return NotificationResponse.failure("发送微信客服消息异常: " + e.getMessage());
        }
    }

    @Override
    public NotificationResponse sendTemplateMessage(WechatRequest request) {
        if (wxMpService == null) {
            return NotificationResponse.failure("微信公众号服务未初始化");
        }

        try {
            List<String> successUsers = new ArrayList<>();
            List<String> failedUsers = new ArrayList<>();

            for (String toUser : request.getToUsers()) {
                try {
                    WxMpTemplateMessage templateMessage = buildTemplateMessage(request, toUser);
                    String msgId = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);

                    if (msgId != null && !msgId.isEmpty()) {
                        successUsers.add(toUser);
                    } else {
                        failedUsers.add(toUser);
                    }
                } catch (WxErrorException e) {
                    log.error("发送模板消息失败，用户: {}, 错误: {}", toUser, e.getMessage());
                    failedUsers.add(toUser);
                }
            }

            NotificationResponse response;
            if (failedUsers.isEmpty()) {
                response = NotificationResponse.success(
                    String.format("模板消息发送成功，成功用户数: %d", successUsers.size()),
                    String.join(",", successUsers)
                );
            } else {
                response = NotificationResponse.failure(
                    String.format("部分用户发送失败，成功: %d, 失败: %d",
                        successUsers.size(), failedUsers.size())
                );
            }

            response.setType(NotificationType.WECHAT);
            return response;

        } catch (Exception e) {
            log.error("发送微信模板消息异常", e);
            return NotificationResponse.failure("发送微信模板消息异常: " + e.getMessage());
        }
    }

    @Override
    public String getUserInfo(String openId) {
        if (wxMpService == null) {
            return "{\"error\": \"微信公众号服务未初始化\"}";
        }

        try {
            WxMpUser user = wxMpService.getUserService().userInfo(openId);
            return user.toString();
        } catch (WxErrorException e) {
            log.error("获取用户信息失败，OpenId: {}, 错误: {}", openId, e.getMessage());
            return String.format("{\"error\": \"%s\"}", e.getMessage());
        }
    }

    @Override
    public String getFollowers(String nextOpenId) {
        if (wxMpService == null) {
            return "{\"error\": \"微信公众号服务未初始化\"}";
        }

        try {
            WxMpUserList userList = wxMpService.getUserService().userList(nextOpenId);
            return userList.toString();
        } catch (WxErrorException e) {
            log.error("获取关注者列表失败，错误: {}", e.getMessage());
            return String.format("{\"error\": \"%s\"}", e.getMessage());
        }
    }

    /**
     * 构建客服消息
     */
    private WxMpKefuMessage buildCustomMessage(WechatRequest request, String toUser) {
        WxMpKefuMessage message = new WxMpKefuMessage();
        message.setToUser(toUser);
        message.setMsgType(request.getMessageType());

        switch (request.getMessageType()) {
            case "text":
                message.setContent(request.getContent());
                break;
            case "image":
            case "voice":
            case "video":
                message.setMediaId(request.getMediaId());
                break;
            case "news":
                // 图文消息处理
                if (request.getArticles() != null && !request.getArticles().isEmpty()) {
                    List<WxMpKefuMessage.WxArticle> articles = new ArrayList<>();
                    for (WechatRequest.Article article : request.getArticles()) {
                        WxMpKefuMessage.WxArticle wxArticle = new WxMpKefuMessage.WxArticle();
                        wxArticle.setTitle(article.getTitle());
                        wxArticle.setDescription(article.getDescription());
                        wxArticle.setUrl(article.getUrl());
                        wxArticle.setPicUrl(article.getPicUrl());
                        articles.add(wxArticle);
                    }
                    message.setArticles(articles);
                }
                break;
            default:
                break;
        }

        return message;
    }

    /**
     * 构建模板消息
     */
    private WxMpTemplateMessage buildTemplateMessage(WechatRequest request, String toUser) {
        WxMpTemplateMessage templateMessage = new WxMpTemplateMessage();
        templateMessage.setToUser(toUser);
        templateMessage.setTemplateId(request.getTemplateId());
        templateMessage.setUrl(request.getUrl());

        // 设置模板数据
        if (request.getTemplateData() != null && !request.getTemplateData().isEmpty()) {
            List<WxMpTemplateData> data = new ArrayList<>();
            for (Map.Entry<String, WechatRequest.TemplateData> entry : request.getTemplateData().entrySet()) {
                WxMpTemplateData templateData = new WxMpTemplateData(
                    entry.getKey(),
                    entry.getValue().getValue(),
                    entry.getValue().getColor()
                );
                data.add(templateData);
            }
            templateMessage.setData(data);
        }

        // 设置小程序信息
        if (request.getMiniProgram() != null) {
            WxMpTemplateMessage.MiniProgram miniProgram = new WxMpTemplateMessage.MiniProgram(
                request.getMiniProgram().getAppId(),
                request.getMiniProgram().getPagePath(),
                false
            );
            templateMessage.setMiniProgram(miniProgram);
        }

        return templateMessage;
    }
}
