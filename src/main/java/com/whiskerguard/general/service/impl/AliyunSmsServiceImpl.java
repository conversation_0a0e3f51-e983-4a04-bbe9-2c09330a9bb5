package com.whiskerguard.general.service.impl;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.NotificationType;
import com.whiskerguard.general.model.SmsRequest;
import com.whiskerguard.general.service.SmsService;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 阿里云短信服务实现
 */
@Service("aliyunSmsService")
public class AliyunSmsServiceImpl implements SmsService {

    private static final Logger log = LoggerFactory.getLogger(AliyunSmsServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private final ObjectMapper objectMapper;
    private Client client;

    @Autowired
    public AliyunSmsServiceImpl(ApplicationProperties applicationProperties, ObjectMapper objectMapper) {
        this.applicationProperties = applicationProperties;
        this.objectMapper = objectMapper;
        initClient();
    }

    /**
     * 初始化阿里云短信客户端
     */
    private void initClient() {
        try {
            ApplicationProperties.Notification.Sms smsProperties = applicationProperties.getNotification().getSms();
            if (!smsProperties.isEnabled()) {
                log.warn("阿里云短信服务未启用");
                return;
            }

            Config config = new Config()
                .setAccessKeyId(smsProperties.getAccessKeyId())
                .setAccessKeySecret(smsProperties.getAccessKeySecret())
                .setEndpoint(smsProperties.getEndpoint());

            this.client = new Client(config);
            log.info("阿里云短信客户端初始化成功");
        } catch (Exception e) {
            log.error("阿里云短信客户端初始化失败", e);
        }
    }

    @Override
    public NotificationResponse send(SmsRequest request) {
        if (client == null) {
            return NotificationResponse.failure("阿里云短信服务未初始化");
        }

        try {
            ApplicationProperties.Notification.Sms smsProperties = applicationProperties.getNotification().getSms();

            // 构建请求参数
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(request.getRecipient())
                .setSignName(smsProperties.getSignName())
                .setTemplateCode(request.getTemplateId())
                .setTemplateParam(convertMapToJson(request.getTemplateParams()));

            // 发送短信
            SendSmsResponse response = client.sendSms(sendSmsRequest);

            // 处理响应
            if ("OK".equals(response.getBody().getCode())) {
                NotificationResponse notificationResponse = NotificationResponse.success("短信发送成功", response.getBody().getBizId());
                notificationResponse.setType(NotificationType.SMS);
                return notificationResponse;
            } else {
                log.error("阿里云短信发送失败: {}, {}", response.getBody().getCode(), response.getBody().getMessage());
                NotificationResponse notificationResponse = NotificationResponse.failure(
                    response.getBody().getMessage(),
                    response.getBody().getCode()
                );
                notificationResponse.setType(NotificationType.SMS);
                return notificationResponse;
            }
        } catch (Exception e) {
            log.error("阿里云短信发送异常", e);
            return NotificationResponse.failure("短信发送异常: " + e.getMessage());
        }
    }

    /**
     * 将Map转换为JSON字符串
     */
    private String convertMapToJson(Map<String, Object> map) throws JsonProcessingException {
        if (map == null || map.isEmpty()) {
            return "{}";
        }
        return objectMapper.writeValueAsString(map);
    }
}
