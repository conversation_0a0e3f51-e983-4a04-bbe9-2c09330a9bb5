package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.repository.NotificationRecordRepository;
import com.whiskerguard.general.service.NotificationRecordService;
import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import com.whiskerguard.general.service.mapper.NotificationRecordMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.general.domain.NotificationRecord}.
 */
@Service
@Transactional
public class NotificationRecordServiceImpl implements NotificationRecordService {

    private static final Logger LOG = LoggerFactory.getLogger(NotificationRecordServiceImpl.class);

    private final NotificationRecordRepository notificationRecordRepository;

    private final NotificationRecordMapper notificationRecordMapper;

    public NotificationRecordServiceImpl(
        NotificationRecordRepository notificationRecordRepository,
        NotificationRecordMapper notificationRecordMapper
    ) {
        this.notificationRecordRepository = notificationRecordRepository;
        this.notificationRecordMapper = notificationRecordMapper;
    }

    @Override
    public NotificationRecordDTO save(NotificationRecordDTO notificationRecordDTO) {
        LOG.debug("Request to save NotificationRecord : {}", notificationRecordDTO);
        NotificationRecord notificationRecord = notificationRecordMapper.toEntity(notificationRecordDTO);
        notificationRecord = notificationRecordRepository.save(notificationRecord);
        return notificationRecordMapper.toDto(notificationRecord);
    }

    @Override
    public NotificationRecordDTO update(NotificationRecordDTO notificationRecordDTO) {
        LOG.debug("Request to update NotificationRecord : {}", notificationRecordDTO);
        NotificationRecord notificationRecord = notificationRecordMapper.toEntity(notificationRecordDTO);
        notificationRecord = notificationRecordRepository.save(notificationRecord);
        return notificationRecordMapper.toDto(notificationRecord);
    }

    @Override
    public Optional<NotificationRecordDTO> partialUpdate(NotificationRecordDTO notificationRecordDTO) {
        LOG.debug("Request to partially update NotificationRecord : {}", notificationRecordDTO);

        return notificationRecordRepository
            .findById(notificationRecordDTO.getId())
            .map(existingNotificationRecord -> {
                notificationRecordMapper.partialUpdate(existingNotificationRecord, notificationRecordDTO);

                return existingNotificationRecord;
            })
            .map(notificationRecordRepository::save)
            .map(notificationRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NotificationRecordDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NotificationRecords");
        return notificationRecordRepository.findAll(pageable).map(notificationRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NotificationRecordDTO> findOne(Long id) {
        LOG.debug("Request to get NotificationRecord : {}", id);
        return notificationRecordRepository.findById(id).map(notificationRecordMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NotificationRecord : {}", id);
        notificationRecordRepository.deleteById(id);
    }
}
