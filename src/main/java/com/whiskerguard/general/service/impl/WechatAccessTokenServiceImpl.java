package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.service.WechatAccessTokenService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信公众号Access Token管理服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/25
 */
@Service
public class WechatAccessTokenServiceImpl implements WechatAccessTokenService {

    private static final Logger log = LoggerFactory.getLogger(WechatAccessTokenServiceImpl.class);

    private WxMpService wxMpService;

    private final ApplicationProperties applicationProperties;

    @Autowired
    public WechatAccessTokenServiceImpl(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @PostConstruct
    public void init() {
        try {
            ApplicationProperties.Notification.Wechat wechatProperties =
                applicationProperties.getNotification().getWechat();

            if (!wechatProperties.isEnabled()) {
                log.warn("微信公众号服务未启用");
                return;
            }

            WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
            config.setAppId(wechatProperties.getAppId());
            config.setSecret(wechatProperties.getAppSecret());
            config.setToken(wechatProperties.getToken());
            config.setAesKey(wechatProperties.getAesKey());

            wxMpService = new WxMpServiceImpl();
            wxMpService.setWxMpConfigStorage(config);

            log.info("微信公众号服务初始化成功，AppId: {}", wechatProperties.getAppId());
        } catch (Exception e) {
            log.error("微信公众号服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("微信公众号服务已关闭");
    }

    @Override
    public String getAccessToken() {
        try {
            if (wxMpService == null) {
                log.error("微信公众号服务未初始化");
                return null;
            }

            String accessToken = wxMpService.getAccessToken();
            log.debug("获取Access Token成功: {}", maskToken(accessToken));
            return accessToken;
        } catch (WxErrorException e) {
            log.error("获取Access Token失败: 错误代码={}, 错误信息={}",
                e.getError().getErrorCode(), e.getError().getErrorMsg());
            return null;
        } catch (Exception e) {
            log.error("获取Access Token异常", e);
            return null;
        }
    }

    @Override
    public String refreshAccessToken() {
        try {
            if (wxMpService == null) {
                return null;
            }

            // 清除当前缓存的token
            clearAccessTokenCache();

            // 获取新的token
            String newAccessToken = wxMpService.getAccessToken(true);
            log.info("Access Token刷新成功: {}", maskToken(newAccessToken));
            return newAccessToken;

        } catch (WxErrorException e) {
            log.error("刷新Access Token失败: 错误代码={}, 错误信息={}",
                e.getError().getErrorCode(), e.getError().getErrorMsg());
            return null;
        } catch (Exception e) {
            log.error("刷新Access Token异常", e);
            return null;
        }
    }

    @Override
    public boolean isAccessTokenValid(String accessToken) {
        if (accessToken == null || accessToken.isEmpty()) {
            return false;
        }

        try {
            // 通过调用一个简单的API来验证token是否有效
            wxMpService.getUserService().userList(null);
            return true;
        } catch (WxErrorException e) {
            // 40001: access_token无效
            // 40014: access_token不合法
            // 42001: access_token超时
            if (e.getError().getErrorCode() == 40001 ||
                e.getError().getErrorCode() == 40014 ||
                e.getError().getErrorCode() == 42001) {
                log.warn("Access Token无效: 错误代码={}, 错误信息={}",
                    e.getError().getErrorCode(), e.getError().getErrorMsg());
                return false;
            }
            // 其他错误可能不是token问题
            log.debug("验证Access Token时发生其他错误: {}", e.getMessage());
            return true;
        } catch (Exception e) {
            log.error("验证Access Token异常", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getAccessTokenInfo() {
        Map<String, Object> info = new HashMap<>();

        try {
            String accessToken = getAccessToken();
            if (accessToken != null) {
                info.put("accessToken", accessToken);
                info.put("isValid", isAccessTokenValid(accessToken));
                info.put("remainingTime", getAccessTokenRemainingTime());
                info.put("retrievedAt", Instant.now());

                // 获取配置信息
                if (wxMpService.getWxMpConfigStorage() != null) {
                    info.put("appId", wxMpService.getWxMpConfigStorage().getAppId());
                    info.put("expiresTime", wxMpService.getWxMpConfigStorage().getExpiresTime());
                }
            } else {
                info.put("error", "无法获取Access Token");
            }
        } catch (Exception e) {
            log.error("获取Access Token信息异常", e);
            info.put("error", "获取信息异常: " + e.getMessage());
        }

        return info;
    }

    @Override
    public void clearAccessTokenCache() {
        try {
            if (wxMpService != null && wxMpService.getWxMpConfigStorage() != null) {
                wxMpService.getWxMpConfigStorage().expireAccessToken();
                log.info("Access Token缓存已清理");
            }
        } catch (Exception e) {
            log.error("清理Access Token缓存异常", e);
        }
    }

    @Override
    public long getAccessTokenRemainingTime() {
        try {
            if (wxMpService != null && wxMpService.getWxMpConfigStorage() != null) {
                long expiresTime = wxMpService.getWxMpConfigStorage().getExpiresTime();
                long currentTime = System.currentTimeMillis();

                if (expiresTime > currentTime) {
                    // 转换为秒
                    return (expiresTime - currentTime) / 1000;
                }
            }
        } catch (Exception e) {
            log.error("获取Access Token剩余时间异常", e);
        }
        // 表示已过期或无效
        return -1;
    }


    /**
     * 掩码处理Access Token，用于日志输出
     */
    private String maskToken(String token) {
        if (token == null || token.length() < 10) {
            return "***";
        }
        return token.substring(0, 6) + "***" + token.substring(token.length() - 4);
    }
}
