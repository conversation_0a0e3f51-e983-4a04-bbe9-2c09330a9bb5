package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.domain.UserNotificationPreference;
import com.whiskerguard.general.repository.UserNotificationPreferenceRepository;
import com.whiskerguard.general.service.UserNotificationPreferenceService;
import com.whiskerguard.general.service.dto.UserNotificationPreferenceDTO;
import com.whiskerguard.general.service.mapper.UserNotificationPreferenceMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.general.domain.UserNotificationPreference}.
 */
@Service
@Transactional
public class UserNotificationPreferenceServiceImpl implements UserNotificationPreferenceService {

    private static final Logger LOG = LoggerFactory.getLogger(UserNotificationPreferenceServiceImpl.class);

    private final UserNotificationPreferenceRepository userNotificationPreferenceRepository;

    private final UserNotificationPreferenceMapper userNotificationPreferenceMapper;

    public UserNotificationPreferenceServiceImpl(
        UserNotificationPreferenceRepository userNotificationPreferenceRepository,
        UserNotificationPreferenceMapper userNotificationPreferenceMapper
    ) {
        this.userNotificationPreferenceRepository = userNotificationPreferenceRepository;
        this.userNotificationPreferenceMapper = userNotificationPreferenceMapper;
    }

    @Override
    public UserNotificationPreferenceDTO save(UserNotificationPreferenceDTO userNotificationPreferenceDTO) {
        LOG.debug("Request to save UserNotificationPreference : {}", userNotificationPreferenceDTO);
        UserNotificationPreference userNotificationPreference = userNotificationPreferenceMapper.toEntity(userNotificationPreferenceDTO);
        userNotificationPreference = userNotificationPreferenceRepository.save(userNotificationPreference);
        return userNotificationPreferenceMapper.toDto(userNotificationPreference);
    }

    @Override
    public UserNotificationPreferenceDTO update(UserNotificationPreferenceDTO userNotificationPreferenceDTO) {
        LOG.debug("Request to update UserNotificationPreference : {}", userNotificationPreferenceDTO);
        UserNotificationPreference userNotificationPreference = userNotificationPreferenceMapper.toEntity(userNotificationPreferenceDTO);
        userNotificationPreference = userNotificationPreferenceRepository.save(userNotificationPreference);
        return userNotificationPreferenceMapper.toDto(userNotificationPreference);
    }

    @Override
    public Optional<UserNotificationPreferenceDTO> partialUpdate(UserNotificationPreferenceDTO userNotificationPreferenceDTO) {
        LOG.debug("Request to partially update UserNotificationPreference : {}", userNotificationPreferenceDTO);

        return userNotificationPreferenceRepository
            .findById(userNotificationPreferenceDTO.getId())
            .map(existingUserNotificationPreference -> {
                userNotificationPreferenceMapper.partialUpdate(existingUserNotificationPreference, userNotificationPreferenceDTO);

                return existingUserNotificationPreference;
            })
            .map(userNotificationPreferenceRepository::save)
            .map(userNotificationPreferenceMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserNotificationPreferenceDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all UserNotificationPreferences");
        return userNotificationPreferenceRepository.findAll(pageable).map(userNotificationPreferenceMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserNotificationPreferenceDTO> findOne(Long id) {
        LOG.debug("Request to get UserNotificationPreference : {}", id);
        return userNotificationPreferenceRepository.findById(id).map(userNotificationPreferenceMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete UserNotificationPreference : {}", id);
        userNotificationPreferenceRepository.deleteById(id);
    }
}
