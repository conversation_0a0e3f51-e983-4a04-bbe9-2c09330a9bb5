package com.whiskerguard.general.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.domain.SignatureDocument;
import com.whiskerguard.general.domain.enumeration.SignatureDocumentStatus;
import com.whiskerguard.general.domain.enumeration.SignatureProvider;
import com.whiskerguard.general.repository.SignatureDocumentRepository;
import com.whiskerguard.general.service.ElectronicSignatureService;
import com.whiskerguard.general.service.dto.*;
import com.whiskerguard.general.service.exception.EntityNotFoundException;
import com.whiskerguard.general.service.mapper.SignatureDocumentMapper;
import java.time.Instant;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

/**
 * 法大大电子签名服务实现类
 * <p>
 * 实现了与法大大电子签名平台的集成，包括创建签名任务、查询签名状态、处理回调等功能。
 * </p>
 *
 * <AUTHOR> Yan
 */
@Service("fadadaSignatureService")
@ConditionalOnProperty(prefix = "application.electronic-signature.fadada", name = "enabled", havingValue = "true")
public class FadadaSignatureServiceImpl implements ElectronicSignatureService {

    private static final Logger log = LoggerFactory.getLogger(FadadaSignatureServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private final SignatureDocumentRepository signatureDocumentRepository;
    private final SignatureDocumentMapper signatureDocumentMapper;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public FadadaSignatureServiceImpl(
        ApplicationProperties applicationProperties,
        SignatureDocumentRepository signatureDocumentRepository,
        SignatureDocumentMapper signatureDocumentMapper
    ) {
        this.applicationProperties = applicationProperties;
        this.signatureDocumentRepository = signatureDocumentRepository;
        this.signatureDocumentMapper = signatureDocumentMapper;

        // 使用 SimpleClientHttpRequestFactory 配置 RestTemplate，避免使用 HttpClient
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(5000); // 连接超时设置为5秒
        factory.setReadTimeout(15000); // 读取超时设置为15秒
        this.restTemplate = new RestTemplate(factory);
        this.objectMapper = new ObjectMapper();
    }

    @Override
    @Transactional
    public SignatureResultDTO createSignatureTask(SignatureRequestDTO request) {
        log.debug("创建法大大签名任务: {}", request);
        try {
            // 1. 准备请求参数
            Map<String, Object> requestParams = prepareSignatureParams(request);

            // 2. 发送请求到法大大API
            String apiUrl = applicationProperties.getElectronicSignature().getFadada().getApiUrl() + "/signature/create";
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, requestEntity, Map.class);
            Map<String, Object> responseBody = response.getBody();

            // 3. 处理响应
            if (responseBody != null && "0000".equals(responseBody.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) responseBody.get("data");

                // 4. 保存签名文档记录
                SignatureDocument document = new SignatureDocument();
                document.setTitle(request.getTitle());
                document.setDescription(request.getDescription());
                document.setDocumentUrl(request.getDocumentUrl());
                document.setStatus(SignatureDocumentStatus.PENDING);
                document.setProvider(SignatureProvider.FADADA);
                document.setExternalId(data.get("documentId").toString());
                document.setTransactionId(data.get("transactionId").toString());
                document.setUserId(request.getUserId());
                document.setExpireTime(request.getExpireTime());
                document.setVersion(1);
                document.setCreatedBy(request.getUserId());
                document.setCreatedAt(Instant.now());
                document.setUpdatedBy(request.getUserId());
                document.setUpdatedAt(Instant.now());
                document.setIsDeleted(false);

                // 保存元数据
                if (request.getMetadata() != null) {
                    document.setMetadata(objectMapper.writeValueAsString(request.getMetadata()));
                }

                SignatureDocument savedDocument = signatureDocumentRepository.save(document);

                // 5. 构建返回结果
                return SignatureResultDTO.success(
                    savedDocument.getId(),
                    savedDocument.getExternalId(),
                    savedDocument.getTransactionId(),
                    data.get("signUrl").toString(),
                    SignatureDocumentStatus.PENDING
                );
            } else {
                String errorMsg = responseBody != null ? responseBody.get("message").toString() : "未知错误";
                log.error("法大大签名任务创建失败: {}", errorMsg);
                return SignatureResultDTO.failure("法大大签名任务创建失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("创建法大大签名任务异常", e);
            return SignatureResultDTO.failure("创建法大大签名任务异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public SignatureDocumentDTO querySignatureStatus(Long documentId) {
        log.debug("查询法大大签名状态: {}", documentId);
        try {
            // 1. 查询本地文档记录
            Optional<SignatureDocument> documentOpt = signatureDocumentRepository.findById(documentId);
            if (documentOpt.isEmpty()) {
                log.error("签名文档不存在: {}", documentId);
                return null;
            }

            SignatureDocument document = documentOpt.orElseThrow(() -> new EntityNotFoundException("签名文档不存在"));

            // 2. 如果文档已经是终态，直接返回
            if (isTerminalStatus(document.getStatus())) {
                return signatureDocumentMapper.toDto(document);
            }

            // 3. 准备请求参数
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("documentId", document.getExternalId());
            requestParams.put("transactionId", document.getTransactionId());

            // 4. 发送请求到法大大API
            String apiUrl = applicationProperties.getElectronicSignature().getFadada().getApiUrl() + "/signature/status";
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, requestEntity, Map.class);
            Map<String, Object> responseBody = response.getBody();

            // 5. 处理响应
            if (responseBody != null && "0000".equals(responseBody.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                String status = data.get("status").toString();

                // 6. 更新本地文档状态（如果需要）
                // 注意：这里只是查询，不更新状态，状态更新由回调处理

                return signatureDocumentMapper.toDto(document);
            } else {
                log.error("查询法大大签名状态失败: {}", responseBody);
                return signatureDocumentMapper.toDto(document);
            }
        } catch (Exception e) {
            log.error("查询法大大签名状态异常", e);
            return null;
        }
    }

    @Override
    @Transactional
    public Map<String, Object> handleCallback(SignatureCallbackDTO callbackDTO, Map<String, String> params) {
        log.debug("处理法大大签名回调: {}", callbackDTO);
        try {
            // 1. 验证回调签名
            if (!verifyCallbackSignature(params)) {
                log.error("法大大回调签名验证失败");
                return Collections.singletonMap("result", "签名验证失败");
            }

            // 2. 查询本地文档记录
            Optional<SignatureDocument> documentOpt = signatureDocumentRepository.findOne(
                Specification.where((root, query, cb) ->
                    cb.and(
                        cb.equal(root.get("externalId"), callbackDTO.getExternalId()),
                        cb.equal(root.get("provider"), SignatureProvider.FADADA)
                    )
                )
            );

            if (documentOpt.isEmpty()) {
                log.error("签名文档不存在: {}", callbackDTO.getExternalId());
                return Collections.singletonMap("result", "文档不存在");
            }

            SignatureDocument document = documentOpt.orElseThrow(() -> new EntityNotFoundException("签名文档不存在"));

            // 3. 更新文档状态
            document.setStatus(callbackDTO.getStatus());
            document.setSignedTime(callbackDTO.getSignedTime());
            document.setSignedDocumentUrl(callbackDTO.getSignedDocumentUrl());
            document.setUpdatedAt(Instant.now());

            signatureDocumentRepository.save(document);

            return Collections.singletonMap("result", "success");
        } catch (Exception e) {
            log.error("处理法大大签名回调异常", e);
            return Collections.singletonMap("result", "处理异常: " + e.getMessage());
        }
    }

    @Override
    public String getSignedDocument(Long documentId) {
        log.debug("获取法大大签名后的文档: {}", documentId);
        try {
            // 1. 查询本地文档记录
            Optional<SignatureDocument> documentOpt = signatureDocumentRepository.findById(documentId);
            if (documentOpt.isEmpty()) {
                log.error("签名文档不存在: {}", documentId);
                return null;
            }

            SignatureDocument document = documentOpt.orElseThrow(() -> new EntityNotFoundException("签名文档不存在"));

            // 2. 如果文档已签署，直接返回签署后的文档URL
            if (SignatureDocumentStatus.SIGNED.equals(document.getStatus()) && document.getSignedDocumentUrl() != null) {
                return document.getSignedDocumentUrl();
            }

            // 3. 如果文档未签署或签署后的文档URL为空，尝试从法大大获取
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("documentId", document.getExternalId());
            requestParams.put("transactionId", document.getTransactionId());

            String apiUrl = applicationProperties.getElectronicSignature().getFadada().getApiUrl() + "/signature/document";
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, requestEntity, Map.class);
            Map<String, Object> responseBody = response.getBody();

            if (responseBody != null && "0000".equals(responseBody.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                String signedDocumentUrl = data.get("documentUrl").toString();

                // 更新本地文档记录
                document.setSignedDocumentUrl(signedDocumentUrl);
                document.setUpdatedAt(Instant.now());
                signatureDocumentRepository.save(document);

                return signedDocumentUrl;
            } else {
                log.error("获取法大大签名后的文档失败: {}", responseBody);
                return null;
            }
        } catch (Exception e) {
            log.error("获取法大大签名后的文档异常", e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean cancelSignatureTask(Long documentId) {
        log.debug("取消法大大签名任务: {}", documentId);
        try {
            // 1. 查询本地文档记录
            Optional<SignatureDocument> documentOpt = signatureDocumentRepository.findById(documentId);
            if (documentOpt.isEmpty()) {
                log.error("签名文档不存在: {}", documentId);
                return false;
            }

            SignatureDocument document = documentOpt.orElseThrow(() -> new EntityNotFoundException("签名文档不存在"));

            // 2. 如果文档已经是终态，无法取消
            if (isTerminalStatus(document.getStatus())) {
                log.error("签名文档已处于终态，无法取消: {}, {}", documentId, document.getStatus());
                return false;
            }

            // 3. 准备请求参数
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("documentId", document.getExternalId());
            requestParams.put("transactionId", document.getTransactionId());

            // 4. 发送请求到法大大API
            String apiUrl = applicationProperties.getElectronicSignature().getFadada().getApiUrl() + "/signature/cancel";
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, requestEntity, Map.class);
            Map<String, Object> responseBody = response.getBody();

            // 5. 处理响应
            if (responseBody != null && "0000".equals(responseBody.get("code"))) {
                // 6. 更新本地文档状态
                document.setStatus(SignatureDocumentStatus.CANCELLED);
                document.setUpdatedAt(Instant.now());
                signatureDocumentRepository.save(document);

                return true;
            } else {
                log.error("取消法大大签名任务失败: {}", responseBody);
                return false;
            }
        } catch (Exception e) {
            log.error("取消法大大签名任务异常", e);
            return false;
        }
    }

    /**
     * 准备签名请求参数
     *
     * @param request 签名请求
     * @return 请求参数
     */
    private Map<String, Object> prepareSignatureParams(SignatureRequestDTO request) {
        Map<String, Object> params = new HashMap<>();
        params.put("title", request.getTitle());
        params.put("documentUrl", request.getDocumentUrl());

        // 设置回调URL
        String callbackUrl =
            applicationProperties.getElectronicSignature().getCallbackBaseUrl() +
            applicationProperties.getElectronicSignature().getFadada().getCallbackUrl();
        params.put("callbackUrl", callbackUrl);

        // 设置签署人信息
        List<Map<String, Object>> signers = new ArrayList<>();
        for (SignerInfoDTO signer : request.getSigners()) {
            Map<String, Object> signerMap = new HashMap<>();
            signerMap.put("name", signer.getName());
            signerMap.put("mobile", signer.getMobile());
            if (signer.getIdCard() != null) {
                signerMap.put("idCard", signer.getIdCard());
            }
            if (signer.getEmail() != null) {
                signerMap.put("email", signer.getEmail());
            }
            if (signer.getOrder() != null) {
                signerMap.put("order", signer.getOrder());
            }
            if (signer.getCompanyName() != null) {
                signerMap.put("companyName", signer.getCompanyName());
            }
            signers.add(signerMap);
        }
        params.put("signers", signers);

        // 设置过期时间
        if (request.getExpireTime() != null) {
            params.put("expireTime", request.getExpireTime().toEpochMilli());
        }

        return params;
    }

    /**
     * 创建请求头
     *
     * @return HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String appId = applicationProperties.getElectronicSignature().getFadada().getAppId();
        String appSecret = applicationProperties.getElectronicSignature().getFadada().getAppSecret();
        String timestamp = String.valueOf(System.currentTimeMillis());

        // 计算签名
        String signContent = appId + timestamp + appSecret;
        String sign = DigestUtils.md5DigestAsHex(signContent.getBytes());

        headers.set("X-Fadada-AppId", appId);
        headers.set("X-Fadada-Timestamp", timestamp);
        headers.set("X-Fadada-Sign", sign);

        return headers;
    }

    /**
     * 验证回调签名
     *
     * @param params 回调参数
     * @return 是否验证通过
     */
    private boolean verifyCallbackSignature(Map<String, String> params) {
        String sign = params.get("sign");
        if (sign == null) {
            return false;
        }

        String appId = applicationProperties.getElectronicSignature().getFadada().getAppId();
        String appSecret = applicationProperties.getElectronicSignature().getFadada().getAppSecret();
        String timestamp = params.get("timestamp");

        // 计算签名
        String signContent = appId + timestamp + appSecret;
        String calculatedSign = DigestUtils.md5DigestAsHex(signContent.getBytes());

        return calculatedSign.equals(sign);
    }

    /**
     * 判断是否为终态
     *
     * @param status 状态
     * @return 是否为终态
     */
    private boolean isTerminalStatus(SignatureDocumentStatus status) {
        return (
            SignatureDocumentStatus.SIGNED.equals(status) ||
            SignatureDocumentStatus.REJECTED.equals(status) ||
            SignatureDocumentStatus.FAILED.equals(status) ||
            SignatureDocumentStatus.EXPIRED.equals(status) ||
            SignatureDocumentStatus.CANCELLED.equals(status)
        );
    }
}
