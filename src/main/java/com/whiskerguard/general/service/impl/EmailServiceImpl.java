package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.EmailRequest;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.NotificationType;
import com.whiskerguard.general.service.EmailService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

/**
 * 邮件服务实现
 */
@Service
public class EmailServiceImpl implements EmailService {

    private static final Logger log = LoggerFactory.getLogger(EmailServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private final JavaMailSender javaMailSender;
    private final SpringTemplateEngine templateEngine;

    @Autowired
    public EmailServiceImpl(
        ApplicationProperties applicationProperties,
        @Autowired(required = false) JavaMailSender javaMailSender,
        @Autowired(required = false) SpringTemplateEngine templateEngine
    ) {
        this.applicationProperties = applicationProperties;
        this.javaMailSender = javaMailSender;
        this.templateEngine = templateEngine;
    }

    @Override
    public NotificationResponse send(EmailRequest request) {
        if (!applicationProperties.getNotification().getEmail().isEnabled()) {
            return NotificationResponse.failure("邮件服务未启用");
        }

        if (javaMailSender == null) {
            log.warn("JavaMailSender未配置，无法发送邮件");
            return NotificationResponse.failure("邮件服务未配置");
        }

        try {
            // 创建邮件消息
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, StandardCharsets.UTF_8.name());

            // 设置发件人
            message.setFrom(applicationProperties.getNotification().getEmail().getFrom());

            // 设置收件人
            message.setTo(request.getRecipient());

            // 设置抄送人
            if (request.getCc() != null && !request.getCc().isEmpty()) {
                message.setCc(request.getCc().toArray(new String[0]));
            }

            // 设置密送人
            if (request.getBcc() != null && !request.getBcc().isEmpty()) {
                message.setBcc(request.getBcc().toArray(new String[0]));
            }

            // 设置主题
            message.setSubject(request.getSubject());

            // 设置内容
            if (request.getTemplateId() != null && templateEngine != null) {
                // 使用模板
                String content = createContentFromTemplate(request.getTemplateId(), request.getTemplateParams());
                message.setText(content, true);
            } else {
                // 直接使用内容
                message.setText(request.getContent() != null ? request.getContent() : "", request.isHtml());
            }

            // 发送邮件
            javaMailSender.send(mimeMessage);

            // 返回成功响应
            NotificationResponse response = NotificationResponse.success("邮件发送成功");
            response.setType(NotificationType.EMAIL);
            return response;
        } catch (MessagingException e) {
            log.error("邮件发送失败", e);
            return NotificationResponse.failure("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 使用Thymeleaf模板创建邮件内容
     *
     * @param templateName 模板名称
     * @param variables    模板变量
     * @return 渲染后的内容
     */
    private String createContentFromTemplate(String templateName, Map<String, Object> variables) {
        if (templateEngine == null) {
            log.warn("Thymeleaf模板引擎未配置，无法使用模板");
            return "模板引擎未配置，请检查配置或直接使用内容";
        }

        Context context = new Context(Locale.CHINA);
        if (variables != null) {
            context.setVariables(variables);
        }
        return templateEngine.process(templateName, context);
    }
}
