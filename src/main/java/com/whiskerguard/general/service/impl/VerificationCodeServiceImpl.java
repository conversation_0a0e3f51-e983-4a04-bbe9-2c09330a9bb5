package com.whiskerguard.general.service.impl;

import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.domain.enumeration.VerificationCodeType;
import com.whiskerguard.general.model.*;
import com.whiskerguard.general.service.NotificationService;
import com.whiskerguard.general.service.VerificationCodeService;
import lombok.Data;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.io.Serial;
import java.io.Serializable;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 验证码服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
@Service
public class VerificationCodeServiceImpl implements VerificationCodeService {

    private static final Logger log = LoggerFactory.getLogger(VerificationCodeServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private final NotificationService notificationService;
    private final RedissonClient redissonClient;
    private final Environment environment;
    private final Random random = new Random();

    @Autowired
    public VerificationCodeServiceImpl(
        ApplicationProperties applicationProperties,
        NotificationService notificationService,
        RedissonClient redissonClient,
        Environment environment
    ) {
        this.applicationProperties = applicationProperties;
        this.notificationService = notificationService;
        this.redissonClient = redissonClient;
        this.environment = environment;
    }

    @Override
    public VerificationCodeResponse sendVerificationCode(VerificationCodeRequest request) {
        ApplicationProperties.VerificationCode config = applicationProperties.getVerificationCode();

        if (!config.isEnabled()) {
            return VerificationCodeResponse.failure("验证码服务未启用");
        }

        String phoneNumber = request.getPhoneNumber();
        VerificationCodeType codeType = request.getCodeType();

        try {
            // 检查是否可以发送验证码
            if (!canSendVerificationCode(phoneNumber, codeType)) {
                return VerificationCodeResponse.failure("发送过于频繁，请稍后再试");
            }

            // 生成验证码
            String code = generateVerificationCode(config.getCodeLength());

            // 存储验证码到Redis
            String redisKey = buildRedisKey(phoneNumber, codeType);
            RBucket<VerificationCodeData> bucket = redissonClient.getBucket(redisKey);
            VerificationCodeData codeData = new VerificationCodeData();
            codeData.setCode(code);
            codeData.setPhoneNumber(phoneNumber);
            codeData.setCodeType(codeType);
            codeData.setCreatedAt(Instant.now());
            codeData.setExpiresAt(Instant.now().plusSeconds(config.getExpirationMinutes() * 60L));
            bucket.set(codeData);
            bucket.expire(Duration.ofMinutes(config.getExpirationMinutes()));

            // 发送短信
            NotificationResponse smsResponse = sendSmsVerificationCode(request, code, TenantContextUtil.getCurrentTenantId());

            if (smsResponse.isSuccess()) {
                // 构建响应
                VerificationCodeResponse response = VerificationCodeResponse.success("验证码发送成功");
                response.setPhoneNumber(VerificationCodeResponse.maskPhoneNumber(phoneNumber));
                response.setCodeType(codeType);
                response.setSentAt(codeData.getCreatedAt());
                response.setNextSendTime(Instant.now().plusSeconds(config.getSendIntervalSeconds()));

                // 开发环境返回验证码
                if (isDevelopmentEnvironment()) {
                    response.setCode(code);
                }

                log.info("验证码发送成功: 手机号={}, 类型={}", VerificationCodeResponse.maskPhoneNumber(phoneNumber), codeType);

                return response;
            } else {
                // 短信发送失败，删除Redis中的验证码
                bucket.delete();
                return VerificationCodeResponse.failure("验证码发送失败: " + smsResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("发送验证码异常: 手机号={}, 类型={}", phoneNumber, codeType, e);
            return VerificationCodeResponse.failure("验证码发送失败: " + e.getMessage());
        }
    }

    @Override
    public VerificationCodeResponse validateVerificationCode(VerificationCodeValidateRequest request) {
        ApplicationProperties.VerificationCode config = applicationProperties.getVerificationCode();

        if (!config.isEnabled()) {
            return VerificationCodeResponse.failure("验证码服务未启用");
        }

        String phoneNumber = request.getPhoneNumber();
        String inputCode = request.getCode();
        VerificationCodeType codeType = request.getCodeType();

        try {
            // 从Redis获取验证码
            String redisKey = buildRedisKey(phoneNumber, codeType);
            RBucket<VerificationCodeData> bucket = redissonClient.getBucket(redisKey);
            VerificationCodeData codeData = bucket.get();

            // 检查验证码是否过期
            if (Instant.now().isAfter(codeData.getExpiresAt())) {
                bucket.delete();
                return VerificationCodeResponse.failure("验证码已过期");
            }

            // 验证验证码
            if (!inputCode.equals(codeData.getCode())) {
                return VerificationCodeResponse.failure("验证码错误");
            }

            // 验证成功
            if (request.isDeleteAfterValidation()) {
                bucket.delete();
            }

            VerificationCodeResponse response = VerificationCodeResponse.success("验证码验证成功");
            response.setPhoneNumber(VerificationCodeResponse.maskPhoneNumber(phoneNumber));
            response.setCodeType(codeType);

            log.info("验证码验证成功: 手机号={}, 类型={}", VerificationCodeResponse.maskPhoneNumber(phoneNumber), codeType);

            return response;
        } catch (Exception e) {
            log.error("验证验证码异常: 手机号={}, 类型={}", phoneNumber, codeType, e);
            return VerificationCodeResponse.failure("验证码验证失败: " + e.getMessage());
        }
    }

    @Override
    public boolean canSendVerificationCode(String phoneNumber, VerificationCodeType codeType) {
        ApplicationProperties.VerificationCode config = applicationProperties.getVerificationCode();

        // 检查发送间隔
        String lastSendKey = buildLastSendTimeKey(phoneNumber, codeType, TenantContextUtil.getCurrentTenantId());
        RBucket<Instant> lastSendBucket = redissonClient.getBucket(lastSendKey);
        Instant lastSendTime = lastSendBucket.get();

        if (lastSendTime != null) {
            Instant nextAllowedTime = lastSendTime.plusSeconds(config.getSendIntervalSeconds());
            return !Instant.now().isBefore(nextAllowedTime);
        }

        return true;
    }

    private String generateVerificationCode(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    private String buildRedisKey(String phoneNumber, VerificationCodeType codeType) {
        return "verification_code:" + ":" + phoneNumber + ":" + codeType.name();
    }

    private String buildLastSendTimeKey(String phoneNumber, VerificationCodeType codeType, Long tenantId) {
        return "last_send_time:" + tenantId + ":" + phoneNumber + ":" + codeType.name();
    }

    private NotificationResponse sendSmsVerificationCode(VerificationCodeRequest request, String code, Long tenantId) {
        SmsRequest smsRequest = new SmsRequest();
        smsRequest.setRecipient(request.getPhoneNumber());
        smsRequest.setTenantId(tenantId);
        smsRequest.setRegionCode(request.getRegionCode());
        smsRequest.setProviderType(SmsProviderType.TENCENT);

        // 设置模板参数 ["0986", "5分钟"]
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("code", code);
        templateParams.put("time", "5");
        smsRequest.setTemplateParams(templateParams);

        return notificationService.sendSms(smsRequest);
    }

    private boolean isDevelopmentEnvironment() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("dev".equals(profile) || "development".equals(profile)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证码数据存储类
     */
    @Data
    public static class VerificationCodeData implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private String code;
        private String phoneNumber;
        private VerificationCodeType codeType;
        private Instant createdAt;
        private Instant expiresAt;
    }
}
