package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.domain.SignatureDocument;
import com.whiskerguard.general.domain.enumeration.SignatureProvider;
import com.whiskerguard.general.repository.SignatureDocumentRepository;
import com.whiskerguard.general.service.ElectronicSignatureFactory;
import com.whiskerguard.general.service.ElectronicSignatureService;
import com.whiskerguard.general.service.SignatureDocumentService;
import com.whiskerguard.general.service.dto.*;
import com.whiskerguard.general.service.exception.EntityNotFoundException;
import com.whiskerguard.general.service.mapper.SignatureDocumentMapper;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.general.domain.SignatureDocument}.
 */
@Service
@Transactional
public class SignatureDocumentServiceImpl implements SignatureDocumentService {

    private static final Logger LOG = LoggerFactory.getLogger(SignatureDocumentServiceImpl.class);

    private final SignatureDocumentRepository signatureDocumentRepository;
    private final SignatureDocumentMapper signatureDocumentMapper;
    private final ElectronicSignatureFactory electronicSignatureFactory;

    public SignatureDocumentServiceImpl(
        SignatureDocumentRepository signatureDocumentRepository,
        SignatureDocumentMapper signatureDocumentMapper,
        ElectronicSignatureFactory electronicSignatureFactory
    ) {
        this.signatureDocumentRepository = signatureDocumentRepository;
        this.signatureDocumentMapper = signatureDocumentMapper;
        this.electronicSignatureFactory = electronicSignatureFactory;
    }

    @Override
    public SignatureDocumentDTO save(SignatureDocumentDTO signatureDocumentDTO) {
        LOG.debug("Request to save SignatureDocument : {}", signatureDocumentDTO);
        SignatureDocument signatureDocument = signatureDocumentMapper.toEntity(signatureDocumentDTO);
        signatureDocument = signatureDocumentRepository.save(signatureDocument);
        return signatureDocumentMapper.toDto(signatureDocument);
    }

    @Override
    public SignatureDocumentDTO update(SignatureDocumentDTO signatureDocumentDTO) {
        LOG.debug("Request to update SignatureDocument : {}", signatureDocumentDTO);
        SignatureDocument signatureDocument = signatureDocumentMapper.toEntity(signatureDocumentDTO);
        signatureDocument = signatureDocumentRepository.save(signatureDocument);
        return signatureDocumentMapper.toDto(signatureDocument);
    }

    @Override
    public Optional<SignatureDocumentDTO> partialUpdate(SignatureDocumentDTO signatureDocumentDTO) {
        LOG.debug("Request to partially update SignatureDocument : {}", signatureDocumentDTO);

        return signatureDocumentRepository
            .findById(signatureDocumentDTO.getId())
            .map(existingSignatureDocument -> {
                signatureDocumentMapper.partialUpdate(existingSignatureDocument, signatureDocumentDTO);

                return existingSignatureDocument;
            })
            .map(signatureDocumentRepository::save)
            .map(signatureDocumentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<SignatureDocumentDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all SignatureDocuments");
        return signatureDocumentRepository.findAll(pageable).map(signatureDocumentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SignatureDocumentDTO> findOne(Long id) {
        LOG.debug("Request to get SignatureDocument : {}", id);
        return signatureDocumentRepository.findById(id).map(signatureDocumentMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete SignatureDocument : {}", id);
        signatureDocumentRepository.deleteById(id);
    }

    @Override
    @Transactional
    public SignatureResultDTO createSignatureTask(SignatureRequestDTO request) {
        LOG.debug("创建签名任务: {}", request);

        // 根据提供商类型获取对应的签名服务
        ElectronicSignatureService signatureService = electronicSignatureFactory.getSignatureService(request.getProvider());

        // 调用签名服务创建签名任务
        return signatureService.createSignatureTask(request);
    }

    @Override
    @Transactional(readOnly = true)
    public SignatureDocumentDTO querySignatureStatus(Long documentId) {
        LOG.debug("查询签名状态: {}", documentId);

        // 查询签名文档
        Optional<SignatureDocument> documentOpt = signatureDocumentRepository.findById(documentId);
        if (documentOpt.isEmpty()) {
            LOG.error("签名文档不存在: {}", documentId);
            return null;
        }

        SignatureDocument document = documentOpt.orElseThrow(() -> new EntityNotFoundException("签名文档不存在"));

        // 根据提供商类型获取对应的签名服务
        ElectronicSignatureService signatureService = electronicSignatureFactory.getSignatureService(document.getProvider());

        // 调用签名服务查询签名状态
        return signatureService.querySignatureStatus(documentId);
    }

    @Override
    @Transactional
    public Map<String, Object> handleCallback(String provider, SignatureCallbackDTO callbackDTO, Map<String, String> params) {
        LOG.debug("处理签名回调: {}, {}", provider, callbackDTO);

        // 根据提供商类型获取对应的签名服务
        SignatureProvider providerEnum;
        try {
            providerEnum = SignatureProvider.valueOf(provider.toUpperCase());
        } catch (IllegalArgumentException e) {
            LOG.error("不支持的签名服务提供商: {}", provider);
            return Map.of("result", "不支持的签名服务提供商: " + provider);
        }

        ElectronicSignatureService signatureService = electronicSignatureFactory.getSignatureService(providerEnum);

        // 调用签名服务处理回调
        return signatureService.handleCallback(callbackDTO, params);
    }

    @Override
    public String getSignedDocument(Long documentId) {
        LOG.debug("获取签名后的文档: {}", documentId);

        // 查询签名文档
        Optional<SignatureDocument> documentOpt = signatureDocumentRepository.findById(documentId);
        if (documentOpt.isEmpty()) {
            LOG.error("签名文档不存在: {}", documentId);
            return null;
        }

        SignatureDocument document = documentOpt.orElseThrow(() -> new EntityNotFoundException("签名文档不存在"));

        // 根据提供商类型获取对应的签名服务
        ElectronicSignatureService signatureService = electronicSignatureFactory.getSignatureService(document.getProvider());

        // 调用签名服务获取签名后的文档
        return signatureService.getSignedDocument(documentId);
    }

    @Override
    @Transactional
    public boolean cancelSignatureTask(Long documentId) {
        LOG.debug("取消签名任务: {}", documentId);

        // 查询签名文档
        Optional<SignatureDocument> documentOpt = signatureDocumentRepository.findById(documentId);
        if (documentOpt.isEmpty()) {
            LOG.error("签名文档不存在: {}", documentId);
            return false;
        }

        SignatureDocument document = documentOpt.orElseThrow(() -> new EntityNotFoundException("签名文档不存在"));

        // 根据提供商类型获取对应的签名服务
        ElectronicSignatureService signatureService = electronicSignatureFactory.getSignatureService(document.getProvider());

        // 调用签名服务取消签名任务
        return signatureService.cancelSignatureTask(documentId);
    }
}
