package com.whiskerguard.general.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.general.domain.UserWechatBinding;
import com.whiskerguard.general.repository.UserWechatBindingRepository;
import com.whiskerguard.general.service.UserWechatBindingService;
import com.whiskerguard.general.service.dto.UserWechatBindingDTO;
import com.whiskerguard.general.service.mapper.UserWechatBindingMapper;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户微信绑定服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/24
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UserWechatBindingServiceImpl implements UserWechatBindingService {

    private static final Logger log = LoggerFactory.getLogger(UserWechatBindingServiceImpl.class);

    private final UserWechatBindingRepository userWechatBindingRepository;

    private final UserWechatBindingMapper userWechatBindingMapper;

    private final HttpServletRequest httpServletRequest;

    @Autowired
    public UserWechatBindingServiceImpl(UserWechatBindingRepository userWechatBindingRepository,
                                        UserWechatBindingMapper userWechatBindingMapper,
                                        HttpServletRequest httpServletRequest) {
        this.userWechatBindingRepository = userWechatBindingRepository;
        this.userWechatBindingMapper = userWechatBindingMapper;
        this.httpServletRequest = httpServletRequest;
    }

    @Override
    public UserWechatBindingDTO save(UserWechatBindingDTO userWechatBinding) {
        log.debug("保存用户微信绑定: {}", userWechatBinding);
        //判断当前用户是否已经绑定微信
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_USER_ID));
        if (isUserBound(userId)) {
            throw new BadRequestAlertException("用户已绑定微信", "UserWechatBinding", "userBound");
        }
        //判断当前openId是否已经绑定用户
        if (isOpenIdBound(userWechatBinding.getOpenId())) {
            throw new BadRequestAlertException("微信已绑定用户", "UserWechatBinding", "openIdBound");
        }
        UserWechatBinding entity = userWechatBindingMapper.toEntity(userWechatBinding);
        String username = TenantContextUtil.getCurrentUserName();
        entity.setTenantId(TenantContextUtil.getCurrentTenantId());
        entity.setEmployeeId(userId);
        entity.setCreatedAt(Instant.now());
        entity.setCreatedBy(username);
        entity.setUpdatedAt(Instant.now());
        entity.setUpdatedBy(username);
        entity.setIsDeleted(Boolean.FALSE);
        userWechatBindingRepository.save(entity);
        return userWechatBindingMapper.toDto(entity);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserWechatBindingDTO> findAll(Pageable pageable) {
        log.debug("查找所有用户微信绑定: pageable={}", pageable);
        return userWechatBindingRepository.findAll(TenantContextUtil.getCurrentTenantId(), pageable).map(userWechatBindingMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        log.debug("删除用户微信绑定: id={}", id);
        userWechatBindingRepository.findById(id).ifPresent(userWechatBinding -> {
            userWechatBinding.setIsDeleted(Boolean.TRUE);
            userWechatBindingRepository.save(userWechatBinding);
        });
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserWechatBindingDTO> findByEmployeeId(Long employeeId) {
        log.debug("根据用户ID查找绑定记录: employeeId={}", employeeId);
        return userWechatBindingRepository.findByEmployeeIdAndIsDeletedFalse(employeeId).stream().map(userWechatBindingMapper::toDto).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserWechatBindingDTO> findByOpenId(String openId) {
        log.debug("根据OpenID查找绑定记录: openId={}", openId);
        return userWechatBindingRepository.findByOpenIdAndIsDeletedFalse(openId).map(userWechatBindingMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public UserWechatBindingDTO findByEmployeeIdAndOpenId(Long employeeId, String openId) {
        log.debug("根据用户ID和OpenID查找绑定记录: userId={}, openId={}", employeeId, openId);
        UserWechatBinding userWechatBinding = userWechatBindingRepository.findOneByOpenIdAndEmployeeId(employeeId, openId);
        return userWechatBindingMapper.toDto(userWechatBinding);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUserBound(Long employeeId) {
        log.debug("检查用户是否已绑定微信: userId={}", employeeId);
        return userWechatBindingRepository.existsByEmployeeIdAndIsDeletedFalse(employeeId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isOpenIdBound(String openId) {
        log.debug("检查OpenID是否已被绑定: openId={}", openId);
        return userWechatBindingRepository.existsByOpenIdAndIsDeletedFalse(openId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUserBoundToOpenId(Long userId, String openId) {
        log.debug("检查用户和OpenID的绑定是否存在: userId={}, openId={}", userId, openId);
        return userWechatBindingRepository.existsByEmployeeIdAndOpenIdAndIsDeletedFalse(userId, openId);
    }

    @Override
    public Map<String, Object> unbindUserWechat(String openId) {
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(httpServletRequest, RequestConstants.X_USER_ID));
        log.info("解绑用户微信: userId={}, openId={}", userId, openId);

        Map<String, Object> result = new HashMap<>();

        try {
            Optional<UserWechatBinding> bindingOpt = userWechatBindingRepository.findByEmployeeIdAndOpenIdAndIsDeletedFalse(userId, openId);
            UserWechatBinding userWechatBinding = bindingOpt.orElse(null);
            if (userWechatBinding == null) {
                result.put("success", false);
                result.put("message", "未找到绑定记录");
                result.put("errorCode", "BINDING_NOT_FOUND");
                return result;
            }

            // 执行解绑
            userWechatBinding.setIsDeleted(Boolean.TRUE);
            userWechatBinding = userWechatBindingRepository.save(userWechatBinding);

            result.put("success", true);
            result.put("message", "解绑成功");
            result.put("binding", userWechatBinding);
            log.info("用户微信解绑成功: userId={}, openId={}, bindingId={}", userId, openId, userWechatBinding.getId());
        } catch (Exception e) {
            log.error("解绑用户微信失败", e);
            result.put("success", false);
            result.put("message", "解绑失败: " + e.getMessage());
            result.put("errorCode", "UNBINDING_ERROR");
        }

        return result;
    }

    @Override
    public void unbindUserWechatByOpenId(String openId) {
        log.info("根据OpenID解绑用户微信: openId={}", openId);
        userWechatBindingRepository.updateIsDeletedByOpenId(openId);
    }

}
