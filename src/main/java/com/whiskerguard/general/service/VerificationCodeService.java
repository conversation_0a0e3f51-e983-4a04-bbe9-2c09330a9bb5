package com.whiskerguard.general.service;

import com.whiskerguard.general.domain.enumeration.VerificationCodeType;
import com.whiskerguard.general.model.VerificationCodeRequest;
import com.whiskerguard.general.model.VerificationCodeResponse;
import com.whiskerguard.general.model.VerificationCodeValidateRequest;

/**
 * 验证码服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
public interface VerificationCodeService {

    /**
     * 发送验证码
     *
     * @param request 验证码请求
     * @return 验证码响应
     */
    VerificationCodeResponse sendVerificationCode(VerificationCodeRequest request);

    /**
     * 验证验证码
     *
     * @param request 验证请求
     * @return 验证结果
     */
    VerificationCodeResponse validateVerificationCode(VerificationCodeValidateRequest request);

    /**
     * 检查是否可以发送验证码
     *
     * @param phoneNumber 手机号
     * @param codeType    验证码类型
     * @return 是否可以发送
     */
    boolean canSendVerificationCode(String phoneNumber, VerificationCodeType codeType);

}
