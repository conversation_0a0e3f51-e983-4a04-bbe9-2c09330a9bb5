package com.whiskerguard.general.service;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;

/**
 * 微信消息时间管理器
 * 用于管理用户最后交互时间，避免超时发送客服消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/24
 */
@Service
public class WechatMessageTimeManager {

    private static final Logger log = LoggerFactory.getLogger(WechatMessageTimeManager.class);

    private static final String USER_LAST_INTERACTION_KEY_PREFIX = "wechat:last_interaction:";
    // 微信客服消息时间限制：48小时
    private static final long CUSTOMER_SERVICE_TIME_LIMIT_HOURS = 48;

    private final RedissonClient redissonClient;

    @Autowired
    public WechatMessageTimeManager(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 记录用户最后交互时间
     *
     * @param openId 用户OpenID
     */
    public void recordUserInteraction(String openId) {
        String key = USER_LAST_INTERACTION_KEY_PREFIX + openId;
        RBucket<Instant> bucket = redissonClient.getBucket(key);

        Instant now = Instant.now();
        // 多存储1小时作为缓冲
        bucket.expire(Duration.ofHours(CUSTOMER_SERVICE_TIME_LIMIT_HOURS + 1));

        log.debug("记录用户交互时间: openId={}, time={}", openId, now);
    }

    /**
     * 检查是否可以发送客服消息
     *
     * @param openId 用户OpenID
     * @return true 如果可以发送客服消息，false 如果超过时间限制
     */
    public boolean canSendCustomerServiceMessage(String openId) {
        String key = USER_LAST_INTERACTION_KEY_PREFIX + openId;
        RBucket<Instant> bucket = redissonClient.getBucket(key);

        Instant lastInteraction = bucket.get();
        if (lastInteraction == null) {
            log.warn("用户无交互记录，不能发送客服消息: openId={}", openId);
            return false;
        }

        Instant now = Instant.now();
        long hoursSinceLastInteraction = ChronoUnit.HOURS.between(lastInteraction, now);

        boolean canSend = hoursSinceLastInteraction < CUSTOMER_SERVICE_TIME_LIMIT_HOURS;

        log.debug("检查客服消息发送权限: openId={}, 最后交互时间={}, 距今{}小时, 可发送={}",
            openId, lastInteraction, hoursSinceLastInteraction, canSend);

        return canSend;
    }

    /**
     * 获取用户最后交互时间
     *
     * @param openId 用户OpenID
     * @return 最后交互时间，如果没有记录则返回null
     */
    public Instant getLastInteractionTime(String openId) {
        String key = USER_LAST_INTERACTION_KEY_PREFIX + openId;
        RBucket<Instant> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    /**
     * 获取距离客服消息过期还有多少小时
     *
     * @param openId 用户OpenID
     * @return 剩余小时数，如果已过期返回0，如果没有交互记录返回-1
     */
    public long getRemainingHours(String openId) {
        Instant lastInteraction = getLastInteractionTime(openId);
        if (lastInteraction == null) {
            return -1;
        }

        Instant now = Instant.now();
        long hoursSinceLastInteraction = ChronoUnit.HOURS.between(lastInteraction, now);

        return Math.max(0, CUSTOMER_SERVICE_TIME_LIMIT_HOURS - hoursSinceLastInteraction);
    }

    /**
     * 清理用户交互记录
     *
     * @param openId 用户OpenID
     */
    public void clearUserInteraction(String openId) {
        String key = USER_LAST_INTERACTION_KEY_PREFIX + openId;
        RBucket<Instant> bucket = redissonClient.getBucket(key);
        bucket.delete();

        log.debug("清理用户交互记录: openId={}", openId);
    }

    /**
     * 批量检查用户是否可以发送客服消息
     *
     * @param openIds 用户OpenID列表
     * @return 可以发送客服消息的用户OpenID列表
     */
    public java.util.List<String> filterUsersCanReceiveCustomerService(java.util.List<String> openIds) {
        return openIds.stream()
            .filter(this::canSendCustomerServiceMessage)
            .toList();
    }

    /**
     * 获取客服消息时间限制（小时）
     *
     * @return 时间限制小时数
     */
    public long getCustomerServiceTimeLimit() {
        return CUSTOMER_SERVICE_TIME_LIMIT_HOURS;
    }
}
