package com.whiskerguard.general.service;

import com.whiskerguard.general.model.EmailRequest;
import com.whiskerguard.general.model.NotificationResponse;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 邮件服务接口
 */
public interface EmailService {

    /**
     * 发送邮件
     *
     * @param request 邮件请求
     * @return 通知响应
     */
    NotificationResponse send(EmailRequest request);

    /**
     * 发送邮件（别名方法）
     *
     * @param emailRequest 邮件请求
     * @return 发送结果
     */
    default NotificationResponse sendEmail(EmailRequest emailRequest) {
        return send(emailRequest);
    }

    /**
     * 异步发送邮件
     *
     * @param emailRequest 邮件请求
     * @return 异步发送结果
     */
    CompletableFuture<NotificationResponse> sendEmailAsync(EmailRequest emailRequest);

    /**
     * 批量发送邮件
     *
     * @param emailRequests 邮件请求列表
     * @return 发送结果列表
     */
    List<NotificationResponse> sendBatchEmails(List<EmailRequest> emailRequests);

    /**
     * 发送简单文本邮件
     *
     * @param to      收件人
     * @param subject 主题
     * @param content 内容
     * @return 发送结果
     */
    NotificationResponse sendSimpleEmail(String to, String subject, String content);

    /**
     * 发送简单HTML邮件
     *
     * @param to          收件人
     * @param subject     主题
     * @param htmlContent HTML内容
     * @return 发送结果
     */
    NotificationResponse sendHtmlEmail(String to, String subject, String htmlContent);

    /**
     * 发送带附件的邮件
     *
     * @param emailRequest 邮件请求（包含附件）
     * @return 发送结果
     */
    NotificationResponse sendEmailWithAttachments(EmailRequest emailRequest);

    /**
     * 验证邮箱地址格式
     *
     * @param email 邮箱地址
     * @return 是否有效
     */
    boolean isValidEmail(String email);

    /**
     * 验证邮件请求
     *
     * @param emailRequest 邮件请求
     * @return 验证结果
     */
    Map<String, String> validateEmailRequest(EmailRequest emailRequest);

}
