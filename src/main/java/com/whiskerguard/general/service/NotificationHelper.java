package com.whiskerguard.general.service;

import com.whiskerguard.general.domain.enumeration.*;
import com.whiskerguard.general.service.dto.*;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * 通知发送辅助工具类
 * 为其他微服务提供简单易用的通知发送接口
 *
 * <AUTHOR> Yan
 * @version 1.0
 * @date 2025-06-23
 */
@Component
public class NotificationHelper {

    private final NotificationCenterService notificationCenterService;

    public NotificationHelper(NotificationCenterService notificationCenterService) {
        this.notificationCenterService = notificationCenterService;
    }

    // ===================================================================
    // 系统通知便捷方法
    // ===================================================================

    /**
     * 发送系统公告
     *
     * @param title 标题
     * @param content 内容
     * @param scope 范围
     * @param targetIds 目标ID列表
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendSystemAnnouncement(String title, String content, NotificationScope scope, List<Long> targetIds) {
        SystemNotificationRequestDTO request = new SystemNotificationRequestDTO();
        request.setSubType(NotificationSubType.SYSTEM_ANNOUNCEMENT);
        request.setScope(scope);
        request.setTitle(title);
        request.setContent(content);
        request.setTargetIds(targetIds);
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.NORMAL);

        return notificationCenterService.sendSystemNotification(request);
    }

    /**
     * 发送租户到期提醒
     *
     * @param tenantId 租户ID
     * @param tenantName 租户名称
     * @param daysLeft 剩余天数
     * @param renewalUrl 续费链接
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendTenantExpirationNotice(Long tenantId, String tenantName, int daysLeft, String renewalUrl) {
        SystemNotificationRequestDTO request = new SystemNotificationRequestDTO();
        request.setSubType(NotificationSubType.TENANT_EXPIRED);
        request.setScope(NotificationScope.TENANT);
        request.setTitle("租户服务即将到期提醒");
        request.setContent(String.format("尊敬的用户，您的租户 %s 将在 %d 天后到期，请及时续费。", tenantName, daysLeft));
        request.setTargetIds(Arrays.asList(tenantId));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.SMS));
        request.setPriority(NotificationPriority.HIGH);

        return notificationCenterService.sendSystemNotification(request);
    }

    /**
     * 发送系统维护通知
     *
     * @param title 标题
     * @param content 内容
     * @param maintenanceTime 维护时间
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendMaintenanceNotice(String title, String content, Instant maintenanceTime) {
        SystemNotificationRequestDTO request = new SystemNotificationRequestDTO();
        request.setSubType(NotificationSubType.SYSTEM_MAINTENANCE);
        request.setScope(NotificationScope.GLOBAL);
        request.setTitle(title);
        request.setContent(content);
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.HIGH);
        request.setScheduledTime(maintenanceTime.minusSeconds(3600)); // 提前1小时通知

        return notificationCenterService.sendSystemNotification(request);
    }

    // ===================================================================
    // 任务通知便捷方法
    // ===================================================================

    /**
     * 发送审批待处理通知
     *
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param applicant 申请人
     * @param approvers 审批人列表
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendApprovalPendingNotification(String taskId, String taskName, String applicant, List<Long> approvers) {
        TaskNotificationRequestDTO request = new TaskNotificationRequestDTO();
        request.setSubType(NotificationSubType.APPROVAL_PENDING);
        request.setTitle("待审批任务通知");
        request.setContent(String.format("您有一个来自 %s 的审批任务：%s，请及时处理。", applicant, taskName));
        request.setRecipients(approvers);
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.HIGH);
        request.setBusinessId(taskId);
        request.setBusinessType("APPROVAL");

        return notificationCenterService.sendTaskNotification(request);
    }

    /**
     * 发送审批结果通知
     *
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param result 审批结果
     * @param approver 审批人
     * @param applicantId 申请人ID
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendApprovalResultNotification(
        String taskId,
        String taskName,
        String result,
        String approver,
        Long applicantId
    ) {
        NotificationSubType subType = "APPROVED".equals(result)
            ? NotificationSubType.APPROVAL_APPROVED
            : NotificationSubType.APPROVAL_REJECTED;

        TaskNotificationRequestDTO request = new TaskNotificationRequestDTO();
        request.setSubType(subType);
        request.setTitle("审批结果通知");
        request.setContent(String.format("您的审批任务 %s 已被 %s %s。", taskName, approver, "APPROVED".equals(result) ? "通过" : "拒绝"));
        request.setRecipients(Arrays.asList(applicantId));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.NORMAL);
        request.setBusinessId(taskId);
        request.setBusinessType("APPROVAL");

        return notificationCenterService.sendTaskNotification(request);
    }

    /**
     * 发送任务完成通知
     *
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param executor 执行人
     * @param result 执行结果
     * @param creatorId 任务创建者ID
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendTaskCompletionNotification(
        String taskId,
        String taskName,
        String executor,
        String result,
        Long creatorId
    ) {
        TaskNotificationRequestDTO request = new TaskNotificationRequestDTO();
        request.setSubType(NotificationSubType.TASK_COMPLETED);
        request.setTitle("任务完成通知");
        request.setContent(String.format("任务 %s 已由 %s 完成，执行结果：%s。", taskName, executor, result));
        request.setRecipients(Arrays.asList(creatorId));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.NORMAL);
        request.setBusinessId(taskId);
        request.setBusinessType("TASK");

        return notificationCenterService.sendTaskNotification(request);
    }

    // ===================================================================
    // 用户通知便捷方法
    // ===================================================================

    /**
     * 发送密码修改通知
     *
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendPasswordChangeNotification(Long userId, String ipAddress) {
        UserNotificationRequestDTO request = new UserNotificationRequestDTO();
        request.setUserId(userId);
        request.setSubType(NotificationSubType.PASSWORD_CHANGED);
        request.setTitle("密码修改通知");
        request.setContent(String.format("您的账户密码已成功修改，操作IP：%s。如非本人操作，请立即联系管理员。", ipAddress));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.SMS));
        request.setPriority(NotificationPriority.HIGH);

        return notificationCenterService.sendUserNotification(request);
    }

    /**
     * 发送登录异常通知
     *
     * @param userId 用户ID
     * @param ipAddress IP地址
     * @param location 登录地点
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendLoginAnomalyNotification(Long userId, String ipAddress, String location) {
        UserNotificationRequestDTO request = new UserNotificationRequestDTO();
        request.setUserId(userId);
        request.setSubType(NotificationSubType.USER_LOGIN);
        request.setTitle("异地登录提醒");
        request.setContent(String.format("检测到您的账户在 %s（IP：%s）登录，如非本人操作，请立即修改密码。", location, ipAddress));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.SMS));
        request.setPriority(NotificationPriority.HIGH);

        return notificationCenterService.sendUserNotification(request);
    }

    // ===================================================================
    // 业务通知便捷方法
    // ===================================================================

    /**
     * 发送培训提醒通知
     *
     * @param userIds 用户ID列表
     * @param trainingName 培训名称
     * @param startTime 开始时间
     * @return 批次ID
     */
    public String sendTrainingReminderNotification(List<Long> userIds, String trainingName, Instant startTime) {
        BatchNotificationRequestDTO request = new BatchNotificationRequestDTO();
        request.setCategory(NotificationCategory.BUSINESS);
        request.setSubType(NotificationSubType.TRAINING_REMINDER);
        request.setTitle("培训提醒");
        request.setContent(String.format("您有一个培训 %s 即将开始，请准时参加。", trainingName));
        request.setRecipientIds(userIds);
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.NORMAL);
        request.setScheduledTime(startTime.minusSeconds(1800)); // 提前30分钟提醒

        return notificationCenterService.sendBatchNotification(request);
    }

    /**
     * 发送合同到期提醒
     *
     * @param userId 用户ID
     * @param contractName 合同名称
     * @param expiryDate 到期日期
     * @param daysLeft 剩余天数
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendContractExpiryNotification(Long userId, String contractName, Instant expiryDate, int daysLeft) {
        UserNotificationRequestDTO request = new UserNotificationRequestDTO();
        request.setUserId(userId);
        request.setSubType(NotificationSubType.CONTRACT_EXPIRING);
        request.setTitle("合同到期提醒");
        request.setContent(String.format("合同 %s 将在 %d 天后到期，请及时处理续签事宜。", contractName, daysLeft));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.HIGH);

        return notificationCenterService.sendUserNotification(request);
    }

    /**
     * 发送违规举报通知
     *
     * @param reportId 举报ID
     * @param reporterName 举报人
     * @param violationType 违规类型
     * @param handlerIds 处理人ID列表
     * @return 通知记录DTO
     */
    public NotificationRecordDTO sendViolationReportNotification(
        String reportId,
        String reporterName,
        String violationType,
        List<Long> handlerIds
    ) {
        TaskNotificationRequestDTO request = new TaskNotificationRequestDTO();
        request.setSubType(NotificationSubType.VIOLATION_REPORTED);
        request.setTitle("违规举报通知");
        request.setContent(String.format("收到一起 %s 违规举报（举报人：%s），请及时处理。", violationType, reporterName));
        request.setRecipients(handlerIds);
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.HIGH);
        request.setBusinessId(reportId);
        request.setBusinessType("VIOLATION");

        return notificationCenterService.sendTaskNotification(request);
    }
}
