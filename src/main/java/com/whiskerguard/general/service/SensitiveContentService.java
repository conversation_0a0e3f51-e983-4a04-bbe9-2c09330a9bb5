package com.whiskerguard.general.service;

import com.whiskerguard.general.sensitive.config.SensitiveProperties;
import com.whiskerguard.general.sensitive.matcher.MatchResult;
import com.whiskerguard.general.sensitive.matcher.TrieMatcher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 敏感内容检查和掩码处理服务
 * Service for sensitive content checking and masking
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SensitiveContentService {

    private final TrieMatcher matcher;
    private final SensitiveProperties properties;

    /**
     * 检查文本内容中的敏感词
     * Check text content for sensitive words
     *
     * @param tenantId 租户ID（0表示平台级）
     * @param content 待检查内容
     * @return 包含敏感内容信息的匹配结果
     */
    public MatchResult check(Long tenantId, String content) {
        log.debug("检查租户{}的内容中的敏感词 | Checking content for sensitive words for tenant {}", tenantId, tenantId);
        return matcher.check(tenantId, content);
    }

    /**
     * 使用默认掩码字符替换文本内容中的敏感词
     * Mask sensitive words in text content with default mask character
     *
     * @param tenantId 租户ID（0表示平台级）
     * @param content 待掩码内容
     * @return 掩码处理后的文本
     */
    public String mask(Long tenantId, String content) {
        return mask(tenantId, content, properties.getDefaultMaskChar());
    }

    /**
     * 使用指定掩码字符替换文本内容中的敏感词
     * Mask sensitive words in text content with specified mask character
     *
     * @param tenantId 租户ID（0表示平台级）
     * @param content 待掩码内容
     * @param maskChar 用于掩码的字符
     * @return 掩码处理后的文本
     */
    public String mask(Long tenantId, String content, char maskChar) {
        log.debug("掩码处理租户{}的内容中的敏感词 | Masking sensitive words in content for tenant {}", tenantId, tenantId);
        return matcher.mask(tenantId, content, maskChar);
    }
}
