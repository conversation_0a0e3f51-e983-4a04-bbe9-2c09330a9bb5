package com.whiskerguard.general.service.exception;

/**
 * 实体未找到异常
 * 当请求的实体在数据库中不存在时抛出此异常
 */
public class EntityNotFoundException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用指定的错误消息构造一个新的EntityNotFoundException
     *
     * @param message 详细消息
     */
    public EntityNotFoundException(String message) {
        super(message);
    }

    /**
     * 使用指定的错误消息和原因构造一个新的EntityNotFoundException
     *
     * @param message 详细消息
     * @param cause 原因
     */
    public EntityNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
