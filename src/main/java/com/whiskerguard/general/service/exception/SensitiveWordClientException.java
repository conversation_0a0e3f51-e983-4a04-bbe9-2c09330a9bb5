package com.whiskerguard.general.service.exception;

/**
 * 敏感词客户端异常，处理敏感词过滤过程中出现的错误。
 * Exception for handling errors during sensitive word filtering.
 */
public class SensitiveWordClientException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用错误信息构造异常
     * Constructs an exception with an error message
     *
     * @param message 错误信息
     *                Error message
     */
    public SensitiveWordClientException(String message) {
        super(message);
    }

    /**
     * 使用错误信息和原始异常构造异常
     * Constructs an exception with an error message and cause
     *
     * @param message 错误信息
     *                Error message
     * @param cause 原始异常
     *              Original cause
     */
    public SensitiveWordClientException(String message, Throwable cause) {
        super(message, cause);
    }
}
