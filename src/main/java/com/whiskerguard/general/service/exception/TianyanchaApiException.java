package com.whiskerguard.general.service.exception;

/**
 * Custom exception for Tianyancha API related errors.
 *
 * 天眼查API相关错误的自定义异常类。
 */
public class TianyanchaApiException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private Integer errorCode;
    private String apiEndpoint;

    /**
     * Constructor with message only.
     *
     * 仅包含错误信息的构造函数。
     */
    public TianyanchaApiException(String message) {
        super(message);
        this.errorCode = null;
        this.apiEndpoint = null;
    }

    /**
     * Constructor with message and cause.
     *
     * 包含错误信息和原因的构造函数。
     */
    public TianyanchaApiException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = null;
        this.apiEndpoint = null;
    }

    /**
     * Constructor with message, error code and API endpoint.
     *
     * 包含错误信息、错误代码和API端点的构造函数。
     */
    public TianyanchaApiException(String message, Integer errorCode, String apiEndpoint) {
        super(message);
        this.errorCode = errorCode;
        this.apiEndpoint = apiEndpoint;
    }

    /**
     * Constructor with all parameters.
     *
     * 包含所有参数的构造函数。
     */
    public TianyanchaApiException(String message, Integer errorCode, String apiEndpoint, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.apiEndpoint = apiEndpoint;
    }

    /**
     * Get the error code from API response.
     *
     * 获取API响应的错误代码。
     */
    public Integer getErrorCode() {
        return errorCode;
    }

    /**
     * Get the API endpoint that caused the error.
     *
     * 获取导致错误的API端点。
     */
    public String getApiEndpoint() {
        return apiEndpoint;
    }

    /**
     * Get the API endpoint that caused the error.
     * Alias for getApiEndpoint() to match test expectations.
     *
     * 获取导致错误的API端点。
     * getApiEndpoint()的别名，以匹配测试期望。
     */
    public String getEndpoint() {
        return apiEndpoint;
    }

    /**
     * Set the error code.
     * This method is provided for test purposes.
     *
     * 设置错误代码。
     * 此方法用于测试目的。
     */
    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * Set the API endpoint.
     * This method is provided for test purposes.
     *
     * 设置API端点。
     * 此方法用于测试目的。
     */
    public void setEndpoint(String endpoint) {
        this.apiEndpoint = endpoint;
    }
}
