package com.whiskerguard.general.service;

import com.whiskerguard.general.service.dto.UserNotificationPreferenceDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.general.domain.UserNotificationPreference}.
 */
public interface UserNotificationPreferenceService {
    /**
     * Save a userNotificationPreference.
     *
     * @param userNotificationPreferenceDTO the entity to save.
     * @return the persisted entity.
     */
    UserNotificationPreferenceDTO save(UserNotificationPreferenceDTO userNotificationPreferenceDTO);

    /**
     * Updates a userNotificationPreference.
     *
     * @param userNotificationPreferenceDTO the entity to update.
     * @return the persisted entity.
     */
    UserNotificationPreferenceDTO update(UserNotificationPreferenceDTO userNotificationPreferenceDTO);

    /**
     * Partially updates a userNotificationPreference.
     *
     * @param userNotificationPreferenceDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<UserNotificationPreferenceDTO> partialUpdate(UserNotificationPreferenceDTO userNotificationPreferenceDTO);

    /**
     * Get all the userNotificationPreferences.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<UserNotificationPreferenceDTO> findAll(Pageable pageable);

    /**
     * Get the "id" userNotificationPreference.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<UserNotificationPreferenceDTO> findOne(Long id);

    /**
     * Delete the "id" userNotificationPreference.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
