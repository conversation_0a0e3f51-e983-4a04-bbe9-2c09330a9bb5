package com.whiskerguard.general.service;

import com.whiskerguard.general.domain.Company;
import com.whiskerguard.general.repository.CompanyRepository;
import com.whiskerguard.general.service.dto.CompanyDTO;
import com.whiskerguard.general.service.mapper.CompanyMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link Company}.
 * <p>
 * 用于管理{@link Company}的服务实现。
 */
@Service
@Transactional
public class CompanyService {

    private static final Logger log = LoggerFactory.getLogger(CompanyService.class);

    private final CompanyRepository companyRepository;
    private final CompanyMapper companyMapper;

    public CompanyService(CompanyRepository companyRepository, CompanyMapper companyMapper) {
        this.companyRepository = companyRepository;
        this.companyMapper = companyMapper;
    }

    /**
     * Save a company.
     *
     * @param companyDTO 要保存的实体。
     *                   <p>
     *                   保存一个企业。
     * @return 持久化的实体。
     */
    public CompanyDTO save(CompanyDTO companyDTO) {
        log.debug("Request to save Company : {}", companyDTO);
        Company company = companyMapper.toEntity(companyDTO);
        company = companyRepository.save(company);
        return companyMapper.toDto(company);
    }

    /**
     * Save a company entity directly.
     *
     * @param company 要保存的实体。
     *                <p>
     *                直接保存企业实体。
     * @return 持久化的实体。
     */
    public Company save(Company company) {
        log.debug("Request to save Company entity : {}", company);
        return companyRepository.save(company);
    }

    /**
     * Update a company.
     *
     * @param companyDTO 要保存的实体。
     *                   <p>
     *                   更新一个企业。
     * @return 持久化的实体。
     */
    public CompanyDTO update(CompanyDTO companyDTO) {
        log.debug("Request to update Company : {}", companyDTO);
        Company company = companyMapper.toEntity(companyDTO);
        company = companyRepository.save(company);
        return companyMapper.toDto(company);
    }

    /**
     * Partially update a company.
     *
     * @param companyDTO 要部分更新的实体。
     *                   <p>
     *                   部分更新一个企业。
     * @return 持久化的实体。
     */
    public Optional<CompanyDTO> partialUpdate(CompanyDTO companyDTO) {
        log.debug("Request to partially update Company : {}", companyDTO);

        return companyRepository
            .findById(companyDTO.getId())
            .map(existingCompany -> {
                companyMapper.partialUpdate(existingCompany, companyDTO);
                return existingCompany;
            })
            .map(companyRepository::save)
            .map(companyMapper::toDto);
    }

    /**
     * Get all the companies.
     *
     * @param pageable 分页信息。
     *                 <p>
     *                 获取所有企业。
     * @return 实体列表。
     */
    @Transactional(readOnly = true)
    public Page<CompanyDTO> findAll(Pageable pageable) {
        log.debug("Request to get all Companies");
        return companyRepository.findAll(pageable).map(companyMapper::toDto);
    }

    /**
     * Get one company by id.
     *
     * @param id 实体的id。
     *           <p>
     *           根据id获取一个企业。
     * @return 实体。
     */
    @Transactional(readOnly = true)
    public Optional<CompanyDTO> findOne(Long id) {
        log.debug("Request to get Company : {}", id);
        return companyRepository.findById(id).map(companyMapper::toDto);
    }

    /**
     * Get one company entity by id.
     *
     * @param id 实体的id。
     *           <p>
     *           根据id获取一个企业实体。
     * @return 实体。
     */
    @Transactional(readOnly = true)
    public Optional<Company> findOneEntity(Long id) {
        log.debug("Request to get Company entity : {}", id);
        return companyRepository.findById(id);
    }

    /**
     * Delete the company by id.
     *
     * <p>
     * 根据id删除企业。
     *
     * @param id 实体的id。
     */
    public void delete(Long id) {
        log.debug("Request to delete Company : {}", id);
        companyRepository.deleteById(id);
    }

    /**
     * Find company by unified social credit code.
     *
     * @param creditCode 统一社会信用代码。
     *                   <p>
     *                   根据统一社会信用代码查找企业。
     * @return 如果找到，返回企业。
     */
    @Transactional(readOnly = true)
    public Optional<Company> findByUnifiedSocialCreditCode(String creditCode) {
        log.debug("Request to find Company by unified social credit code : {}", creditCode);
        return companyRepository.findByUnifiedSocialCreditCode(creditCode);
    }

    /**
     * Find company by name.
     *
     * @param name 企业名称。
     *             <p>
     *             根据名称查找企业。
     * @return 如果找到，返回企业。
     */
    @Transactional(readOnly = true)
    public Optional<Company> findByName(String name) {
        log.debug("Request to find Company by name : {}", name);
        return companyRepository.findByName(name);
    }

    /**
     * Find company by registration number.
     *
     * @param regNumber 注册号。
     *                  <p>
     *                  根据注册号查找企业。
     * @return 如果找到，返回企业。
     */
    @Transactional(readOnly = true)
    public Optional<Company> findByRegNumber(String regNumber) {
        log.debug("Request to find Company by registration number : {}", regNumber);
        return companyRepository.findByRegNumber(regNumber);
    }

    /**
     * Find company by tax number.
     *
     * @param taxNumber 税号。
     *                  <p>
     *                  根据税号查找企业。
     * @return 如果找到，返回企业。
     */
    @Transactional(readOnly = true)
    public Optional<Company> findByTaxNumber(String taxNumber) {
        log.debug("Request to find Company by tax number : {}", taxNumber);
        return companyRepository.findByTaxNumber(taxNumber);
    }

    /**
     * Find company by Tianyancha ID.
     *
     * @param tianyanchaId 天眼查ID。
     *                     <p>
     *                     根据天眼查ID查找企业。
     * @return 如果找到，返回企业。
     */
    @Transactional(readOnly = true)
    public Optional<Company> findByTianyanchaId(Long tianyanchaId) {
        log.debug("Request to find Company by Tianyancha ID : {}", tianyanchaId);
        return companyRepository.findByTianyanchaId(tianyanchaId);
    }

    /**
     * Search companies by name containing (case-insensitive).
     *
     * @param name 要搜索的名称。
     *             <p>
     *             通过包含名称搜索企业（不区分大小写）。
     * @return 企业列表。
     */
    @Transactional(readOnly = true)
    public List<CompanyDTO> searchByName(String name) {
        log.debug("Request to search Companies by name containing : {}", name);
        return companyRepository.findByNameContainingIgnoreCase(name).stream().map(companyMapper::toDto).collect(Collectors.toList());
    }

    /**
     * Find company by any identifier (name, credit code, reg number, tax number).
     * This method tries different search strategies to find a company.
     *
     * @param keyword 搜索关键词。
     *                <p>
     *                通过任意标识符查找企业（名称、统一社会信用代码、注册号、税号）。
     *                该方法尝试不同的搜索策略来查找企业。
     * @return 如果找到，返回企业。
     */
    @Transactional(readOnly = true)
    public Optional<Company> findByAnyIdentifier(String keyword) {
        log.debug("Request to find Company by any identifier : {}", keyword);

        if (keyword == null || keyword.trim().isEmpty()) {
            return Optional.empty();
        }

        String trimmedKeyword = keyword.trim();

        // Try unified social credit code first
        // 首先尝试统一社会信用代码
        Optional<Company> company = findByUnifiedSocialCreditCode(trimmedKeyword);
        if (company.isPresent()) {
            return company;
        }

        // Try registration number
        // 尝试注册号
        company = findByRegNumber(trimmedKeyword);
        if (company.isPresent()) {
            return company;
        }

        // Try tax number
        // 尝试税号
        company = findByTaxNumber(trimmedKeyword);
        if (company.isPresent()) {
            return company;
        }

        // Try exact name match
        // 尝试精确名称匹配
        company = findByName(trimmedKeyword);
        if (company.isPresent()) {
            return company;
        }

        // Try partial name match (take first result if any)
        // 尝试部分名称匹配（如果有结果，取第一个）
        List<CompanyDTO> companies = searchByName(trimmedKeyword);
        if (!companies.isEmpty()) {
            return findOneEntity(companies.get(0).getId());
        }

        return Optional.empty();
    }

    /**
     * Check if company data is stale and needs refresh.
     *
     * @param company              要检查的企业。
     * @param cacheExpirationHours 缓存过期时间（小时）。
     *                             <p>
     *                             检查企业数据是否过期并需要刷新。
     * @return 如果数据过期，返回true。
     */
    public boolean isDataStale(Company company, int cacheExpirationHours) {
        if (company.getCacheTime() == null) {
            return true;
        }

        Instant expirationTime = company.getCacheTime().plusSeconds(cacheExpirationHours * 3600L);
        return Instant.now().isAfter(expirationTime);
    }

    /**
     * Update cache time for a company.
     *
     * <p>
     * 更新企业的缓存时间。
     *
     * @param company 要更新的企业。
     */
    public void updateCacheTime(Company company) {
        company.setCacheTime(Instant.now());
        companyRepository.save(company);
    }
}
