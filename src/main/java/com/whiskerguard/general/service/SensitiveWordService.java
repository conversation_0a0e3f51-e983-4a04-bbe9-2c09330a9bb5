package com.whiskerguard.general.service;

import com.esotericsoftware.minlog.Log;
import com.whiskerguard.general.domain.SensitiveWord;
import com.whiskerguard.general.repository.SensitiveWordRepository;
import com.whiskerguard.general.service.dto.SensitiveWordDTO;
import com.whiskerguard.general.service.mapper.SensitiveWordMapper;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.whiskerguard.general.domain.SensitiveWord}.
 */
@Service
@Transactional
public class SensitiveWordService {

    private static final Logger LOG = LoggerFactory.getLogger(SensitiveWordService.class);

    private final SensitiveWordRepository sensitiveWordRepository;

    private final SensitiveWordMapper sensitiveWordMapper;

    public SensitiveWordService(SensitiveWordRepository sensitiveWordRepository, SensitiveWordMapper sensitiveWordMapper) {
        this.sensitiveWordRepository = sensitiveWordRepository;
        this.sensitiveWordMapper = sensitiveWordMapper;
    }

    /**
     * Save a sensitiveWord.
     *
     * @param sensitiveWordDTO the entity to save.
     * @return the persisted entity.
     */
    public SensitiveWordDTO save(SensitiveWordDTO sensitiveWordDTO) {
        LOG.debug("Request to save SensitiveWord : {}", sensitiveWordDTO);
        SensitiveWord sensitiveWord = sensitiveWordMapper.toEntity(sensitiveWordDTO);
        sensitiveWord = sensitiveWordRepository.save(sensitiveWord);
        return sensitiveWordMapper.toDto(sensitiveWord);
    }

    /**
     * Update a sensitiveWord.
     *
     * @param sensitiveWordDTO the entity to save.
     * @return the persisted entity.
     */
    public SensitiveWordDTO update(SensitiveWordDTO sensitiveWordDTO) {
        LOG.debug("Request to update SensitiveWord : {}", sensitiveWordDTO);
        SensitiveWord sensitiveWord = sensitiveWordMapper.toEntity(sensitiveWordDTO);
        sensitiveWord = sensitiveWordRepository.save(sensitiveWord);
        return sensitiveWordMapper.toDto(sensitiveWord);
    }

    /**
     * Partially update a sensitiveWord.
     *
     * @param sensitiveWordDTO the entity to update partially.
     * @return the persisted entity.
     */
    public Optional<SensitiveWordDTO> partialUpdate(SensitiveWordDTO sensitiveWordDTO) {
        LOG.debug("Request to partially update SensitiveWord : {}", sensitiveWordDTO);

        return sensitiveWordRepository
            .findById(sensitiveWordDTO.getId())
            .map(existingSensitiveWord -> {
                sensitiveWordMapper.partialUpdate(existingSensitiveWord, sensitiveWordDTO);

                return existingSensitiveWord;
            })
            .map(sensitiveWordRepository::save)
            .map(sensitiveWordMapper::toDto);
    }

    /**
     * Get all the sensitiveWords.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    @Transactional(readOnly = true)
    public Page<SensitiveWordDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all SensitiveWords");
        return sensitiveWordRepository.findAll(pageable).map(sensitiveWordMapper::toDto);
    }

    /**
     * Get one sensitiveWord by id.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    @Transactional(readOnly = true)
    public Optional<SensitiveWordDTO> findOne(Long id) {
        LOG.debug("Request to get SensitiveWord : {}", id);
        return sensitiveWordRepository.findById(id).map(sensitiveWordMapper::toDto);
    }

    /**
     * Delete the sensitiveWord by id.
     *
     * @param id the id of the entity.
     */
    public void delete(Long id) {
        LOG.debug("Request to delete SensitiveWord : {}", id);
        sensitiveWordRepository.deleteById(id);
    }

    /**
     * 过滤文本中的敏感词，将敏感词替换为 *
     * Filter sensitive words in text, replacing them with *
     *
     * @param content 需要过滤的内容
     *                The content to be filtered
     * @return 过滤后的内容，敏感词被替换为 *
     *         Filtered content with sensitive words replaced by *
     */
    @Transactional(readOnly = true)
    public String filterSensitiveWords(String content) {
        LOG.debug("Request to filter sensitive words in content");
        if (content == null || content.isEmpty()) {
            return content;
        }

        String filteredContent = content;
        // 从数据库获取所有有效的敏感词
        // Get all active sensitive words from database
        List<SensitiveWord> sensitiveWords = sensitiveWordRepository.findActiveSensitiveWords();

        for (SensitiveWord word : sensitiveWords) {
            String term = word.getTerm();
            if (term != null && !term.isEmpty()) {
                String replacement = "*".repeat(term.length());
                filteredContent = filteredContent.replace(term, replacement);
            }
        }

        return filteredContent;
    }

    /**
     * 查找文本中包含的敏感词
     * Find sensitive words contained in the text
     *
     * @param content 需要检查的内容
     *                The content to check
     * @return 发现的敏感词列表
     *         List of found sensitive words
     */
    @Transactional(readOnly = true)
    public List<String> findSensitiveWords(String content) {
        LOG.debug("Request to find sensitive words in content");
        if (content == null || content.isEmpty()) {
            return Collections.emptyList();
        }

        // 从数据库获取所有有效的敏感词
        // Get all active sensitive words from database
        List<SensitiveWord> sensitiveWords = sensitiveWordRepository.findActiveSensitiveWords();

        return sensitiveWords
            .stream()
            .map(SensitiveWord::getTerm)
            .filter(term -> term != null && !term.isEmpty() && content.contains(term))
            .collect(Collectors.toList());
    }
}
