package com.whiskerguard.general.service;

import com.whiskerguard.general.domain.enumeration.SignatureProvider;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * 电子签名服务工厂类
 * <p>
 * 根据签名服务提供商类型，返回对应的电子签名服务实现。
 * </p>
 *
 * <AUTHOR>
 */
@Component
public class ElectronicSignatureFactory {

    private final Map<String, ElectronicSignatureService> signatureServices;

    public ElectronicSignatureFactory(Map<String, ElectronicSignatureService> signatureServices) {
        this.signatureServices = signatureServices;
    }

    /**
     * 获取电子签名服务
     *
     * @param provider 签名服务提供商
     * @return 电子签名服务
     */
    public ElectronicSignatureService getSignatureService(SignatureProvider provider) {
        switch (provider) {
            case FADADA:
                return signatureServices.get("fadadaSignatureService");
            case ESIGN:
                return signatureServices.get("esignSignatureService");
            default:
                throw new IllegalArgumentException("不支持的签名服务提供商: " + provider);
        }
    }
}
