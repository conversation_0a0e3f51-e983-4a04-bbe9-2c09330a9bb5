package com.whiskerguard.general.service;

import java.util.Map;

/**
 * 微信公众号Access Token管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
public interface WechatAccessTokenService {

    /**
     * 获取当前有效的Access Token
     *
     * @return Access Token
     */
    String getAccessToken();

    /**
     * 强制刷新Access Token
     *
     * @return 新的Access Token
     */
    String refreshAccessToken();

    /**
     * 检查Access Token是否有效
     *
     * @param accessToken 要检查的Access Token
     * @return 是否有效
     */
    boolean isAccessTokenValid(String accessToken);

    /**
     * 获取Access Token详细信息
     *
     * @return Access Token信息，包括token、过期时间等
     */
    Map<String, Object> getAccessTokenInfo();

    /**
     * 清理Access Token缓存
     */
    void clearAccessTokenCache();

    /**
     * 获取Access Token剩余有效时间（秒）
     *
     * @return 剩余有效时间，-1表示已过期或无效
     */
    long getAccessTokenRemainingTime();

}
