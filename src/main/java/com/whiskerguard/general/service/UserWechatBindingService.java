package com.whiskerguard.general.service;

import com.whiskerguard.general.service.dto.UserWechatBindingDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户微信绑定服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/24
 */
public interface UserWechatBindingService {

    /**
     * 方法名称：save
     * 描述：保存用户微信绑定关系。
     *
     * @param userWechatBindingDTO 用户微信绑定关系DTO
     * @return 用户微信绑定关系DTO
     * @since 1.0
     */
    UserWechatBindingDTO save(UserWechatBindingDTO userWechatBindingDTO);

    /**
     * 方法名称：findAll
     * 描述：获取所有绑定记录（分页）。
     *
     * @param pageable 分页信息
     * @return 绑定记录分页数据
     * @since 1.0
     */
    Page<UserWechatBindingDTO> findAll(Pageable pageable);

    /**
     * 方法名称：delete
     * 描述：根据ID删除绑定记录。
     *
     * @param id 绑定记录ID
     * @since 1.0
     */
    void delete(Long id);

    /**
     * 方法名称：findByEmployeeId
     * 描述：根据用户ID查找绑定记录。
     *
     * @param employeeId 用户ID
     * @return 绑定记录列表
     * @since 1.0
     */
    List<UserWechatBindingDTO> findByEmployeeId(Long employeeId);

    /**
     * 方法名称：findByOpenId
     * 描述：根据OpenID查找绑定记录。
     *
     * @param openId 微信OpenID
     * @return 绑定记录
     * @since 1.0
     */
    Optional<UserWechatBindingDTO> findByOpenId(String openId);

    /**
     * 方法名称：findByEmployeeIdAndOpenId
     * 描述：根据用户ID和OpenID查找绑定记录。
     *
     * @param employeeId 用户ID
     * @param openId     微信OpenID
     * @return 绑定记录
     * @since 1.0
     */
    UserWechatBindingDTO findByEmployeeIdAndOpenId(Long employeeId, String openId);

    /**
     * 方法名称：isUserBound
     * 描述：检查用户是否已绑定微信。
     *
     * @param employeeId 用户ID
     * @return 是否已绑定
     * @since 1.0
     */
    boolean isUserBound(Long employeeId);

    /**
     * 方法名称：isOpenIdBound
     * 描述：检查OpenID是否已绑定用户。
     *
     * @param openId 微信OpenID
     * @return 是否已绑定
     * @since 1.0
     */
    boolean isOpenIdBound(String openId);

    /**
     * 方法名称：isUserBoundToOpenId
     * 描述：检查用户是否已绑定到指定的OpenID。
     *
     * @param employeeId 用户ID
     * @param openId     微信OpenID
     * @return 是否已绑定
     * @since 1.0
     */
    boolean isUserBoundToOpenId(Long employeeId, String openId);

    /**
     * 方法名称：unbindUserWechat
     * 描述：解绑用户微信。
     *
     * @param openId 微信OpenID
     * @return 解绑结果
     * @since 1.0
     */
    Map<String, Object> unbindUserWechat(String openId);

    /**
     * 方法名称：unbindUserWechatByOpenId
     * 描述：根据OpenID解绑用户微信。
     *
     * @param openId 微信OpenID
     * @since 1.0
     */
    void unbindUserWechatByOpenId(String openId);
}
