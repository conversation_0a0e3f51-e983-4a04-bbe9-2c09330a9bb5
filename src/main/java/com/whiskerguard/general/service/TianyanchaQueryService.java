package com.whiskerguard.general.service;

import com.whiskerguard.general.client.TianyanchaApiClient;
import com.whiskerguard.general.config.TianyanchaProperties;
import com.whiskerguard.general.domain.Company;
import com.whiskerguard.general.service.dto.CompanyDTO;
import com.whiskerguard.general.service.dto.CompanyVerificationResultDTO;
import com.whiskerguard.general.service.dto.TianyanchaCompanyBasicInfoResponseDTO;
import com.whiskerguard.general.service.dto.TianyanchaCompanyVerificationResponseDTO;
import com.whiskerguard.general.service.exception.TianyanchaApiException;
import com.whiskerguard.general.service.mapper.CompanyMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service for querying Tianyancha data with local caching.
 * This service orchestrates between Tianyancha API and local database.
 * <p>
 * 用于查询天眼查数据并进行本地缓存的服务。
 * 该服务协调天眼查API和本地数据库之间的交互。
 */
@Service
@Transactional
public class TianyanchaQueryService {

    private static final Logger log = LoggerFactory.getLogger(TianyanchaQueryService.class);

    private final TianyanchaApiClient tianyanchaApiClient;
    private final CompanyService companyService;
    private final CompanyMapper companyMapper;
    private final TianyanchaProperties tianyanchaProperties;

    public TianyanchaQueryService(
        TianyanchaApiClient tianyanchaApiClient,
        CompanyService companyService,
        CompanyMapper companyMapper,
        TianyanchaProperties tianyanchaProperties
    ) {
        this.tianyanchaApiClient = tianyanchaApiClient;
        this.companyService = companyService;
        this.companyMapper = companyMapper;
        this.tianyanchaProperties = tianyanchaProperties;
    }

    /**
     * Get company basic information with caching.
     * First checks local cache, then fetches from Tianyancha API if needed.
     *
     * @param keyword 搜索关键词（企业名称、ID、注册号或统一社会信用代码）
     * @return 企业信息
     */
    public CompanyDTO getCompanyBasicInfo(String keyword) {
        log.debug("Request to get company basic info for keyword: {}", keyword);

        // First, try to find in local cache
        // 首先尝试从本地缓存中查找
        Optional<Company> cachedCompany = companyService.findByAnyIdentifier(keyword);

        if (cachedCompany.isPresent()) {
            Company company = cachedCompany.orElse(null);

            // Check if cached data is still fresh
            // 检查缓存数据是否仍然新鲜
            if (!companyService.isDataStale(company, tianyanchaProperties.getCacheExpirationHours())) {
                log.debug("Returning cached company data for keyword: {}", keyword);
                return companyMapper.toDto(company);
            } else {
                log.debug("Cached data is stale for keyword: {}, fetching from API", keyword);
            }
        }

        // Fetch from Tianyancha API
        // 从天眼查API获取
        TianyanchaCompanyBasicInfoResponseDTO apiResponse = tianyanchaApiClient.getCompanyBasicInfo(keyword);

        if (apiResponse.getResult() == null) {
            throw new TianyanchaApiException("No company data found for keyword: " + keyword);
        }

        // Convert API response to entity
        // 将API响应转换为实体
        Company company = companyMapper.fromTianyanchaResponse(apiResponse);

        // Check if we already have this company in database (by Tianyancha ID or credit code)
        // 检查数据库中是否已有该企业（通过天眼查ID或统一社会信用代码）
        Optional<Company> existingCompany = Optional.empty();

        if (company.getTianyanchaId() != null) {
            existingCompany = companyService.findByTianyanchaId(company.getTianyanchaId());
        }

        if (existingCompany.isEmpty() && company.getUnifiedSocialCreditCode() != null) {
            existingCompany = companyService.findByUnifiedSocialCreditCode(company.getUnifiedSocialCreditCode());
        }

        if (existingCompany.isPresent()) {
            // Update existing company
            // 更新已存在的企业
            Company existing = existingCompany.orElse(null);
            updateCompanyFromApiResponse(existing, company);
            company = companyService.save(existing);
            log.debug("Updated existing company in cache: {}", company.getName());
        } else {
            // Save new company
            // 保存新企业
            company = companyService.save(company);
            log.debug("Saved new company to cache: {}", company.getName());
        }

        return companyMapper.toDto(company);
    }

    /**
     * Verify company three elements (name, credit code, legal person name).
     *
     * @param companyName     企业全称
     * @param creditCode      统一社会信用代码
     * @param legalPersonName 法人姓名
     * @return 验证结果
     */
    public CompanyVerificationResultDTO verifyCompanyThreeElements(String companyName, String creditCode, String legalPersonName) {
        log.debug("Request to verify company three elements: {}, {}, {}", companyName, creditCode, legalPersonName);

        TianyanchaCompanyVerificationResponseDTO apiResponse = tianyanchaApiClient.verifyCompanyThreeElements(
            companyName,
            creditCode,
            legalPersonName
        );

        if (apiResponse.getResult() == null) {
            throw new TianyanchaApiException("No verification result returned from API");
        }

        CompanyVerificationResultDTO result = new CompanyVerificationResultDTO();
        result.setVerifyResult(apiResponse.getResult().getVerifyResult());
        // Use the input parameters since they represent what was actually verified
        result.setCompanyName(companyName);
        result.setCreditCode(creditCode);
        result.setLegalPersonName(legalPersonName);

        if (result.isMatch()) {
            result.setMessage("企业三要素验证通过");
        } else {
            result.setMessage("企业三要素验证失败");
        }

        return result;
    }

    /**
     * Get company risk information.
     *
     * @param keyword 搜索关键词
     * @return 原始JSON响应
     */
    public String getCompanyRisk(String keyword) {
        log.debug("Request to get company risk for keyword: {}", keyword);
        return tianyanchaApiClient.getCompanyInfo("risk/company", keyword);
    }

    /**
     * Get company risk information.
     *
     * @param keyword 搜索关键词
     * @return 原始JSON响应
     */
    public String getCompanyRiskInfo(String keyword) {
        log.debug("Request to get company risk info for keyword: {}", keyword);
        return tianyanchaApiClient.getCompanyInfo("risk/company", keyword);
    }

    /**
     * Get company contact information.
     *
     * @param keyword 搜索关键词
     * @return 原始JSON响应
     */
    public String getCompanyContactInfo(String keyword) {
        log.debug("Request to get company contact info for keyword: {}", keyword);
        return tianyanchaApiClient.getCompanyInfo("baseinfo/contact", keyword);
    }

    /**
     * Get company change records.
     *
     * @param keyword 搜索关键词
     * @return 原始JSON响应
     */
    public String getCompanyChangeRecords(String keyword) {
        log.debug("Request to get company change records for keyword: {}", keyword);
        return tianyanchaApiClient.getCompanyInfo("baseinfo/changeinfo", keyword);
    }

    /**
     * Get company type information.
     *
     * @param keyword 搜索关键词
     * @return 原始JSON响应
     */
    public String getCompanyTypeInfo(String keyword) {
        log.debug("Request to get company type info for keyword: {}", keyword);
        return tianyanchaApiClient.getCompanyInfo("baseinfo/companytype", keyword);
    }

    /**
     * Get company industrial and commercial information.
     *
     * @param keyword 搜索关键词
     * @return 原始JSON响应
     */
    public String getCompanyBusinessInfo(String keyword) {
        log.debug("Request to get company business info for keyword: {}", keyword);
        return tianyanchaApiClient.getCompanyInfo("baseinfo/business", keyword);
    }

    /**
     * Get company dishonest person records.
     *
     * @param keyword 搜索关键词
     * @return 原始JSON响应
     */
    public String getCompanyDishonestPersons(String keyword) {
        log.debug("Request to get company dishonest persons for keyword: {}", keyword);
        return tianyanchaApiClient.getCompanyInfo("risk/dishonest", keyword);
    }

    /**
     * Get company case filing information.
     *
     * @param keyword 搜索关键词
     * @return 原始JSON响应
     */
    public String getCompanyCaseFilings(String keyword) {
        log.debug("Request to get company case filings for keyword: {}", keyword);
        return tianyanchaApiClient.getCompanyInfo("risk/casefiling", keyword);
    }

    /**
     * Force refresh company data from Tianyancha API.
     * Bypasses cache and always fetches fresh data.
     *
     * @param keyword 搜索关键词
     * @return 更新后的企业信息
     */
    public CompanyDTO refreshCompanyData(String keyword) {
        log.debug("Request to force refresh company data for keyword: {}", keyword);

        // Fetch fresh data from API
        // 从API获取最新数据
        TianyanchaCompanyBasicInfoResponseDTO apiResponse = tianyanchaApiClient.getCompanyBasicInfo(keyword);

        if (apiResponse.getResult() == null) {
            throw new TianyanchaApiException("No company data found for keyword: " + keyword);
        }

        // Convert API response to entity
        // 将API响应转换为实体
        Company company = companyMapper.fromTianyanchaResponse(apiResponse);

        // Find existing company
        // 查找已存在的企业
        Optional<Company> existingCompany = companyService.findByAnyIdentifier(keyword);

        if (existingCompany.isPresent()) {
            // Update existing company
            // 更新已存在的企业
            Company existing = existingCompany.orElse(null);
            updateCompanyFromApiResponse(existing, company);
            company = companyService.save(existing);
            log.debug("Force refreshed existing company: {}", company.getName());
        } else {
            // Save new company
            // 保存新企业
            company = companyService.save(company);
            log.debug("Saved new company during force refresh: {}", company.getName());
        }

        return companyMapper.toDto(company);
    }

    /**
     * Update existing company entity with data from API response.
     * <p>
     * 使用API响应数据更新现有的企业实体。
     */
    private void updateCompanyFromApiResponse(Company existing, Company apiData) {
        // Update all fields from API data while preserving the existing ID
        // 更新所有来自API数据的字段，同时保留现有ID
        Long existingId = existing.getId();

        // Copy all fields from API data
        // 复制来自API数据的所有字段
        existing.setTianyanchaId(apiData.getTianyanchaId());
        existing.setName(apiData.getName());
        existing.setUnifiedSocialCreditCode(apiData.getUnifiedSocialCreditCode());
        existing.setLegalPersonName(apiData.getLegalPersonName());
        existing.setRegStatus(apiData.getRegStatus());
        existing.setRegCapital(apiData.getRegCapital());
        existing.setRegCapitalCurrency(apiData.getRegCapitalCurrency());
        existing.setEstablishTime(apiData.getEstablishTime());
        existing.setCompanyOrgType(apiData.getCompanyOrgType());
        existing.setRegNumber(apiData.getRegNumber());
        existing.setTaxNumber(apiData.getTaxNumber());
        existing.setOrgNumber(apiData.getOrgNumber());
        existing.setIndustry(apiData.getIndustry());
        existing.setRegLocation(apiData.getRegLocation());
        existing.setBusinessScope(apiData.getBusinessScope());
        existing.setTianyanchaUpdateTime(apiData.getTianyanchaUpdateTime());
        existing.setCacheTime(apiData.getCacheTime());
        existing.setHistoryNames(apiData.getHistoryNames());
        existing.setCancelDate(apiData.getCancelDate());
        existing.setRevokeDate(apiData.getRevokeDate());
        existing.setRevokeReason(apiData.getRevokeReason());
        existing.setCancelReason(apiData.getCancelReason());
        existing.setApprovedTime(apiData.getApprovedTime());
        existing.setFromTime(apiData.getFromTime());
        existing.setToTime(apiData.getToTime());
        existing.setActualCapital(apiData.getActualCapital());
        existing.setActualCapitalCurrency(apiData.getActualCapitalCurrency());
        existing.setRegInstitute(apiData.getRegInstitute());
        existing.setCity(apiData.getCity());
        existing.setDistrict(apiData.getDistrict());
        existing.setStaffNumRange(apiData.getStaffNumRange());
        existing.setSocialStaffNum(apiData.getSocialStaffNum());
        existing.setBondNum(apiData.getBondNum());
        existing.setBondName(apiData.getBondName());
        existing.setBondType(apiData.getBondType());
        existing.setUsedBondName(apiData.getUsedBondName());
        existing.setAlias(apiData.getAlias());
        existing.setProperty3(apiData.getProperty3());
        existing.setTags(apiData.getTags());
        existing.setPercentileScore(apiData.getPercentileScore());
        existing.setIsMicroEnt(apiData.getIsMicroEnt());
        existing.setBase(apiData.getBase());
        existing.setType(apiData.getType());
        existing.setCompForm(apiData.getCompForm());
        existing.setIndustryCategory(apiData.getIndustryCategory());
        existing.setIndustryCategoryBig(apiData.getIndustryCategoryBig());
        existing.setIndustryCategoryMiddle(apiData.getIndustryCategoryMiddle());
        existing.setIndustryCategorySmall(apiData.getIndustryCategorySmall());

        // Restore the original ID
        // 恢复原始ID
        existing.setId(existingId);
    }
}
