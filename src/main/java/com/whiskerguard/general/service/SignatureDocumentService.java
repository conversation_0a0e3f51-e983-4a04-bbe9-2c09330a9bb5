package com.whiskerguard.general.service;

import com.whiskerguard.general.service.dto.SignatureCallbackDTO;
import com.whiskerguard.general.service.dto.SignatureDocumentDTO;
import com.whiskerguard.general.service.dto.SignatureRequestDTO;
import com.whiskerguard.general.service.dto.SignatureResultDTO;
import java.util.Map;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.general.domain.SignatureDocument}.
 */
public interface SignatureDocumentService {
    /**
     * Save a signatureDocument.
     *
     * @param signatureDocumentDTO the entity to save.
     * @return the persisted entity.
     */
    SignatureDocumentDTO save(SignatureDocumentDTO signatureDocumentDTO);

    /**
     * Updates a signatureDocument.
     *
     * @param signatureDocumentDTO the entity to update.
     * @return the persisted entity.
     */
    SignatureDocumentDTO update(SignatureDocumentDTO signatureDocumentDTO);

    /**
     * Partially updates a signatureDocument.
     *
     * @param signatureDocumentDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<SignatureDocumentDTO> partialUpdate(SignatureDocumentDTO signatureDocumentDTO);

    /**
     * Get all the signatureDocuments.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<SignatureDocumentDTO> findAll(Pageable pageable);

    /**
     * Get the "id" signatureDocument.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<SignatureDocumentDTO> findOne(Long id);

    /**
     * Delete the "id" signatureDocument.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * 创建签名任务
     * <p>
     * 根据请求参数创建一个新的签名任务，并返回签名结果，包括签名URL、任务ID等信息。
     * </p>
     *
     * @param request 签名请求参数，包含文档信息、签署人信息等
     * @return 签名结果，包含签名URL、任务ID等信息
     */
    SignatureResultDTO createSignatureTask(SignatureRequestDTO request);

    /**
     * 查询签名状态
     * <p>
     * 根据签名文档ID查询当前签名任务的状态。
     * </p>
     *
     * @param documentId 签名文档ID
     * @return 签名文档DTO，包含最新的签名状态
     */
    SignatureDocumentDTO querySignatureStatus(Long documentId);

    /**
     * 处理签名回调
     * <p>
     * 处理签名服务商的回调请求，更新签名文档状态。
     * </p>
     *
     * @param provider    签名服务提供商
     * @param callbackDTO 回调数据
     * @param params      额外参数
     * @return 处理结果
     */
    Map<String, Object> handleCallback(String provider, SignatureCallbackDTO callbackDTO, Map<String, String> params);

    /**
     * 获取签名后的文档
     * <p>
     * 根据签名文档ID获取签名后的文档URL。
     * </p>
     *
     * @param documentId 签名文档ID
     * @return 签名后的文档URL
     */
    String getSignedDocument(Long documentId);

    /**
     * 取消签名任务
     * <p>
     * 根据签名文档ID取消正在进行的签名任务。
     * </p>
     *
     * @param documentId 签名文档ID
     * @return 取消结果，true表示成功，false表示失败
     */
    boolean cancelSignatureTask(Long documentId);
}
