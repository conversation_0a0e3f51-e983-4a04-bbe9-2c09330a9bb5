package com.whiskerguard.general.config;

import java.time.format.DateTimeFormatter;

/**
 * Application constants.
 */
public final class Constants {

    public static final String SYSTEM = "system";

    public static final String DEFAULT_LANGUAGE = "zh-cn";

    // 文件相关常量
    public static final String FILE_ALREADY_EXISTS_MSG = "File already exists";

    // COS 相关常量
    public static final String COS_REGION = "ap-nanjing";

    // 5MB 分片大小
    public static final long COS_PART_SIZE = 5 * 1024 * 1024L;

    // 默认上传目录
    public static final String DEFAULT_UPLOAD_DIR = "whiskerguard-";

    // 文件类型限制
    public static final String[] ALLOWED_FILE_TYPES = {
        // 图片
        "image/jpg",
        "image/jpeg",
        "image/png",
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/plain",
        // 压缩文件
        "application/zip",
        "application/x-zip-compressed",
        "application/x-rar-compressed",
        "application/octet-stream",
        // 视频文件
        "video/mp4",
        "video/avi",
        "video/mov",
        "video/quicktime",
        "video/x-msvideo",
        "video/x-ms-wmv",
        "video/x-flv",
        "video/x-matroska",
        "video/webm",
        "video/3gpp",
        "video/3gpp2"
    };

    // 文件大小限制 (5MB)
    public static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static final String SPILT_MEDIAN = "-";

    private Constants() {}
}
