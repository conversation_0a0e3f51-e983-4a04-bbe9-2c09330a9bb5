package com.whiskerguard.general.config;

import java.time.Duration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Configuration for Tianyancha API integration.
 * Provides beans for HTTP client and other necessary components.
 *
 * 天眼查API集成配置类。
 * 提供HTTP客户端和其他必要组件的Bean定义。
 */
@Configuration
public class TianyanchaConfiguration {

    private final TianyanchaProperties tianyanchaProperties;

    public TianyanchaConfiguration(TianyanchaProperties tianyanchaProperties) {
        this.tianyanchaProperties = tianyanchaProperties;
    }

    /**
     * RestTemplate bean configured for Tianyancha API calls.
     * Includes timeout settings based on configuration properties.
     *
     * 配置用于天眼查API调用的RestTemplate Bean。
     * 包含基于配置属性的超时设置。
     */
    @Bean
    public RestTemplate tianyanchaRestTemplate() {
        return new RestTemplateBuilder()
            .setConnectTimeout(Duration.ofMillis(tianyanchaProperties.getConnectionTimeoutMs()))
            .setReadTimeout(Duration.ofMillis(tianyanchaProperties.getReadTimeoutMs()))
            .build();
    }
}
