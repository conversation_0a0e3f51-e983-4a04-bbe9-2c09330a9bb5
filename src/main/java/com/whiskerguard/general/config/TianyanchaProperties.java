package com.whiskerguard.general.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for Tianyancha API integration.
 * This class holds the configuration values for connecting to the Tianyancha API,
 * including the API token, base URL, and cache settings.
 *
 * 天眼查API集成的配置属性。
 * 该类保存连接到天眼查API的配置值，包括API令牌、基础URL和缓存设置。
 */
@Configuration
@ConfigurationProperties(prefix = "tianyancha")
public class TianyanchaProperties {

    /**
     * The API token for authenticating with Tianyancha API.
     * This should be configured in application.yml or environment variables.
     *
     * 用于天眼查API认证的API令牌。
     * 应在application.yml或环境变量中配置。
     */
    private String apiToken;

    /**
     * The base URL for Tianyancha API.
     * Default: https://open.api.tianyancha.com/services/open/ic/
     *
     * 天眼查API的基础URL。
     * 默认值: https://open.api.tianyancha.com/services/open/ic/
     */
    private String baseUrl = "https://open.api.tianyancha.com/services/open/ic/";

    /**
     * Cache expiration time in hours.
     * Data older than this will be considered stale and refreshed from API.
     * Default: 168 hours (7 days)
     *
     * 缓存过期时间（小时）。
     * 超过此时间的数据将被视为过期，需要从API刷新。
     * 默认值: 168小时（7天）
     */
    private int cacheExpirationHours = 168;

    /**
     * Connection timeout for HTTP requests in milliseconds.
     * Default: 5000ms (5 seconds)
     *
     * HTTP请求的连接超时时间（毫秒）。
     * 默认值: 5000毫秒（5秒）
     */
    private int connectionTimeoutMs = 5000;

    /**
     * Read timeout for HTTP requests in milliseconds.
     * Default: 10000ms (10 seconds)
     *
     * HTTP请求的读取超时时间（毫秒）。
     * 默认值: 10000毫秒（10秒）
     */
    private int readTimeoutMs = 10000;

    /**
     * Maximum number of retry attempts for failed API calls.
     * Default: 3
     *
     * 失败API调用的最大重试次数。
     * 默认值: 3
     */
    private int maxRetryAttempts = 3;

    /**
     * Initial delay for retry attempts in milliseconds.
     * Default: 1000ms (1 second)
     *
     * 重试尝试的初始延迟时间（毫秒）。
     * 默认值: 1000毫秒（1秒）
     */
    private long retryDelayMs = 1000;

    // Getters and Setters - getter和setter方法

    public String getApiToken() {
        return apiToken;
    }

    public void setApiToken(String apiToken) {
        this.apiToken = apiToken;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public int getCacheExpirationHours() {
        return cacheExpirationHours;
    }

    public void setCacheExpirationHours(int cacheExpirationHours) {
        this.cacheExpirationHours = cacheExpirationHours;
    }

    public int getConnectionTimeoutMs() {
        return connectionTimeoutMs;
    }

    public void setConnectionTimeoutMs(int connectionTimeoutMs) {
        this.connectionTimeoutMs = connectionTimeoutMs;
    }

    public int getReadTimeoutMs() {
        return readTimeoutMs;
    }

    public void setReadTimeoutMs(int readTimeoutMs) {
        this.readTimeoutMs = readTimeoutMs;
    }

    public int getMaxRetryAttempts() {
        return maxRetryAttempts;
    }

    public void setMaxRetryAttempts(int maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }

    public long getRetryDelayMs() {
        return retryDelayMs;
    }

    public void setRetryDelayMs(long retryDelayMs) {
        this.retryDelayMs = retryDelayMs;
    }

    @Override
    public String toString() {
        return (
            "TianyanchaProperties{" +
            "baseUrl='" +
            baseUrl +
            '\'' +
            ", cacheExpirationHours=" +
            cacheExpirationHours +
            ", connectionTimeoutMs=" +
            connectionTimeoutMs +
            ", readTimeoutMs=" +
            readTimeoutMs +
            ", maxRetryAttempts=" +
            maxRetryAttempts +
            ", retryDelayMs=" +
            retryDelayMs +
            '}'
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TianyanchaProperties that = (TianyanchaProperties) o;

        if (cacheExpirationHours != that.cacheExpirationHours) return false;
        if (connectionTimeoutMs != that.connectionTimeoutMs) return false;
        if (readTimeoutMs != that.readTimeoutMs) return false;
        if (maxRetryAttempts != that.maxRetryAttempts) return false;
        if (retryDelayMs != that.retryDelayMs) return false;
        if (apiToken != null ? !apiToken.equals(that.apiToken) : that.apiToken != null) return false;
        return baseUrl != null ? baseUrl.equals(that.baseUrl) : that.baseUrl == null;
    }

    @Override
    public int hashCode() {
        int result = apiToken != null ? apiToken.hashCode() : 0;
        result = 31 * result + (baseUrl != null ? baseUrl.hashCode() : 0);
        result = 31 * result + cacheExpirationHours;
        result = 31 * result + connectionTimeoutMs;
        result = 31 * result + readTimeoutMs;
        result = 31 * result + maxRetryAttempts;
        result = 31 * result + (int) (retryDelayMs ^ (retryDelayMs >>> 32));
        return result;
    }
}
