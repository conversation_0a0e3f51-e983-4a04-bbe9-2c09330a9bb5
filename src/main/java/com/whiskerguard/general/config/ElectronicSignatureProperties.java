package com.whiskerguard.general.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 电子签名配置属性类
 * <p>
 * 用于配置不同电子签名服务提供商的参数，如API密钥、密钥、回调URL等。
 * 配置项在application.yml中以application.electronic-signature为前缀。
 * </p>
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "application.electronic-signature")
public class ElectronicSignatureProperties {

    /**
     * 回调基础URL
     */
    private String callbackBaseUrl;

    /**
     * 法大大配置
     */
    private final Fadada fadada = new Fadada();

    /**
     * e签宝配置
     */
    private final Esign esign = new Esign();

    public String getCallbackBaseUrl() {
        return callbackBaseUrl;
    }

    public void setCallbackBaseUrl(String callbackBaseUrl) {
        this.callbackBaseUrl = callbackBaseUrl;
    }

    public Fadada getFadada() {
        return fadada;
    }

    public Esign getEsign() {
        return esign;
    }

    /**
     * 法大大配置类
     */
    public static class Fadada {

        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appSecret;

        /**
         * API地址
         */
        private String apiUrl;

        /**
         * 回调URL
         */
        private String callbackUrl;

        /**
         * 是否启用
         */
        private boolean enabled = true;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppSecret() {
            return appSecret;
        }

        public void setAppSecret(String appSecret) {
            this.appSecret = appSecret;
        }

        public String getApiUrl() {
            return apiUrl;
        }

        public void setApiUrl(String apiUrl) {
            this.apiUrl = apiUrl;
        }

        public String getCallbackUrl() {
            return callbackUrl;
        }

        public void setCallbackUrl(String callbackUrl) {
            this.callbackUrl = callbackUrl;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }

    /**
     * e签宝配置类
     */
    public static class Esign {

        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appSecret;

        /**
         * API地址
         */
        private String apiUrl;

        /**
         * 回调URL
         */
        private String callbackUrl;

        /**
         * 是否启用
         */
        private boolean enabled = true;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppSecret() {
            return appSecret;
        }

        public void setAppSecret(String appSecret) {
            this.appSecret = appSecret;
        }

        public String getApiUrl() {
            return apiUrl;
        }

        public void setApiUrl(String apiUrl) {
            this.apiUrl = apiUrl;
        }

        public String getCallbackUrl() {
            return callbackUrl;
        }

        public void setCallbackUrl(String callbackUrl) {
            this.callbackUrl = callbackUrl;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }
}
