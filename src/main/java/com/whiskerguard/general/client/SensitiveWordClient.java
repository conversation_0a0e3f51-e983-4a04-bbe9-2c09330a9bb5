package com.whiskerguard.general.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.service.SensitiveWordService;
import com.whiskerguard.general.service.dto.SensitiveWordFilterRequestDTO;
import com.whiskerguard.general.service.dto.SensitiveWordFilterResponseDTO;
import com.whiskerguard.general.service.exception.SensitiveWordClientException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 敏感词过滤服务客户端
 * 为其他微服务提供敏感词过滤功能，处理请求、错误处理和重试逻辑
 *
 * Client for providing sensitive word filtering services to other microservices.
 * Handles requests, error handling, and retry logic.
 */
@Component
public class SensitiveWordClient {

    private static final Logger log = LoggerFactory.getLogger(SensitiveWordClient.class);

    /**
     * 默认最大重试次数
     * Default maximum retry attempts
     */
    private static final int DEFAULT_MAX_RETRY_ATTEMPTS = 3;

    /**
     * 默认初始重试延迟（毫秒）
     * Default initial retry delay in milliseconds
     */
    private static final long DEFAULT_INITIAL_RETRY_DELAY_MS = 100;

    private final SensitiveWordService sensitiveWordService;
    private final ObjectMapper objectMapper;

    /**
     * 构造函数
     * Constructor
     *
     * @param sensitiveWordService 敏感词服务
     *                            The sensitive word service
     * @param objectMapper JSON转换工具
     *                    JSON object mapper
     */
    public SensitiveWordClient(SensitiveWordService sensitiveWordService, ObjectMapper objectMapper) {
        this.sensitiveWordService = sensitiveWordService;
        this.objectMapper = objectMapper;
    }

    /**
     * 过滤内容中的敏感词
     *
     * Filters content for sensitive words.
     *
     * @param content 需要过滤的内容
     *               The content to be filtered
     * @return 过滤后的内容（敏感词已被替换）
     *        Filtered content with sensitive words replaced
     * @throws SensitiveWordClientException 如果过滤失败
     *                                    If filtering fails
     */
    public String filterContent(String content) {
        return executeWithRetry(
            () -> {
                try {
                    return sensitiveWordService.filterSensitiveWords(content);
                } catch (Exception e) {
                    log.error("过滤内容时出错: {}", e.getMessage());
                    throw new SensitiveWordClientException("过滤内容时出错: " + e.getMessage(), e);
                }
            },
            "filterContent"
        );
    }

    /**
     * 过滤内容中的敏感词，并返回过滤后的内容和发现的敏感词
     *
     * Filters content for sensitive words and returns both filtered content and found sensitive words.
     *
     * @param content 需要过滤的内容
     *               The content to be filtered
     * @return 包含过滤后内容和发现的敏感词的响应
     *        Response containing filtered content and found sensitive words
     * @throws SensitiveWordClientException 如果过滤失败
     *                                    If filtering fails
     */
    public SensitiveWordFilterResponseDTO filterContentDetailed(String content) {
        return executeWithRetry(
            () -> {
                try {
                    String filteredContent = sensitiveWordService.filterSensitiveWords(content);
                    List<String> foundWords = sensitiveWordService.findSensitiveWords(content);

                    SensitiveWordFilterResponseDTO response = new SensitiveWordFilterResponseDTO();
                    response.setOriginalContent(content);
                    response.setFilteredContent(filteredContent);
                    response.setFoundSensitiveWords(foundWords);
                    response.setContainsSensitiveWords(!foundWords.isEmpty());

                    return response;
                } catch (Exception e) {
                    log.error("详细过滤内容时出错: {}", e.getMessage());
                    throw new SensitiveWordClientException("详细过滤内容时出错: " + e.getMessage(), e);
                }
            },
            "filterContentDetailed"
        );
    }

    /**
     * 批量处理多个内容项的敏感词过滤
     *
     * Batch processes multiple content items for sensitive word filtering.
     *
     * @param request 包含多个需要过滤的内容项的请求
     *               The request containing multiple content items to filter
     * @return 包含所有过滤后内容项的响应列表
     *        List of responses with all filtered content items
     * @throws SensitiveWordClientException 如果批量过滤失败
     *                                    If batch filtering fails
     */
    public List<SensitiveWordFilterResponseDTO> batchFilterContent(SensitiveWordFilterRequestDTO request) {
        return executeWithRetry(
            () -> {
                try {
                    return request.getContents().stream().map(this::filterContentDetailed).collect(Collectors.toList());
                } catch (Exception e) {
                    log.error("批量过滤内容时出错: {}", e.getMessage());
                    throw new SensitiveWordClientException("批量过滤内容时出错: " + e.getMessage(), e);
                }
            },
            "batchFilterContent"
        );
    }

    /**
     * 检查内容是否包含任何敏感词，不进行过滤
     *
     * Checks if content contains any sensitive words without filtering.
     *
     * @param content 需要检查的内容
     *               The content to check
     * @return 如果发现敏感词则返回true，否则返回false
     *        True if sensitive words are found, false otherwise
     * @throws SensitiveWordClientException 如果检查失败
     *                                    If checking fails
     */
    public boolean containsSensitiveWords(String content) {
        return executeWithRetry(
            () -> {
                try {
                    return !sensitiveWordService.findSensitiveWords(content).isEmpty();
                } catch (Exception e) {
                    log.error("检查内容是否包含敏感词时出错: {}", e.getMessage());
                    throw new SensitiveWordClientException("检查内容是否包含敏感词时出错: " + e.getMessage(), e);
                }
            },
            "containsSensitiveWords"
        );
    }

    /**
     * 使用重试逻辑执行敏感词服务调用
     *
     * Executes the sensitive word service call with retry logic.
     *
     * @param <T> 返回类型
     *           Return type
     * @param clientCall 客户端调用供应商
     *                  Client call supplier
     * @param operation 操作名称，用于日志记录
     *                 Operation name for logging
     * @return 服务调用的结果
     *        Result of the service call
     * @throws SensitiveWordClientException 如果所有重试尝试都失败
     *                                    If all retry attempts fail
     */
    private <T> T executeWithRetry(ClientCallSupplier<T> clientCall, String operation) {
        int attempts = 0;
        long delay = DEFAULT_INITIAL_RETRY_DELAY_MS;
        int maxRetryAttempts = DEFAULT_MAX_RETRY_ATTEMPTS;

        while (attempts < maxRetryAttempts) {
            try {
                return clientCall.get();
            } catch (SensitiveWordClientException e) {
                attempts++;

                if (attempts >= maxRetryAttempts) {
                    log.error("超过最大重试次数 ({}) 用于操作: {}", maxRetryAttempts, operation);
                    throw e;
                }

                log.warn("服务调用失败 (尝试 {}/{}), {}毫秒后重试: {}", attempts, maxRetryAttempts, delay, e.getMessage());

                try {
                    TimeUnit.MILLISECONDS.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new SensitiveWordClientException("重试延迟期间被中断", ie);
                }

                // 指数退避策略
                // Exponential backoff
                delay *= 2;
            }
        }

        throw new SensitiveWordClientException("重试逻辑中出现意外错误");
    }

    /**
     * 客户端调用供应商的函数式接口
     *
     * Functional interface for client call suppliers.
     *
     * @param <T> 返回类型
     *           Return type
     */
    @FunctionalInterface
    private interface ClientCallSupplier<T> {
        /**
         * 获取结果
         * Get result
         *
         * @return 结果
         *        Result
         * @throws SensitiveWordClientException 如果调用失败
         *                                    If call fails
         */
        T get() throws SensitiveWordClientException;
    }
}
