package com.whiskerguard.general.client;

import com.whiskerguard.common.config.RequestConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Component
public class UserFeignClientInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String token = request.getHeader("Authorization");
            template.header("Authorization", token);
            template.header(RequestConstants.X_TENANT_ID, request.getHeader(RequestConstants.X_TENANT_ID));
            template.header(RequestConstants.X_USER_ID, request.getHeader(RequestConstants.X_USER_ID));
            template.header(RequestConstants.X_USER_NAME, request.getHeader(RequestConstants.X_USER_NAME));
            template.header(RequestConstants.X_SOURCE, request.getHeader(RequestConstants.X_SOURCE));
            template.header(RequestConstants.X_VERSION, request.getHeader(RequestConstants.X_VERSION));
            template.header(RequestConstants.X_FORWARDED_FOR, request.getHeader(RequestConstants.X_FORWARDED_FOR));
        }
    }
}
