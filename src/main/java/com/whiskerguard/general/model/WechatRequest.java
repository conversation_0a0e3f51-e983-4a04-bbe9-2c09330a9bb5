package com.whiskerguard.general.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信公众号推送请求
 */
public class WechatRequest extends NotificationRequest {

    /**
     * 消息类型：text, image, voice, video, music, news, mpnews, mpvideo, wxcard
     */
    private String messageType = "text";

    /**
     * 接收者OpenID列表
     */
    private List<String> toUsers = new ArrayList<>();

    /**
     * 消息内容（文本消息）
     */
    private String content;

    /**
     * 媒体ID（图片、语音、视频等）
     */
    private String mediaId;

    /**
     * 图文消息
     */
    private List<Article> articles = new ArrayList<>();

    /**
     * 模板消息ID
     */
    private String templateId;

    /**
     * 模板消息数据
     */
    private Map<String, TemplateData> templateData = new HashMap<>();

    /**
     * 跳转URL
     */
    private String url;

    /**
     * 小程序信息
     */
    private MiniProgram miniProgram;

    public WechatRequest() {
        setType(NotificationType.WECHAT);
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public List<String> getToUsers() {
        return toUsers;
    }

    public void setToUsers(List<String> toUsers) {
        this.toUsers = toUsers;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public List<Article> getArticles() {
        return articles;
    }

    public void setArticles(List<Article> articles) {
        this.articles = articles;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Map<String, TemplateData> getTemplateData() {
        return templateData;
    }

    public void setTemplateData(Map<String, TemplateData> templateData) {
        this.templateData = templateData;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public MiniProgram getMiniProgram() {
        return miniProgram;
    }

    public void setMiniProgram(MiniProgram miniProgram) {
        this.miniProgram = miniProgram;
    }

    /**
     * 图文消息文章
     */
    public static class Article {
        private String title;
        private String description;
        private String url;
        private String picUrl;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getPicUrl() {
            return picUrl;
        }

        public void setPicUrl(String picUrl) {
            this.picUrl = picUrl;
        }
    }

    /**
     * 模板消息数据
     */
    public static class TemplateData {
        private String value;
        private String color;

        public TemplateData() {}

        public TemplateData(String value) {
            this.value = value;
        }

        public TemplateData(String value, String color) {
            this.value = value;
            this.color = color;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }
    }

    /**
     * 小程序信息
     */
    public static class MiniProgram {
        private String appId;
        private String pagePath;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getPagePath() {
            return pagePath;
        }

        public void setPagePath(String pagePath) {
            this.pagePath = pagePath;
        }
    }
}
