package com.whiskerguard.general.model;

import com.whiskerguard.general.domain.enumeration.VerificationCodeType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * 验证码请求模型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
@Data
public class VerificationCodeRequest {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phoneNumber;

    /**
     * 手机号列表
     */
    private List<String> phoneNumbers;

    /**
     * 验证码类型
     */
    private VerificationCodeType codeType = VerificationCodeType.LOGIN;

    /**
     * 国际区号，默认中国大陆 +86
     */
    private String regionCode = "86";

}
