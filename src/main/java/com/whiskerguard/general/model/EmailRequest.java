package com.whiskerguard.general.model;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 邮件通知请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-30
 */
public class EmailRequest extends NotificationRequest {

    /**
     * 收件人列表（支持多个收件人）
     */
    @NotEmpty(message = "收件人不能为空")
    private List<String> to = new ArrayList<>();

    /**
     * 邮件主题
     */
    @NotNull(message = "邮件主题不能为空")
    @Size(max = 200, message = "邮件主题长度不能超过200字符")
    private String subject;

    /**
     * 邮件内容（HTML格式）
     */
    @NotNull(message = "邮件内容不能为空")
    private String content;

    /**
     * 抄送列表
     */
    private List<String> cc = new ArrayList<>();

    /**
     * 密送列表
     */
    private List<String> bcc = new ArrayList<>();

    /**
     * 发件人邮箱地址（可选，默认使用配置的发件人）
     */
    private String from;

    /**
     * 发件人名称
     */
    private String fromName;

    /**
     * 回复邮箱地址
     */
    private String replyTo;

    /**
     * 是否使用HTML格式
     */
    private boolean html = true;

    /**
     * 邮件优先级（1-高，3-普通，5-低）
     */
    private Integer priority = 3;

    /**
     * 附件列表
     */
    private List<EmailAttachment> attachments = new ArrayList<>();

    /**
     * 内嵌资源（用于HTML邮件中的图片等）
     */
    private Map<String, String> inlineResources;

    /**
     * 业务ID（用于追踪）
     */
    private String businessId;

    /**
     * 邮件类型
     */
    private String emailType;

    /**
     * 是否异步发送
     */
    private boolean async = true;

    /**
     * 发送时间（定时发送，为空则立即发送）
     */
    private Instant sendTime;

    /**
     * 重试次数
     */
    private Integer retryCount = 3;

    /**
     * 标签（用于分类）
     */
    private List<String> tags = new ArrayList<>();

    public EmailRequest() {
        setType(NotificationType.EMAIL);
    }

    public EmailRequest(List<String> to, String subject, String content) {
        this();
        this.to = to;
        this.subject = subject;
        this.content = content;
    }

    public EmailRequest(List<String> to, String subject, String content, boolean html) {
        this(to, subject, content);
        this.html = html;
    }

    // Getters and Setters

    public List<String> getTo() {
        return to;
    }

    public void setTo(List<String> to) {
        this.to = to;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getCc() {
        return cc;
    }

    public void setCc(List<String> cc) {
        this.cc = cc;
    }

    public List<String> getBcc() {
        return bcc;
    }

    public void setBcc(List<String> bcc) {
        this.bcc = bcc;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public boolean isHtml() {
        return html;
    }

    public void setHtml(boolean html) {
        this.html = html;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public List<EmailAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<EmailAttachment> attachments) {
        this.attachments = attachments;
    }

    public Map<String, String> getInlineResources() {
        return inlineResources;
    }

    public void setInlineResources(Map<String, String> inlineResources) {
        this.inlineResources = inlineResources;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getEmailType() {
        return emailType;
    }

    public void setEmailType(String emailType) {
        this.emailType = emailType;
    }

    public boolean isAsync() {
        return async;
    }

    public void setAsync(boolean async) {
        this.async = async;
    }

    public Instant getSendTime() {
        return sendTime;
    }

    public void setSendTime(Instant sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    // 便捷方法

    /**
     * 添加收件人
     */
    public EmailRequest addTo(String email) {
        if (this.to == null) {
            this.to = new ArrayList<>();
        }
        this.to.add(email);
        return this;
    }

    /**
     * 添加抄送
     */
    public EmailRequest addCc(String email) {
        if (this.cc == null) {
            this.cc = new ArrayList<>();
        }
        this.cc.add(email);
        return this;
    }

    /**
     * 添加密送
     */
    public EmailRequest addBcc(String email) {
        if (this.bcc == null) {
            this.bcc = new ArrayList<>();
        }
        this.bcc.add(email);
        return this;
    }

    /**
     * 添加附件
     */
    public EmailRequest addAttachment(EmailAttachment attachment) {
        if (this.attachments == null) {
            this.attachments = new ArrayList<>();
        }
        this.attachments.add(attachment);
        return this;
    }

    /**
     * 添加标签
     */
    public EmailRequest addTag(String tag) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        this.tags.add(tag);
        return this;
    }

    @Override
    public String toString() {
        return "EmailRequest{" +
            "to=" + to +
            ", subject='" + subject + '\'' +
            ", cc=" + cc +
            ", bcc=" + bcc +
            ", from='" + from + '\'' +
            ", fromName='" + fromName + '\'' +
            ", replyTo='" + replyTo + '\'' +
            ", html=" + html +
            ", priority=" + priority +
            ", businessId='" + businessId + '\'' +
            ", emailType='" + emailType + '\'' +
            ", async=" + async +
            ", sendTime=" + sendTime +
            ", retryCount=" + retryCount +
            ", tags=" + tags +
            ", attachments=" + (attachments != null ? attachments.size() : 0) + " attachments" +
            '}';
    }
}
