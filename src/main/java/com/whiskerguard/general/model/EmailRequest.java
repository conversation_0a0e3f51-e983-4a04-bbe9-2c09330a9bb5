package com.whiskerguard.general.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 邮件通知请求
 */
public class EmailRequest extends NotificationRequest {

    /**
     * 邮件主题
     */
    private String subject;

    /**
     * 邮件内容（HTML格式）
     */
    private String content;

    /**
     * 抄送列表
     */
    private List<String> cc = new ArrayList<>();

    /**
     * 密送列表
     */
    private List<String> bcc = new ArrayList<>();

    /**
     * 是否使用HTML格式
     */
    private boolean html = true;

    public EmailRequest() {
        setType(NotificationType.EMAIL);
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getCc() {
        return cc;
    }

    public void setCc(List<String> cc) {
        this.cc = cc;
    }

    public List<String> getBcc() {
        return bcc;
    }

    public void setBcc(List<String> bcc) {
        this.bcc = bcc;
    }

    public boolean isHtml() {
        return html;
    }

    public void setHtml(boolean html) {
        this.html = html;
    }
}
