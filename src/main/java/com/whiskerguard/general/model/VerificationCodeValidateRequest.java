package com.whiskerguard.general.model;

import com.whiskerguard.general.domain.enumeration.VerificationCodeType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 验证码验证请求模型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
@Data
public class VerificationCodeValidateRequest {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phoneNumber;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Size(min = 4, max = 4, message = "验证码必须为4位数字")
    @Pattern(regexp = "^\\d{4}$", message = "验证码必须为4位数字")
    private String code;

    /**
     * 验证码类型
     */
    private VerificationCodeType codeType = VerificationCodeType.LOGIN;

    /**
     * 是否验证后删除验证码（一次性使用）
     */
    private boolean deleteAfterValidation = true;

}
