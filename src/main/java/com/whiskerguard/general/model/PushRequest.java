package com.whiskerguard.general.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * APP推送通知请求
 */
public class PushRequest extends NotificationRequest {

    /**
     * 推送标题
     */
    private String title;

    /**
     * 推送内容
     */
    private String content;

    /**
     * 推送目标类型：单个设备、多个设备、别名、标签等
     */
    private PushTargetType targetType = PushTargetType.ALIAS;

    /**
     * 推送目标值列表
     */
    private List<String> targets = new ArrayList<>();

    /**
     * 额外数据
     */
    private Map<String, Object> extras = new HashMap<>();

    /**
     * 通知栏样式ID
     */
    private Integer notificationStyleId;

    /**
     * 是否静默推送
     */
    private boolean silent = false;

    public PushRequest() {
        setType(NotificationType.PUSH);
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public PushTargetType getTargetType() {
        return targetType;
    }

    public void setTargetType(PushTargetType targetType) {
        this.targetType = targetType;
    }

    public List<String> getTargets() {
        return targets;
    }

    public void setTargets(List<String> targets) {
        this.targets = targets;
    }

    public Map<String, Object> getExtras() {
        return extras;
    }

    public void setExtras(Map<String, Object> extras) {
        this.extras = extras;
    }

    public Integer getNotificationStyleId() {
        return notificationStyleId;
    }

    public void setNotificationStyleId(Integer notificationStyleId) {
        this.notificationStyleId = notificationStyleId;
    }

    public boolean isSilent() {
        return silent;
    }

    public void setSilent(boolean silent) {
        this.silent = silent;
    }
}
