package com.whiskerguard.general.model;

/**
 * 通知响应
 */
public class NotificationResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应代码
     */
    private String code;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 通知类型
     */
    private NotificationType type;

    /**
     * 消息ID
     */
    private String messageId;

    public NotificationResponse() {}

    public NotificationResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public static NotificationResponse success(String message) {
        return new NotificationResponse(true, message);
    }

    public static NotificationResponse success(String message, String businessId) {
        NotificationResponse response = new NotificationResponse(true, message);
        response.setBusinessId(businessId);
        return response;
    }

    public static NotificationResponse failure(String message) {
        return new NotificationResponse(false, message);
    }

    public static NotificationResponse failure(String message, String code) {
        NotificationResponse response = new NotificationResponse(false, message);
        response.setCode(code);
        return response;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public NotificationType getType() {
        return type;
    }

    public void setType(NotificationType type) {
        this.type = type;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
}
