package com.whiskerguard.general.model;

import java.io.InputStream;

/**
 * 邮件附件
 */
public class EmailAttachment {

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 附件内容类型
     */
    private String contentType;

    /**
     * 附件大小（字节）
     */
    private Long size;

    /**
     * 附件数据（字节数组）
     */
    private byte[] data;

    /**
     * 附件输入流
     */
    private InputStream inputStream;

    /**
     * 附件文件路径
     */
    private String filePath;

    /**
     * 是否为内联附件
     */
    private boolean inline = false;

    /**
     * 内联附件的Content-ID
     */
    private String contentId;

    public EmailAttachment() {}

    public EmailAttachment(String fileName, byte[] data) {
        this.fileName = fileName;
        this.data = data;
    }

    public EmailAttachment(String fileName, String contentType, byte[] data) {
        this.fileName = fileName;
        this.contentType = contentType;
        this.data = data;
    }

    public EmailAttachment(String fileName, InputStream inputStream) {
        this.fileName = fileName;
        this.inputStream = inputStream;
    }

    public EmailAttachment(String fileName, String filePath) {
        this.fileName = fileName;
        this.filePath = filePath;
    }

    // Getters and Setters

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
        if (data != null) {
            this.size = (long) data.length;
        }
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public boolean isInline() {
        return inline;
    }

    public void setInline(boolean inline) {
        this.inline = inline;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    @Override
    public String toString() {
        return "EmailAttachment{" +
            "fileName='" + fileName + '\'' +
            ", contentType='" + contentType + '\'' +
            ", size=" + size +
            ", inline=" + inline +
            ", contentId='" + contentId + '\'' +
            '}';
    }
}
