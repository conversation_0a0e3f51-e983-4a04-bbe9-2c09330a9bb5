package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.SignatureDocument;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.Optional;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the SignatureDocument entity.
 */
@SuppressWarnings("unused")
@Repository
public interface SignatureDocumentRepository extends JpaRepository<SignatureDocument, Long>, JpaSpecificationExecutor<SignatureDocument> {
    // 使用JpaSpecificationExecutor提供的findOne方法即可
}
