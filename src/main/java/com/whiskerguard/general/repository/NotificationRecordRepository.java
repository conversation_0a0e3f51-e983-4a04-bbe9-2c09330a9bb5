package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.enumeration.NotificationStatus;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the NotificationRecord entity.
 */
@SuppressWarnings("unused")
@Repository
public interface NotificationRecordRepository extends JpaRepository<NotificationRecord, Long> {
    /**
     * 根据业务ID和业务类型查找通知记录
     */
    List<NotificationRecord> findByBusinessIdAndBusinessType(String businessId, String businessType);

    /**
     * 查找指定状态且计划发送时间小于等于指定时间的通知记录
     */
    List<NotificationRecord> findByStatusAndScheduledTimeLessThanEqual(NotificationStatus status, Instant scheduledTime);

    /**
     * 查找指定状态且计划发送时间小于等于指定时间的通知记录（别名方法）
     */
    default List<NotificationRecord> findByStatusAndScheduledTimeBeforeOrEqual(NotificationStatus status, Instant scheduledTime) {
        return findByStatusAndScheduledTimeLessThanEqual(status, scheduledTime);
    }
}
