package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.CompanyRisk;
import java.util.List;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the CompanyRisk entity.
 */
@SuppressWarnings("unused")
@Repository
public interface CompanyRiskRepository extends JpaRepository<CompanyRisk, Long> {
    /**
     * Find all risks by company ID.
     */
    List<CompanyRisk> findByCompanyId(Long companyId);

    /**
     * Find risks by company ID and risk type.
     */
    List<CompanyRisk> findByCompanyIdAndRiskType(Long companyId, String riskType);

    /**
     * Delete all risks by company ID.
     */
    void deleteByCompanyId(Long companyId);

    /**
     * Count risks by company ID.
     */
    long countByCompanyId(Long companyId);
}
