package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.CompanyDishonestPerson;
import java.util.List;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the CompanyDishonestPerson entity.
 */
@SuppressWarnings("unused")
@Repository
public interface CompanyDishonestPersonRepository extends JpaRepository<CompanyDishonestPerson, Long> {
    /**
     * Find all dishonest persons by company ID.
     */
    List<CompanyDishonestPerson> findByCompanyId(Long companyId);

    /**
     * Find dishonest persons by company ID ordered by publish date descending.
     */
    List<CompanyDishonestPerson> findByCompanyIdOrderByPublishDateDesc(Long companyId);

    /**
     * Find dishonest persons by company ID and person name.
     */
    List<CompanyDishonestPerson> findByCompanyIdAndPersonName(Long companyId, String personName);

    /**
     * Delete all dishonest persons by company ID.
     */
    void deleteByCompanyId(Long companyId);

    /**
     * Count dishonest persons by company ID.
     */
    long countByCompanyId(Long companyId);
}
