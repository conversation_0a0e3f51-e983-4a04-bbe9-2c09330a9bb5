package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.CompanyCaseFiling;
import java.util.List;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the CompanyCaseFiling entity.
 */
@SuppressWarnings("unused")
@Repository
public interface CompanyCaseFilingRepository extends JpaRepository<CompanyCaseFiling, Long> {
    /**
     * Find all case filings by company ID.
     */
    List<CompanyCaseFiling> findByCompanyId(Long companyId);

    /**
     * Find case filings by company ID ordered by filing date descending.
     */
    List<CompanyCaseFiling> findByCompanyIdOrderByFilingDateDesc(Long companyId);

    /**
     * Find case filings by company ID and case type.
     */
    List<CompanyCaseFiling> findByCompanyIdAndCaseType(Long companyId, String caseType);

    /**
     * Delete all case filings by company ID.
     */
    void deleteByCompanyId(Long companyId);

    /**
     * Count case filings by company ID.
     */
    long countByCompanyId(Long companyId);
}
