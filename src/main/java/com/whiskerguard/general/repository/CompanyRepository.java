package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.Company;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the Company entity.
 *
 * 企���实体的Spring Data JPA存储库。
 */
@SuppressWarnings("unused")
@Repository
public interface CompanyRepository extends JpaRepository<Company, Long> {
    /**
     * Find company by unified social credit code.
     *
     * 通过统一社会信用代码查找企业。
     */
    Optional<Company> findByUnifiedSocialCreditCode(String unifiedSocialCreditCode);

    /**
     * Find company by name (exact match).
     *
     * 通过名称查找企业（精确匹配）。
     */
    Optional<Company> findByName(String name);

    /**
     * Find company by registration number.
     *
     * 通过注册号查找企业。
     */
    Optional<Company> findByRegNumber(String regNumber);

    /**
     * Find company by tax number.
     *
     * 通过税号查找企业。
     */
    Optional<Company> findByTaxNumber(String taxNumber);

    /**
     * Find company by Tianyancha ID.
     *
     * 通过天眼查ID查找企业。
     */
    Optional<Company> findByTianyanchaId(Long tianyanchaId);

    /**
     * Find companies by name containing (case-insensitive).
     *
     * 查找名称包含指定字符串的企业（不区分大小写）。
     */
    @Query("SELECT c FROM Company c WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<Company> findByNameContainingIgnoreCase(@Param("name") String name);

    /**
     * Check if company exists by unified social credit code.
     *
     * 检查指定统一社会信用代码的企业是否存在。
     */
    boolean existsByUnifiedSocialCreditCode(String unifiedSocialCreditCode);

    /**
     * Check if company exists by name.
     *
     * 检查指定名称的企业是否存在。
     */
    boolean existsByName(String name);
}
