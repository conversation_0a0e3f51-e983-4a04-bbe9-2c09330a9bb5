package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.SensitiveWord;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 敏感词实体的 Spring Data JPA 仓库
 * Spring Data JPA repository for the SensitiveWord entity.
 */
@Repository
public interface SensitiveWordRepository extends JpaRepository<SensitiveWord, Long> {
    /**
     * 查询特定租户的有效敏感词
     * Find valid sensitive words for a specific tenant.
     *
     * @param tenantId 租户ID（0表示平台级）
     * @param now 当前时间点
     * @return 当前有效的敏感词列表
     */
    @Query(
        "SELECT sw FROM SensitiveWord sw WHERE sw.tenantId = :tenantId " +
        "AND sw.isDeleted = false " +
        "AND sw.validFrom <= :now " +
        "AND sw.validTo >= :now"
    )
    List<SensitiveWord> findValidByTenant(@Param("tenantId") Long tenantId, @Param("now") Instant now);

    /**
     * 使用当前时间查询有效敏感词的便捷方法
     * Convenience method that uses current time for validity check
     */
    default List<SensitiveWord> findValidByTenant(Long tenantId) {
        return findValidByTenant(tenantId, Instant.now());
    }

    /**
     * 查询所有当前有效的敏感词（平台级和所有租户）
     * Find all currently active sensitive words (platform-wide and for all tenants).
     *
     * @return 所有当前有效的敏感词列表
     *         List of all currently active sensitive words
     */
    @Query(
        "SELECT sw FROM SensitiveWord sw WHERE sw.isDeleted = false " +
        "AND sw.validFrom <= CURRENT_TIMESTAMP " +
        "AND sw.validTo >= CURRENT_TIMESTAMP"
    )
    List<SensitiveWord> findActiveSensitiveWords();
}
