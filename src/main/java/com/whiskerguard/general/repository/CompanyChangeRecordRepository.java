package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.CompanyChangeRecord;
import java.util.List;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the CompanyChangeRecord entity.
 */
@SuppressWarnings("unused")
@Repository
public interface CompanyChangeRecordRepository extends JpaRepository<CompanyChangeRecord, Long> {
    /**
     * Find all change records by company ID.
     */
    List<CompanyChangeRecord> findByCompanyId(Long companyId);

    /**
     * Find change records by company ID ordered by change date descending.
     */
    List<CompanyChangeRecord> findByCompanyIdOrderByChangeDateDesc(Long companyId);

    /**
     * Find change records by company ID and change item.
     */
    List<CompanyChangeRecord> findByCompanyIdAndChangeItem(Long companyId, String changeItem);

    /**
     * Delete all change records by company ID.
     */
    void deleteByCompanyId(Long companyId);

    /**
     * Count change records by company ID.
     */
    long countByCompanyId(Long companyId);
}
