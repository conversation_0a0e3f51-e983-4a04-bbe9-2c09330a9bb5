package com.whiskerguard.general.repository;

import com.whiskerguard.general.domain.CompanyContact;
import java.util.Optional;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the CompanyContact entity.
 */
@SuppressWarnings("unused")
@Repository
public interface CompanyContactRepository extends JpaRepository<CompanyContact, Long> {
    /**
     * Find contact by company ID.
     */
    Optional<CompanyContact> findByCompanyId(Long companyId);

    /**
     * Delete contact by company ID.
     */
    void deleteByCompanyId(Long companyId);
}
