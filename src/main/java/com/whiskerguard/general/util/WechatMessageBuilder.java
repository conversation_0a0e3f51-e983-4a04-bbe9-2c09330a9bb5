package com.whiskerguard.general.util;

import com.whiskerguard.general.model.WechatRequest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 微信消息构建工具类
 * 提供便捷的方法来构建各种类型的微信消息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/15
 */
public class WechatMessageBuilder {

    /**
     * 构建文本消息
     */
    public static WechatRequest buildTextMessage(String openId, String content) {
        return buildTextMessage(Collections.singletonList(openId), content);
    }

    /**
     * 构建批量文本消息
     */
    public static WechatRequest buildTextMessage(List<String> openIds, String content) {
        WechatRequest request = new WechatRequest();
        request.setMessageType("text");
        request.setToUsers(openIds);
        request.setContent(content);
        return request;
    }

    /**
     * 构建图片消息
     */
    public static WechatRequest buildImageMessage(String openId, String mediaId) {
        return buildImageMessage(Collections.singletonList(openId), mediaId);
    }

    /**
     * 构建批量图片消息
     */
    public static WechatRequest buildImageMessage(List<String> openIds, String mediaId) {
        WechatRequest request = new WechatRequest();
        request.setMessageType("image");
        request.setToUsers(openIds);
        request.setMediaId(mediaId);
        return request;
    }

    /**
     * 构建语音消息
     */
    public static WechatRequest buildVoiceMessage(String openId, String mediaId) {
        return buildVoiceMessage(Collections.singletonList(openId), mediaId);
    }

    /**
     * 构建批量语音消息
     */
    public static WechatRequest buildVoiceMessage(List<String> openIds, String mediaId) {
        WechatRequest request = new WechatRequest();
        request.setMessageType("voice");
        request.setToUsers(openIds);
        request.setMediaId(mediaId);
        return request;
    }

    /**
     * 构建视频消息
     */
    public static WechatRequest buildVideoMessage(String openId, String mediaId) {
        return buildVideoMessage(Collections.singletonList(openId), mediaId);
    }

    /**
     * 构建批量视频消息
     */
    public static WechatRequest buildVideoMessage(List<String> openIds, String mediaId) {
        WechatRequest request = new WechatRequest();
        request.setMessageType("video");
        request.setToUsers(openIds);
        request.setMediaId(mediaId);
        return request;
    }

    /**
     * 构建图文消息
     */
    public static WechatRequest buildNewsMessage(String openId, List<WechatRequest.Article> articles) {
        return buildNewsMessage(Collections.singletonList(openId), articles);
    }

    /**
     * 构建批量图文消息
     */
    public static WechatRequest buildNewsMessage(List<String> openIds, List<WechatRequest.Article> articles) {
        WechatRequest request = new WechatRequest();
        request.setMessageType("news");
        request.setToUsers(openIds);
        request.setArticles(articles);
        return request;
    }

    /**
     * 构建单篇图文消息
     */
    public static WechatRequest buildSingleNewsMessage(String openId, String title, String description,
                                                       String url, String picUrl) {
        WechatRequest.Article article = createArticle(title, description, url, picUrl);
        return buildNewsMessage(openId, List.of(article));
    }

    /**
     * 构建模板消息
     */
    public static WechatRequest buildTemplateMessage(String openId, String templateId, String url) {
        WechatRequest request = new WechatRequest();
        request.setToUsers(Collections.singletonList(openId));
        request.setTemplateId(templateId);
        request.setUrl(url);
        return request;
    }

    /**
     * 构建带小程序的模板消息
     */
    public static WechatRequest buildTemplateMessageWithMiniProgram(String openId, String templateId,
                                                                    String miniProgramAppId, String pagePath) {
        WechatRequest request = new WechatRequest();
        request.setToUsers(Collections.singletonList(openId));
        request.setTemplateId(templateId);

        WechatRequest.MiniProgram miniProgram = new WechatRequest.MiniProgram();
        miniProgram.setAppId(miniProgramAppId);
        miniProgram.setPagePath(pagePath);
        request.setMiniProgram(miniProgram);

        return request;
    }

    /**
     * 创建模板数据
     */
    public static WechatRequest.TemplateData createTemplateData(String value) {
        return new WechatRequest.TemplateData(value);
    }

    /**
     * 创建带颜色的模板数据
     */
    public static WechatRequest.TemplateData createTemplateData(String value, String color) {
        return new WechatRequest.TemplateData(value, color);
    }

    /**
     * 创建图文消息文章
     */
    public static WechatRequest.Article createArticle(String title, String description, String url, String picUrl) {
        WechatRequest.Article article = new WechatRequest.Article();
        article.setTitle(title);
        article.setDescription(description);
        article.setUrl(url);
        article.setPicUrl(picUrl);
        return article;
    }

    /**
     * 构建通知类模板消息
     * 适用于系统通知、订单状态等场景
     */
    public static WechatRequest buildNotificationTemplate(String openId, String templateId,
                                                          String title, String content, String remark, String url) {
        WechatRequest request = buildTemplateMessage(openId, templateId, url);

        // 添加通用的模板数据
        request.getTemplateData().put("first", createTemplateData(title, "#173177"));
        request.getTemplateData().put("keyword1", createTemplateData(content));
        request.getTemplateData().put("remark", createTemplateData(remark, "#173177"));

        return request;
    }

    /**
     * 构建订单状态模板消息
     */
    public static WechatRequest buildOrderStatusTemplate(String openId, String templateId,
                                                         String orderNo, String status, String time, String url) {
        WechatRequest request = buildTemplateMessage(openId, templateId, url);

        request.getTemplateData().put("first", createTemplateData("您的订单状态已更新", "#173177"));
        request.getTemplateData().put("keyword1", createTemplateData(orderNo));
        request.getTemplateData().put("keyword2", createTemplateData(status));
        request.getTemplateData().put("keyword3", createTemplateData(time));
        request.getTemplateData().put("remark", createTemplateData("如有疑问，请联系客服。", "#173177"));

        return request;
    }

    /**
     * 构建验证码模板消息
     */
    public static WechatRequest buildVerificationCodeTemplate(String openId, String templateId,
                                                              String code, String expireTime) {
        WechatRequest request = buildTemplateMessage(openId, templateId, null);

        request.getTemplateData().put("first", createTemplateData("您的验证码", "#173177"));
        request.getTemplateData().put("keyword1", createTemplateData(code, "#FF0000"));
        request.getTemplateData().put("keyword2", createTemplateData(expireTime));
        request.getTemplateData().put("remark", createTemplateData("请勿泄露验证码给他人。", "#173177"));

        return request;
    }

    /**
     * 构建预约提醒模板消息
     */
    public static WechatRequest buildAppointmentReminderTemplate(String openId, String templateId,
                                                                 String service, String time, String location, String url) {
        WechatRequest request = buildTemplateMessage(openId, templateId, url);

        request.getTemplateData().put("first", createTemplateData("预约提醒", "#173177"));
        request.getTemplateData().put("keyword1", createTemplateData(service));
        request.getTemplateData().put("keyword2", createTemplateData(time));
        request.getTemplateData().put("keyword3", createTemplateData(location));
        request.getTemplateData().put("remark", createTemplateData("请准时参加，如需取消请提前联系。", "#173177"));

        return request;
    }
}
