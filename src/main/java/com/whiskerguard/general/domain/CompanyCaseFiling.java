package com.whiskerguard.general.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Company case filing entity.
 * Stores case filing information from Tianyancha API.
 *
 * 企业立案信息实体。
 * 存储从天眼查API获取的企业立案信息。
 */
@Entity
@Table(name = "company_case_filing")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class CompanyCaseFiling implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key ID
     *
     * 主键ID
     */
    @Id
    @NotNull
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * Case name
     *
     * 案件名称
     */
    @Size(max = 500)
    @Column(name = "case_name", length = 500)
    private String caseName;

    /**
     * Case number
     *
     * 案件编号
     */
    @Size(max = 100)
    @Column(name = "case_number", length = 100)
    private String caseNumber;

    /**
     * Filing date
     *
     * 立案日期
     */
    @Column(name = "filing_date")
    private Instant filingDate;

    /**
     * Court name
     *
     * 法院名称
     */
    @Size(max = 300)
    @Column(name = "court_name", length = 300)
    private String courtName;

    /**
     * Case type
     *
     * 案件类型
     */
    @Size(max = 100)
    @Column(name = "case_type", length = 100)
    private String caseType;

    /**
     * Case status
     *
     * 案件状态
     */
    @Size(max = 100)
    @Column(name = "case_status", length = 100)
    private String caseStatus;

    /**
     * Full case filing details as JSON string
     *
     * 完整的立案详情，以JSON字符串形式存储
     */
    @Lob
    @Column(name = "details")
    private String details;

    /**
     * Relationship with Company entity
     *
     * 与企业实体的关联关系
     */
    @ManyToOne
    @JsonIgnoreProperties(value = { "contact", "risks", "changeRecords", "dishonestPersons", "caseFilings" }, allowSetters = true)
    private Company company;

    // Constructors - 构造函数

    public CompanyCaseFiling() {}

    // Getters and Setters - getter和setter方法

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public CompanyCaseFiling id(Long id) {
        this.setId(id);
        return this;
    }

    public String getCaseName() {
        return caseName;
    }

    public void setCaseName(String caseName) {
        this.caseName = caseName;
    }

    public CompanyCaseFiling caseName(String caseName) {
        this.setCaseName(caseName);
        return this;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public CompanyCaseFiling caseNumber(String caseNumber) {
        this.setCaseNumber(caseNumber);
        return this;
    }

    public Instant getFilingDate() {
        return filingDate;
    }

    public void setFilingDate(Instant filingDate) {
        this.filingDate = filingDate;
    }

    public CompanyCaseFiling filingDate(Instant filingDate) {
        this.setFilingDate(filingDate);
        return this;
    }

    public String getCourtName() {
        return courtName;
    }

    public void setCourtName(String courtName) {
        this.courtName = courtName;
    }

    public CompanyCaseFiling courtName(String courtName) {
        this.setCourtName(courtName);
        return this;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public CompanyCaseFiling caseType(String caseType) {
        this.setCaseType(caseType);
        return this;
    }

    public String getCaseStatus() {
        return caseStatus;
    }

    public void setCaseStatus(String caseStatus) {
        this.caseStatus = caseStatus;
    }

    public CompanyCaseFiling caseStatus(String caseStatus) {
        this.setCaseStatus(caseStatus);
        return this;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public CompanyCaseFiling details(String details) {
        this.setDetails(details);
        return this;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public CompanyCaseFiling company(Company company) {
        this.setCompany(company);
        return this;
    }

    // equals, hashCode, toString methods - equals、hashCode和toString方法

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CompanyCaseFiling)) {
            return false;
        }
        return id != null && id.equals(((CompanyCaseFiling) o).id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return (
            "CompanyCaseFiling{" +
            "id=" +
            getId() +
            ", caseName='" +
            getCaseName() +
            "'" +
            ", caseNumber='" +
            getCaseNumber() +
            "'" +
            ", filingDate='" +
            getFilingDate() +
            "'" +
            ", courtName='" +
            getCourtName() +
            "'" +
            ", caseType='" +
            getCaseType() +
            "'" +
            ", caseStatus='" +
            getCaseStatus() +
            "'" +
            "}"
        );
    }
}
