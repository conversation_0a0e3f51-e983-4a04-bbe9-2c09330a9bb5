package com.whiskerguard.general.domain;

import com.whiskerguard.general.domain.enumeration.SignatureDocumentStatus;
import com.whiskerguard.general.domain.enumeration.SignatureProvider;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * 存储电子签名文档的相关信息
 */
@Entity
@Table(name = "signature_document")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class SignatureDocument implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 文档标题
     */
    @NotNull
    @Column(name = "title", nullable = false)
    private String title;

    /**
     * 文档描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 文档URL地址
     */
    @NotNull
    @Column(name = "document_url", nullable = false)
    private String documentUrl;

    /**
     * 文档状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SignatureDocumentStatus status;

    /**
     * 签名服务提供商
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "provider", nullable = false)
    private SignatureProvider provider;

    /**
     * 外部系统文档ID
     */
    @Column(name = "external_id")
    private String externalId;

    /**
     * 事务ID
     */
    @Column(name = "transaction_id")
    private String transactionId;

    /**
     * 用户ID
     */
    @NotNull
    @Column(name = "user_id", nullable = false)
    private String userId;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    private Instant expireTime;

    /**
     * 签署时间
     */
    @Column(name = "signed_time")
    private Instant signedTime;

    /**
     * 签署后文档URL
     */
    @Column(name = "signed_document_url")
    private String signedDocumentUrl;

    /**
     * 扩展元数据（JSON格式）
     */
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public SignatureDocument id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return this.title;
    }

    public SignatureDocument title(String title) {
        this.setTitle(title);
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return this.description;
    }

    public SignatureDocument description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDocumentUrl() {
        return this.documentUrl;
    }

    public SignatureDocument documentUrl(String documentUrl) {
        this.setDocumentUrl(documentUrl);
        return this;
    }

    public void setDocumentUrl(String documentUrl) {
        this.documentUrl = documentUrl;
    }

    public SignatureDocumentStatus getStatus() {
        return this.status;
    }

    public SignatureDocument status(SignatureDocumentStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(SignatureDocumentStatus status) {
        this.status = status;
    }

    public SignatureProvider getProvider() {
        return this.provider;
    }

    public SignatureDocument provider(SignatureProvider provider) {
        this.setProvider(provider);
        return this;
    }

    public void setProvider(SignatureProvider provider) {
        this.provider = provider;
    }

    public String getExternalId() {
        return this.externalId;
    }

    public SignatureDocument externalId(String externalId) {
        this.setExternalId(externalId);
        return this;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getTransactionId() {
        return this.transactionId;
    }

    public SignatureDocument transactionId(String transactionId) {
        this.setTransactionId(transactionId);
        return this;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getUserId() {
        return this.userId;
    }

    public SignatureDocument userId(String userId) {
        this.setUserId(userId);
        return this;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Instant getExpireTime() {
        return this.expireTime;
    }

    public SignatureDocument expireTime(Instant expireTime) {
        this.setExpireTime(expireTime);
        return this;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public Instant getSignedTime() {
        return this.signedTime;
    }

    public SignatureDocument signedTime(Instant signedTime) {
        this.setSignedTime(signedTime);
        return this;
    }

    public void setSignedTime(Instant signedTime) {
        this.signedTime = signedTime;
    }

    public String getSignedDocumentUrl() {
        return this.signedDocumentUrl;
    }

    public SignatureDocument signedDocumentUrl(String signedDocumentUrl) {
        this.setSignedDocumentUrl(signedDocumentUrl);
        return this;
    }

    public void setSignedDocumentUrl(String signedDocumentUrl) {
        this.signedDocumentUrl = signedDocumentUrl;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public SignatureDocument metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public SignatureDocument version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public SignatureDocument createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public SignatureDocument createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public SignatureDocument updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public SignatureDocument updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public SignatureDocument isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SignatureDocument)) {
            return false;
        }
        return getId() != null && getId().equals(((SignatureDocument) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "SignatureDocument{" +
            "id=" + getId() +
            ", title='" + getTitle() + "'" +
            ", description='" + getDescription() + "'" +
            ", documentUrl='" + getDocumentUrl() + "'" +
            ", status='" + getStatus() + "'" +
            ", provider='" + getProvider() + "'" +
            ", externalId='" + getExternalId() + "'" +
            ", transactionId='" + getTransactionId() + "'" +
            ", userId='" + getUserId() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", signedTime='" + getSignedTime() + "'" +
            ", signedDocumentUrl='" + getSignedDocumentUrl() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
