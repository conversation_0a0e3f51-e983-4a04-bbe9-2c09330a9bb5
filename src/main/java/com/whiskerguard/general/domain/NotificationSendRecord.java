package com.whiskerguard.general.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.general.domain.enumeration.NotificationType;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.domain.enumeration.SendStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * 存储每个接收者的通知发送详细记录
 */
@Entity
@Table(name = "notification_send_record")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class NotificationSendRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 租户ID（0 = 平台级）
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 接收者ID
     */
    @NotNull
    @Column(name = "recipient_id", nullable = false)
    private Long recipientId;

    /**
     * 接收者类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "recipient_type", nullable = false)
    private RecipientType recipientType;

    /**
     * 发送渠道
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "channel", nullable = false)
    private NotificationType channel;

    /**
     * 发送状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SendStatus status;

    /**
     * 发送时间
     */
    @Column(name = "sent_time")
    private Instant sentTime;

    /**
     * 阅读时间
     */
    @Column(name = "read_time")
    private Instant readTime;

    /**
     * 错误信息
     */
    @Size(max = 1000)
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 第三方服务返回的ID
     */
    @Size(max = 100)
    @Column(name = "external_id", length = 100)
    private String externalId;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 50)
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "template", "sendRecords" }, allowSetters = true)
    private NotificationRecord notification;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public NotificationSendRecord id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public NotificationSendRecord tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getRecipientId() {
        return this.recipientId;
    }

    public NotificationSendRecord recipientId(Long recipientId) {
        this.setRecipientId(recipientId);
        return this;
    }

    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }

    public RecipientType getRecipientType() {
        return this.recipientType;
    }

    public NotificationSendRecord recipientType(RecipientType recipientType) {
        this.setRecipientType(recipientType);
        return this;
    }

    public void setRecipientType(RecipientType recipientType) {
        this.recipientType = recipientType;
    }

    public NotificationType getChannel() {
        return this.channel;
    }

    public NotificationSendRecord channel(NotificationType channel) {
        this.setChannel(channel);
        return this;
    }

    public void setChannel(NotificationType channel) {
        this.channel = channel;
    }

    public SendStatus getStatus() {
        return this.status;
    }

    public NotificationSendRecord status(SendStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(SendStatus status) {
        this.status = status;
    }

    public Instant getSentTime() {
        return this.sentTime;
    }

    public NotificationSendRecord sentTime(Instant sentTime) {
        this.setSentTime(sentTime);
        return this;
    }

    public void setSentTime(Instant sentTime) {
        this.sentTime = sentTime;
    }

    public Instant getReadTime() {
        return this.readTime;
    }

    public NotificationSendRecord readTime(Instant readTime) {
        this.setReadTime(readTime);
        return this;
    }

    public void setReadTime(Instant readTime) {
        this.readTime = readTime;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public NotificationSendRecord errorMessage(String errorMessage) {
        this.setErrorMessage(errorMessage);
        return this;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getExternalId() {
        return this.externalId;
    }

    public NotificationSendRecord externalId(String externalId) {
        this.setExternalId(externalId);
        return this;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public Integer getVersion() {
        return this.version;
    }

    public NotificationSendRecord version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public NotificationSendRecord createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public NotificationSendRecord createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public NotificationSendRecord updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public NotificationSendRecord updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public NotificationSendRecord isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public NotificationRecord getNotification() {
        return this.notification;
    }

    public void setNotification(NotificationRecord notificationRecord) {
        this.notification = notificationRecord;
    }

    public NotificationSendRecord notification(NotificationRecord notificationRecord) {
        this.setNotification(notificationRecord);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof NotificationSendRecord)) {
            return false;
        }
        return getId() != null && getId().equals(((NotificationSendRecord) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "NotificationSendRecord{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", recipientId=" + getRecipientId() +
            ", recipientType='" + getRecipientType() + "'" +
            ", channel='" + getChannel() + "'" +
            ", status='" + getStatus() + "'" +
            ", sentTime='" + getSentTime() + "'" +
            ", readTime='" + getReadTime() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", externalId='" + getExternalId() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
