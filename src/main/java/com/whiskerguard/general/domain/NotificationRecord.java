package com.whiskerguard.general.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationPriority;
import com.whiskerguard.general.domain.enumeration.NotificationScope;
import com.whiskerguard.general.domain.enumeration.NotificationStatus;
import com.whiskerguard.general.domain.enumeration.NotificationSubType;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

/**
 * 存储通知发送记录和状态信息
 */
@Entity
@Table(name = "notification_record")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class NotificationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 租户ID（0 = 平台级）
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 通知分类
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    private NotificationCategory category;

    /**
     * 通知子类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "sub_type", nullable = false)
    private NotificationSubType subType;

    /**
     * 通知范围
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "scope", nullable = false)
    private NotificationScope scope;

    /**
     * 通知标题
     */
    @NotNull
    @Size(max = 200)
    @Column(name = "title", length = 200, nullable = false)
    private String title;

    /**
     * 通知内容
     */
    @Size(max = 2000)
    @Column(name = "content", length = 2000)
    private String content;

    /**
     * 接收者类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "recipient_type", nullable = false)
    private RecipientType recipientType;

    /**
     * 接收者ID列表(JSON格式)
     */
    @Size(max = 2000)
    @Column(name = "recipient_ids", length = 2000)
    private String recipientIds;

    /**
     * 发送渠道列表(JSON格式)
     */
    @Size(max = 500)
    @Column(name = "channels", length = 500)
    private String channels;

    /**
     * 优先级
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "priority", nullable = false)
    private NotificationPriority priority;

    /**
     * 状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private NotificationStatus status;

    /**
     * 计划发送时间
     */
    @Column(name = "scheduled_time")
    private Instant scheduledTime;

    /**
     * 实际发送时间
     */
    @Column(name = "sent_time")
    private Instant sentTime;

    /**
     * 关联业务ID
     */
    @Size(max = 100)
    @Column(name = "business_id", length = 100)
    private String businessId;

    /**
     * 业务类型
     */
    @Size(max = 50)
    @Column(name = "business_type", length = 50)
    private String businessType;

    /**
     * 模板参数(JSON格式)
     */
    @Size(max = 2000)
    @Column(name = "template_params", length = 2000)
    private String templateParams;

    /**
     * 重试次数
     */
    @Min(value = 0)
    @Max(value = 10)
    @Column(name = "retry_count")
    private Integer retryCount;

    /**
     * 错误信息
     */
    @Size(max = 1000)
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 50)
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    private NotificationTemplate template;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "notification")
    @JsonIgnoreProperties(value = { "notification" }, allowSetters = true)
    private Set<NotificationSendRecord> sendRecords = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public NotificationRecord id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public NotificationRecord tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public NotificationCategory getCategory() {
        return this.category;
    }

    public NotificationRecord category(NotificationCategory category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(NotificationCategory category) {
        this.category = category;
    }

    public NotificationSubType getSubType() {
        return this.subType;
    }

    public NotificationRecord subType(NotificationSubType subType) {
        this.setSubType(subType);
        return this;
    }

    public void setSubType(NotificationSubType subType) {
        this.subType = subType;
    }

    public NotificationScope getScope() {
        return this.scope;
    }

    public NotificationRecord scope(NotificationScope scope) {
        this.setScope(scope);
        return this;
    }

    public void setScope(NotificationScope scope) {
        this.scope = scope;
    }

    public String getTitle() {
        return this.title;
    }

    public NotificationRecord title(String title) {
        this.setTitle(title);
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return this.content;
    }

    public NotificationRecord content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public RecipientType getRecipientType() {
        return this.recipientType;
    }

    public NotificationRecord recipientType(RecipientType recipientType) {
        this.setRecipientType(recipientType);
        return this;
    }

    public void setRecipientType(RecipientType recipientType) {
        this.recipientType = recipientType;
    }

    public String getRecipientIds() {
        return this.recipientIds;
    }

    public NotificationRecord recipientIds(String recipientIds) {
        this.setRecipientIds(recipientIds);
        return this;
    }

    public void setRecipientIds(String recipientIds) {
        this.recipientIds = recipientIds;
    }

    public String getChannels() {
        return this.channels;
    }

    public NotificationRecord channels(String channels) {
        this.setChannels(channels);
        return this;
    }

    public void setChannels(String channels) {
        this.channels = channels;
    }

    public NotificationPriority getPriority() {
        return this.priority;
    }

    public NotificationRecord priority(NotificationPriority priority) {
        this.setPriority(priority);
        return this;
    }

    public void setPriority(NotificationPriority priority) {
        this.priority = priority;
    }

    public NotificationStatus getStatus() {
        return this.status;
    }

    public NotificationRecord status(NotificationStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(NotificationStatus status) {
        this.status = status;
    }

    public Instant getScheduledTime() {
        return this.scheduledTime;
    }

    public NotificationRecord scheduledTime(Instant scheduledTime) {
        this.setScheduledTime(scheduledTime);
        return this;
    }

    public void setScheduledTime(Instant scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    public Instant getSentTime() {
        return this.sentTime;
    }

    public NotificationRecord sentTime(Instant sentTime) {
        this.setSentTime(sentTime);
        return this;
    }

    public void setSentTime(Instant sentTime) {
        this.sentTime = sentTime;
    }

    public String getBusinessId() {
        return this.businessId;
    }

    public NotificationRecord businessId(String businessId) {
        this.setBusinessId(businessId);
        return this;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessType() {
        return this.businessType;
    }

    public NotificationRecord businessType(String businessType) {
        this.setBusinessType(businessType);
        return this;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getTemplateParams() {
        return this.templateParams;
    }

    public NotificationRecord templateParams(String templateParams) {
        this.setTemplateParams(templateParams);
        return this;
    }

    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }

    public Integer getRetryCount() {
        return this.retryCount;
    }

    public NotificationRecord retryCount(Integer retryCount) {
        this.setRetryCount(retryCount);
        return this;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public NotificationRecord errorMessage(String errorMessage) {
        this.setErrorMessage(errorMessage);
        return this;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getVersion() {
        return this.version;
    }

    public NotificationRecord version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public NotificationRecord createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public NotificationRecord createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public NotificationRecord updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public NotificationRecord updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public NotificationRecord isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public NotificationTemplate getTemplate() {
        return this.template;
    }

    public void setTemplate(NotificationTemplate notificationTemplate) {
        this.template = notificationTemplate;
    }

    public NotificationRecord template(NotificationTemplate notificationTemplate) {
        this.setTemplate(notificationTemplate);
        return this;
    }

    public Set<NotificationSendRecord> getSendRecords() {
        return this.sendRecords;
    }

    public void setSendRecords(Set<NotificationSendRecord> notificationSendRecords) {
        if (this.sendRecords != null) {
            this.sendRecords.forEach(i -> i.setNotification(null));
        }
        if (notificationSendRecords != null) {
            notificationSendRecords.forEach(i -> i.setNotification(this));
        }
        this.sendRecords = notificationSendRecords;
    }

    public NotificationRecord sendRecords(Set<NotificationSendRecord> notificationSendRecords) {
        this.setSendRecords(notificationSendRecords);
        return this;
    }

    public NotificationRecord addSendRecords(NotificationSendRecord notificationSendRecord) {
        this.sendRecords.add(notificationSendRecord);
        notificationSendRecord.setNotification(this);
        return this;
    }

    public NotificationRecord removeSendRecords(NotificationSendRecord notificationSendRecord) {
        this.sendRecords.remove(notificationSendRecord);
        notificationSendRecord.setNotification(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof NotificationRecord)) {
            return false;
        }
        return getId() != null && getId().equals(((NotificationRecord) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "NotificationRecord{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", category='" + getCategory() + "'" +
            ", subType='" + getSubType() + "'" +
            ", scope='" + getScope() + "'" +
            ", title='" + getTitle() + "'" +
            ", content='" + getContent() + "'" +
            ", recipientType='" + getRecipientType() + "'" +
            ", recipientIds='" + getRecipientIds() + "'" +
            ", channels='" + getChannels() + "'" +
            ", priority='" + getPriority() + "'" +
            ", status='" + getStatus() + "'" +
            ", scheduledTime='" + getScheduledTime() + "'" +
            ", sentTime='" + getSentTime() + "'" +
            ", businessId='" + getBusinessId() + "'" +
            ", businessType='" + getBusinessType() + "'" +
            ", templateParams='" + getTemplateParams() + "'" +
            ", retryCount=" + getRetryCount() +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
