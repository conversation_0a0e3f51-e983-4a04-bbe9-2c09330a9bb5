package com.whiskerguard.general.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Company dishonest person entity.
 * Stores dishonest person records from Tianyancha API.
 *
 * 企业失信人实体。
 * 存储从天眼查API获取的失信人记录。
 */
@Entity
@Table(name = "company_dishonest_person")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class CompanyDishonestPerson implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key ID
     *
     * 主键ID
     */
    @Id
    @NotNull
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * Name of dishonest person
     *
     * 失信人姓名
     */
    @Size(max = 200)
    @Column(name = "person_name", length = 200)
    private String personName;

    /**
     * Case number
     *
     * 案件编号
     */
    @Size(max = 100)
    @Column(name = "case_number", length = 100)
    private String caseNumber;

    /**
     * Publish date
     *
     * 发布日期
     */
    @Column(name = "publish_date")
    private Instant publishDate;

    /**
     * Court name
     *
     * 法院名称
     */
    @Size(max = 300)
    @Column(name = "court_name", length = 300)
    private String courtName;

    /**
     * Execution status
     *
     * 执行状态
     */
    @Size(max = 100)
    @Column(name = "execution_status", length = 100)
    private String executionStatus;

    /**
     * Full dishonest person details as JSON string
     *
     * 完整的失信人详情，以JSON字符串形式存储
     */
    @Lob
    @Column(name = "details")
    private String details;

    /**
     * Relationship with Company entity
     *
     * 与企业实体的关联关系
     */
    @ManyToOne
    @JsonIgnoreProperties(value = { "contact", "risks", "changeRecords", "dishonestPersons", "caseFilings" }, allowSetters = true)
    private Company company;

    // Constructors - 构造函数

    public CompanyDishonestPerson() {}

    // Getters and Setters - getter和setter方法

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public CompanyDishonestPerson id(Long id) {
        this.setId(id);
        return this;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public CompanyDishonestPerson personName(String personName) {
        this.setPersonName(personName);
        return this;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public CompanyDishonestPerson caseNumber(String caseNumber) {
        this.setCaseNumber(caseNumber);
        return this;
    }

    public Instant getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(Instant publishDate) {
        this.publishDate = publishDate;
    }

    public CompanyDishonestPerson publishDate(Instant publishDate) {
        this.setPublishDate(publishDate);
        return this;
    }

    public String getCourtName() {
        return courtName;
    }

    public void setCourtName(String courtName) {
        this.courtName = courtName;
    }

    public CompanyDishonestPerson courtName(String courtName) {
        this.setCourtName(courtName);
        return this;
    }

    public String getExecutionStatus() {
        return executionStatus;
    }

    public void setExecutionStatus(String executionStatus) {
        this.executionStatus = executionStatus;
    }

    public CompanyDishonestPerson executionStatus(String executionStatus) {
        this.setExecutionStatus(executionStatus);
        return this;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public CompanyDishonestPerson details(String details) {
        this.setDetails(details);
        return this;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public CompanyDishonestPerson company(Company company) {
        this.setCompany(company);
        return this;
    }

    // equals, hashCode, toString methods - equals、hashCode和toString方法

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CompanyDishonestPerson)) {
            return false;
        }
        return id != null && id.equals(((CompanyDishonestPerson) o).id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return (
            "CompanyDishonestPerson{" +
            "id=" +
            getId() +
            ", personName='" +
            getPersonName() +
            "'" +
            ", caseNumber='" +
            getCaseNumber() +
            "'" +
            ", publishDate='" +
            getPublishDate() +
            "'" +
            ", courtName='" +
            getCourtName() +
            "'" +
            ", executionStatus='" +
            getExecutionStatus() +
            "'" +
            "}"
        );
    }
}
