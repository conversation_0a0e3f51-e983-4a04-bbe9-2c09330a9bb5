package com.whiskerguard.general.domain;

import com.whiskerguard.general.domain.enumeration.LanguageType;
import com.whiskerguard.general.domain.enumeration.SensitiveCategory;
import com.whiskerguard.general.domain.enumeration.SeverityType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * 存储平台级或租户级的敏感词及策略信息
 */
@Entity
@Table(name = "sensitive_word")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class SensitiveWord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 租户ID（0 = 平台级）
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 敏感词条
     */
    @NotNull
    @Column(name = "term", nullable = false)
    private String term;

    /**
     * 语言
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "lang", nullable = false)
    private LanguageType lang;

    /**
     * 分类
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    private SensitiveCategory category;

    /**
     * 严重级别
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "severity", nullable = false)
    private SeverityType severity;

    /**
     * 有效开始时间
     */
    @NotNull
    @Column(name = "valid_from", nullable = false)
    private Instant validFrom;

    /**
     * 有效结束时间
     */
    @NotNull
    @Column(name = "valid_to", nullable = false)
    private Instant validTo;

    /**
     * 备注
     */
    @Column(name = "notes")
    private String notes;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public SensitiveWord id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public SensitiveWord tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getTerm() {
        return this.term;
    }

    public SensitiveWord term(String term) {
        this.setTerm(term);
        return this;
    }

    public void setTerm(String term) {
        this.term = term;
    }

    public LanguageType getLang() {
        return this.lang;
    }

    public SensitiveWord lang(LanguageType lang) {
        this.setLang(lang);
        return this;
    }

    public void setLang(LanguageType lang) {
        this.lang = lang;
    }

    public SensitiveCategory getCategory() {
        return this.category;
    }

    public SensitiveWord category(SensitiveCategory category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(SensitiveCategory category) {
        this.category = category;
    }

    public SeverityType getSeverity() {
        return this.severity;
    }

    public SensitiveWord severity(SeverityType severity) {
        this.setSeverity(severity);
        return this;
    }

    public void setSeverity(SeverityType severity) {
        this.severity = severity;
    }

    public Instant getValidFrom() {
        return this.validFrom;
    }

    public SensitiveWord validFrom(Instant validFrom) {
        this.setValidFrom(validFrom);
        return this;
    }

    public void setValidFrom(Instant validFrom) {
        this.validFrom = validFrom;
    }

    public Instant getValidTo() {
        return this.validTo;
    }

    public SensitiveWord validTo(Instant validTo) {
        this.setValidTo(validTo);
        return this;
    }

    public void setValidTo(Instant validTo) {
        this.validTo = validTo;
    }

    public String getNotes() {
        return this.notes;
    }

    public SensitiveWord notes(String notes) {
        this.setNotes(notes);
        return this;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Integer getVersion() {
        return this.version;
    }

    public SensitiveWord version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public SensitiveWord createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public SensitiveWord createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public SensitiveWord updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public SensitiveWord updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public SensitiveWord isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SensitiveWord)) {
            return false;
        }
        return getId() != null && getId().equals(((SensitiveWord) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "SensitiveWord{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", term='" + getTerm() + "'" +
            ", lang='" + getLang() + "'" +
            ", category='" + getCategory() + "'" +
            ", severity='" + getSeverity() + "'" +
            ", validFrom='" + getValidFrom() + "'" +
            ", validTo='" + getValidTo() + "'" +
            ", notes='" + getNotes() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
