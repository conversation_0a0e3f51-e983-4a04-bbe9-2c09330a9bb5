package com.whiskerguard.general.domain;

import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationSubType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * 存储通知模板配置信息
 */
@Entity
@Table(name = "notification_template")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class NotificationTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 租户ID（0 = 平台级）
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 模板编码
     */
    @NotNull
    @Size(max = 100)
    @Column(name = "code", length = 100, nullable = false, unique = true)
    private String code;

    /**
     * 模板名称
     */
    @NotNull
    @Size(max = 200)
    @Column(name = "name", length = 200, nullable = false)
    private String name;

    /**
     * 通知分类
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    private NotificationCategory category;

    /**
     * 通知子类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "sub_type", nullable = false)
    private NotificationSubType subType;

    /**
     * 标题模板
     */
    @Size(max = 500)
    @Column(name = "title_template", length = 500)
    private String titleTemplate;

    /**
     * 内容模板
     */
    @Size(max = 2000)
    @Column(name = "content_template", length = 2000)
    private String contentTemplate;

    /**
     * 短信模板
     */
    @Size(max = 500)
    @Column(name = "sms_template", length = 500)
    private String smsTemplate;

    /**
     * 邮件模板
     */
    @Size(max = 2000)
    @Column(name = "email_template", length = 2000)
    private String emailTemplate;

    /**
     * 推送模板
     */
    @Size(max = 500)
    @Column(name = "push_template", length = 500)
    private String pushTemplate;

    /**
     * 支持的渠道(JSON格式)
     */
    @Size(max = 200)
    @Column(name = "supported_channels", length = 200)
    private String supportedChannels;

    /**
     * 默认渠道(JSON格式)
     */
    @Size(max = 200)
    @Column(name = "default_channels", length = 200)
    private String defaultChannels;

    /**
     * 是否启用
     */
    @NotNull
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    /**
     * 语言
     */
    @Size(max = 10)
    @Column(name = "language", length = 10)
    private String language;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 50)
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public NotificationTemplate id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public NotificationTemplate tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getCode() {
        return this.code;
    }

    public NotificationTemplate code(String code) {
        this.setCode(code);
        return this;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public NotificationTemplate name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public NotificationCategory getCategory() {
        return this.category;
    }

    public NotificationTemplate category(NotificationCategory category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(NotificationCategory category) {
        this.category = category;
    }

    public NotificationSubType getSubType() {
        return this.subType;
    }

    public NotificationTemplate subType(NotificationSubType subType) {
        this.setSubType(subType);
        return this;
    }

    public void setSubType(NotificationSubType subType) {
        this.subType = subType;
    }

    public String getTitleTemplate() {
        return this.titleTemplate;
    }

    public NotificationTemplate titleTemplate(String titleTemplate) {
        this.setTitleTemplate(titleTemplate);
        return this;
    }

    public void setTitleTemplate(String titleTemplate) {
        this.titleTemplate = titleTemplate;
    }

    public String getContentTemplate() {
        return this.contentTemplate;
    }

    public NotificationTemplate contentTemplate(String contentTemplate) {
        this.setContentTemplate(contentTemplate);
        return this;
    }

    public void setContentTemplate(String contentTemplate) {
        this.contentTemplate = contentTemplate;
    }

    public String getSmsTemplate() {
        return this.smsTemplate;
    }

    public NotificationTemplate smsTemplate(String smsTemplate) {
        this.setSmsTemplate(smsTemplate);
        return this;
    }

    public void setSmsTemplate(String smsTemplate) {
        this.smsTemplate = smsTemplate;
    }

    public String getEmailTemplate() {
        return this.emailTemplate;
    }

    public NotificationTemplate emailTemplate(String emailTemplate) {
        this.setEmailTemplate(emailTemplate);
        return this;
    }

    public void setEmailTemplate(String emailTemplate) {
        this.emailTemplate = emailTemplate;
    }

    public String getPushTemplate() {
        return this.pushTemplate;
    }

    public NotificationTemplate pushTemplate(String pushTemplate) {
        this.setPushTemplate(pushTemplate);
        return this;
    }

    public void setPushTemplate(String pushTemplate) {
        this.pushTemplate = pushTemplate;
    }

    public String getSupportedChannels() {
        return this.supportedChannels;
    }

    public NotificationTemplate supportedChannels(String supportedChannels) {
        this.setSupportedChannels(supportedChannels);
        return this;
    }

    public void setSupportedChannels(String supportedChannels) {
        this.supportedChannels = supportedChannels;
    }

    public String getDefaultChannels() {
        return this.defaultChannels;
    }

    public NotificationTemplate defaultChannels(String defaultChannels) {
        this.setDefaultChannels(defaultChannels);
        return this;
    }

    public void setDefaultChannels(String defaultChannels) {
        this.defaultChannels = defaultChannels;
    }

    public Boolean getEnabled() {
        return this.enabled;
    }

    public NotificationTemplate enabled(Boolean enabled) {
        this.setEnabled(enabled);
        return this;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getLanguage() {
        return this.language;
    }

    public NotificationTemplate language(String language) {
        this.setLanguage(language);
        return this;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getVersion() {
        return this.version;
    }

    public NotificationTemplate version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public NotificationTemplate createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public NotificationTemplate createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public NotificationTemplate updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public NotificationTemplate updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public NotificationTemplate isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof NotificationTemplate)) {
            return false;
        }
        return getId() != null && getId().equals(((NotificationTemplate) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "NotificationTemplate{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", code='" + getCode() + "'" +
            ", name='" + getName() + "'" +
            ", category='" + getCategory() + "'" +
            ", subType='" + getSubType() + "'" +
            ", titleTemplate='" + getTitleTemplate() + "'" +
            ", contentTemplate='" + getContentTemplate() + "'" +
            ", smsTemplate='" + getSmsTemplate() + "'" +
            ", emailTemplate='" + getEmailTemplate() + "'" +
            ", pushTemplate='" + getPushTemplate() + "'" +
            ", supportedChannels='" + getSupportedChannels() + "'" +
            ", defaultChannels='" + getDefaultChannels() + "'" +
            ", enabled='" + getEnabled() + "'" +
            ", language='" + getLanguage() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
