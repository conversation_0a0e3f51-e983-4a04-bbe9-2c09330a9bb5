package com.whiskerguard.general.domain.enumeration;

/**
 * 表示签名文档的不同状态
 */
public enum SignatureDocumentStatus {
    /**
     * 文档已创建但未发送签名
     */
    CREATED,
    /**
     * 文档已发送签名，等待签署
     */
    PENDING,
    /**
     * 文档已成功签署
     */
    SIGNED,
    /**
     * 文档签署被拒绝
     */
    REJECTED,
    /**
     * 签署过程中出现技术故障
     */
    FAILED,
    /**
     * 签署已过期
     */
    EXPIRED,
    /**
     * 签署已被取消
     */
    CANCELLED,
}
