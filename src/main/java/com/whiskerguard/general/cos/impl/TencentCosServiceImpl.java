/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-general-service
 * 文件名称：TencentCosServiceImpl.java
 * 包    名：com.whiskerguard.general.cos.impl
 * 描    述：猫伯伯合规管家公共微服务：提供通用工具类和帮助模块、云对象存储（COS）服务、分布式配置与安全组件、消息和事件驱动服务、缓存与分布式协同、国际化、多语言支持、公共 API 响应与文档功能
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/4/15
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.general.cos.impl;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.endpoint.EndpointBuilder;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.whiskerguard.general.cos.CosProperties;
import com.whiskerguard.general.cos.CosService;
import com.whiskerguard.general.service.FileUploadService;
import com.whiskerguard.general.service.dto.FileOperationRequestDTO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.imageio.ImageIO;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hslf.usermodel.HSLFSlide;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.hslf.usermodel.HSLFTextBox;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.sl.usermodel.SlideShow;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTextShape;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 基于腾讯云 COS 实现的 CosService 接口
 */
@Service
public class TencentCosServiceImpl implements CosService {

    private static final Logger logger = LoggerFactory.getLogger(TencentCosServiceImpl.class);

    @Autowired
    private CosProperties cosProperties;

    private COSClient cosClient;

    /**
     * 初始化 COSClient 对象
     */
    @PostConstruct
    public void init() {
        try {
            BasicCOSCredentials credentials = new BasicCOSCredentials(cosProperties.getSecretId(), cosProperties.getSecretKey());
            ClientConfig clientConfig = new ClientConfig(new Region(cosProperties.getRegion()));
            clientConfig.setHttpProtocol(HttpProtocol.https);
            this.cosClient = new COSClient(credentials, clientConfig);
            logger.info("初始化腾讯云 COSClient 成功，Bucket: {}", cosProperties.getBucketName());
        } catch (Exception e) {
            logger.error("初始化腾讯云 COSClient 失败", e);
            throw new RuntimeException("腾讯云 COSClient 初始化失败", e);
        }
    }

    /**
     * 关闭 COSClient，释放资源
     */
    @PreDestroy
    public void shutdown() {
        if (cosClient != null) {
            cosClient.shutdown();
            logger.info("COSClient 已关闭");
        }
    }

    @Override
    public String upload(File file, String key) throws Exception {
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucketName(), key, file);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentDisposition("inline");
            putObjectRequest.setMetadata(metadata);
            PutObjectResult result = cosClient.putObject(putObjectRequest);
            if (result != null) {
                String url = cosProperties.getDomain() + "/" + key;
                logger.info("文件上传成功，URL: {}", url);
                return url;
            } else {
                logger.error("文件上传失败，result 为 null");
                throw new Exception("文件上传失败，返回结果为空");
            }
        } catch (Exception e) {
            logger.error("文件上传失败，key: {}, error: {}", key, e.getMessage());
            throw e;
        }
    }

    @Override
    public String generatePresignedUrl(String key) throws Exception {
        try {
            // 重新创建一个 COSClient，用于生成预签名 URL
            BasicCOSCredentials credentials = new BasicCOSCredentials(cosProperties.getSecretId(), cosProperties.getSecretKey());
            ClientConfig clientConfig = new ClientConfig(new Region(cosProperties.getRegion()));
            clientConfig.setHttpProtocol(HttpProtocol.https);
            clientConfig.setEndpointBuilder(
                new EndpointBuilder() {
                    @Override
                    public String buildGeneralApiEndpoint(String bucketName) {
                        return cosProperties.getDomain();
                    }

                    @Override
                    public String buildGetServiceApiEndpoint() {
                        return cosProperties.getDomain();
                    }
                }
            );
            COSClient cosClient = new COSClient(credentials, clientConfig);

            // 设置 URL 的有效期为 12小时
            Date expiration = new Date(System.currentTimeMillis() + TimeUnit.HOURS.toMillis(12));
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(cosProperties.getBucketName(), key);
            request.setMethod(com.qcloud.cos.http.HttpMethodName.GET);
            request.setExpiration(expiration);
            ResponseHeaderOverrides headerOverrides = new ResponseHeaderOverrides();
            headerOverrides.setContentDisposition("inline");
            request.setResponseHeaders(headerOverrides);

            URL url = cosClient.generatePresignedUrl(request);
            if (url != null) {
                logger.info("生成文件临时访问链接成功，URL: {}", url);
                return url.toString();
            } else {
                logger.error("生成文件临时访问链接失败，url 为空");
                throw new Exception("文件临时访问链接生成失败，结果为空");
            }
        } catch (Exception e) {
            logger.error("生成临时访问链接失败，key: {}, error: {}", key, e.getMessage());
            throw e;
        }
    }

    @Override
    public boolean delete(String key) throws Exception {
        try {
            DeleteObjectRequest deleteRequest = new DeleteObjectRequest(cosProperties.getBucketName(), key);
            cosClient.deleteObject(deleteRequest);
            // 腾讯云 COS SDK 删除成功后不会抛出异常，这里以日志为准
            logger.info("文件删除成功，key: {}", key);
            return true;
        } catch (Exception e) {
            logger.error("删除文件失败，key: {}, error: {}", key, e.getMessage());
            throw e;
        }
    }

    @Override
    public List<String> listDirectoryFiles(String directoryPath) {
        try {
            // 获取指定目录下的文件列表
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
            listObjectsRequest.setBucketName(cosProperties.getBucketName());
            listObjectsRequest.setPrefix(directoryPath);
            ObjectListing objectListing = cosClient.listObjects(listObjectsRequest);
            List<COSObjectSummary> objectSummaries = objectListing.getObjectSummaries();
            // 提取文件名称并返回
            List<String> fileNames = objectSummaries.stream().map(COSObjectSummary::getKey).collect(Collectors.toList());
            logger.info("获取目录下的文件列表成功，文件数量: {}", fileNames.size());
            return fileNames;
        } catch (Exception e) {
            logger.error("获取目录下的文件列表失败，error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    @Override
    public String readFileContent(FileOperationRequestDTO request) {
        try {
            String cosKeys = request.getCosFileName(); //这里是获取的 COS上面的 Key
            Long tenantId = request.getTenantId();

            // 分割多个文件Key
            String[] keyArray = cosKeys.split(",");
            StringBuilder combinedContent = new StringBuilder();

            // 定义文件内容分隔符
            final String CONTENT_SEPARATOR = "----------------------------------------------------";

            // 处理每一个文件
            for (int i = 0; i < keyArray.length; i++) {
                String cosKey = keyArray[i].trim();

                // 创建针对单个文件的请求对象
                FileOperationRequestDTO singleFileRequest = new FileOperationRequestDTO();
                singleFileRequest.setCosFileName(cosKey);
                singleFileRequest.setTenantId(tenantId);

                // 检查文件是否存在
                if (!checkFileExists(singleFileRequest)) {
                    logger.error("文件不存在，无法读取内容: {}", cosKey);
                    combinedContent.append("文件不存在: ").append(cosKey);
                } else {
                    // 获取文件的输入流
                    GetObjectRequest getObjectRequest = new GetObjectRequest(cosProperties.getBucketName(), cosKey);
                    COSObject cosObject = cosClient.getObject(getObjectRequest);

                    // 获取文件扩展名以判断文件类型
                    String fileExtension = "";
                    int lastDotIndex = cosKey.lastIndexOf('.');
                    if (lastDotIndex > 0) {
                        fileExtension = cosKey.substring(lastDotIndex + 1).toLowerCase();
                    }

                    // 根据文件类型处理文件内容
                    try (InputStream inputStream = cosObject.getObjectContent()) {
                        String fileContent;

                        // 文本类文件直接读取
                        if (
                            "txt".equals(fileExtension) ||
                            "json".equals(fileExtension) ||
                            "xml".equals(fileExtension) ||
                            "html".equals(fileExtension) ||
                            "csv".equals(fileExtension)
                        ) {
                            fileContent = readTextFile(inputStream);
                        }
                        // PDF文件
                        else if ("pdf".equals(fileExtension)) {
                            fileContent = extractTextFromPdf(inputStream);
                        }
                        // Word文档（DOC格式）
                        else if ("doc".equals(fileExtension)) {
                            fileContent = extractTextFromDoc(inputStream);
                        }
                        // Word文档（DOCX格式）
                        else if ("docx".equals(fileExtension)) {
                            fileContent = extractTextFromDocx(inputStream);
                        }
                        // WPS文件（与DOC类似处理）
                        else if ("wps".equals(fileExtension)) {
                            fileContent = extractTextFromDoc(inputStream);
                        }
                        // Excel文件
                        else if ("xls".equals(fileExtension)) {
                            fileContent = extractTextFromXls(inputStream);
                        }
                        // Excel文件（XLSX格式）
                        else if ("xlsx".equals(fileExtension)) {
                            fileContent = extractTextFromXlsx(inputStream);
                        }
                        // PowerPoint文件
                        else if ("ppt".equals(fileExtension)) {
                            fileContent = extractTextFromPpt(inputStream);
                        }
                        // PowerPoint文件（PPTX格式）
                        else if ("pptx".equals(fileExtension)) {
                            fileContent = extractTextFromPptx(inputStream);
                        }
                        // 常见图片格式
                        else if (isImageFile(fileExtension)) {
                            fileContent = extractTextFromImage(inputStream, fileExtension);
                        }
                        // 其他类型文件
                        else {
                            fileContent = "不支持的文件类型: " + fileExtension;
                            logger.warn("文件类型不支持: {}", fileExtension);
                        }

                        // 添加文件名标题
                        combinedContent.append("文件: ").append(cosKey).append("\n\n");
                        combinedContent.append(fileContent);
                    }
                }

                // 如果不是最后一个文件，添加分隔符
                if (i < keyArray.length - 1) {
                    combinedContent.append("\n\n").append(CONTENT_SEPARATOR).append("\n\n");
                }
            }

            return combinedContent.toString();
        } catch (Exception e) {
            logger.error("读取文件内容失败, keys: {}, error: {}", request.getCosFileName(), e.getMessage(), e);
            return "读取文件内容失败: " + e.getMessage();
        }
    }

    @Override
    public Boolean checkFileExists(FileOperationRequestDTO request) {
        try {
            String cosFileName = request.getCosFileName();
            return cosClient.doesObjectExist(cosProperties.getBucketName(), cosFileName);
        } catch (Exception e) {
            logger.error("检查文件是否存在失败, key: {}, error: {}", request.getCosFileName(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getFileInfo(FileOperationRequestDTO request) {
        try {
            String cosFileName = request.getCosFileName();

            // 检查文件是否存在
            if (!checkFileExists(request)) {
                logger.error("文件不存在，无法获取信息: {}", cosFileName);
                return "{\"error\": \"文件不存在\"}";
            }

            // 获取文件元数据
            GetObjectMetadataRequest metadataRequest = new GetObjectMetadataRequest(cosProperties.getBucketName(), cosFileName);
            ObjectMetadata metadata = cosClient.getObjectMetadata(metadataRequest);

            // 构建JSON响应（使用简单的字符串拼接，避免依赖外部库）
            StringBuilder fileInfoJson = new StringBuilder();
            fileInfoJson.append("{");
            fileInfoJson.append("\"fileName\":\"").append(cosFileName.substring(cosFileName.lastIndexOf('/') + 1)).append("\",");
            fileInfoJson.append("\"filePath\":\"").append(cosFileName).append("\",");
            fileInfoJson.append("\"contentType\":\"").append(metadata.getContentType()).append("\",");
            fileInfoJson.append("\"contentLength\":").append(metadata.getContentLength()).append(",");

            // 格式化最后修改时间
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            fileInfoJson.append("\"lastModified\":\"").append(dateFormat.format(metadata.getLastModified())).append("\"");

            // 获取其他元数据
            Map<String, String> userMetadata = metadata.getUserMetadata();
            if (userMetadata != null && !userMetadata.isEmpty()) {
                fileInfoJson.append(",\"userMetadata\":{");
                Iterator<Map.Entry<String, String>> iterator = userMetadata.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, String> entry = iterator.next();
                    fileInfoJson.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\"");
                    if (iterator.hasNext()) {
                        fileInfoJson.append(",");
                    }
                }
                fileInfoJson.append("}");
            }

            fileInfoJson.append("}");
            return fileInfoJson.toString();
        } catch (Exception e) {
            logger.error("获取文件信息失败, key: {}, error: {}", request.getCosFileName(), e.getMessage(), e);
            return "{\"error\": \"" + e.getMessage().replace("\"", "'") + "\"}";
        }
    }

    // 将Map转换为JSON字符串的辅助方法
    private String toJsonString(Map<String, Object> map) {
        StringBuilder json = new StringBuilder("{");
        Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            json.append("\"").append(entry.getKey()).append("\":");

            Object value = entry.getValue();
            if (value instanceof String) {
                json.append("\"").append(value).append("\"");
            } else if (value instanceof Map) {
                json.append(toJsonString((Map<String, Object>) value));
            } else {
                json.append(value);
            }

            if (iterator.hasNext()) {
                json.append(",");
            }
        }
        json.append("}");
        return json.toString();
    }

    // 辅助方法: 读取文本文件内容
    private String readTextFile(InputStream inputStream) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            return content.toString();
        }
    }

    // 辅助方法: 从PDF提取文本
    private String extractTextFromPdf(InputStream inputStream) throws IOException {
        try (PDDocument document = PDDocument.load(inputStream)) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    // 辅助方法: 从Word文档提取文本
    private String extractTextFromDoc(InputStream inputStream) throws IOException {
        StringBuilder text = new StringBuilder();
        try {
            HWPFDocument wordDoc = new HWPFDocument(inputStream);
            WordExtractor extractor = new WordExtractor(wordDoc);
            String[] paragraphs = extractor.getParagraphText();
            for (String paragraph : paragraphs) {
                text.append(paragraph.trim()).append("\n");
            }
        } catch (Exception e) {
            logger.error("提取Word文档文本失败: {}", e.getMessage());
        }
        return text.toString();
    }

    // 辅助方法: 从Word文档提取文本（针对DOCX格式）
    private String extractTextFromDocx(InputStream inputStream) throws IOException {
        try (XWPFDocument document = new XWPFDocument(inputStream)) {
            StringBuilder text = new StringBuilder();
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                text.append(paragraph.getText());
                text.append("\n");
            }
            return text.toString();
        }
    }

    // 辅助方法: 从Excel文件提取文本
    private String extractTextFromXls(InputStream inputStream) throws IOException {
        StringBuilder text = new StringBuilder();
        try (HSSFWorkbook workbook = new HSSFWorkbook(inputStream)) {
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                for (Row row : sheet) {
                    for (Cell cell : row) {
                        switch (cell.getCellType()) {
                            case STRING:
                                text.append(cell.getStringCellValue()).append("\t");
                                break;
                            case NUMERIC:
                                text.append(cell.getNumericCellValue()).append("\t");
                                break;
                            case BOOLEAN:
                                text.append(cell.getBooleanCellValue()).append("\t");
                                break;
                            default:
                                break;
                        }
                    }
                    text.append("\n");
                }
            }
        } catch (Exception e) {
            logger.error("提取Excel文件文本失败: {}", e.getMessage());
        }
        return text.toString();
    }

    // 辅助方法: 从Excel文件提取文本（针对XLSX格式）
    private String extractTextFromXlsx(InputStream inputStream) throws IOException {
        StringBuilder text = new StringBuilder();
        try (XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(i);
                for (org.apache.poi.ss.usermodel.Row row : sheet) {
                    for (org.apache.poi.ss.usermodel.Cell cell : row) {
                        switch (cell.getCellType()) {
                            case STRING:
                                text.append(cell.getStringCellValue()).append("\t");
                                break;
                            case NUMERIC:
                                text.append(cell.getNumericCellValue()).append("\t");
                                break;
                            case BOOLEAN:
                                text.append(cell.getBooleanCellValue()).append("\t");
                                break;
                            default:
                                break;
                        }
                    }
                    text.append("\n");
                }
            }
        } catch (Exception e) {
            logger.error("提取Excel文件文本失败: {}", e.getMessage());
        }
        return text.toString();
    }

    // 辅助方法: 从PowerPoint文件提取文本
    private String extractTextFromPpt(InputStream inputStream) throws IOException {
        StringBuilder text = new StringBuilder();
        try (HSLFSlideShow ppt = new HSLFSlideShow(inputStream)) {
            for (HSLFSlide slide : ppt.getSlides()) {
                if (slide.getTitle() != null) {
                    text.append(slide.getTitle()).append("\n");
                }
                // 获取幻灯片中的所有文本内容
                slide
                    .getShapes()
                    .forEach(shape -> {
                        if (shape instanceof HSLFTextBox) {
                            HSLFTextBox textBox = (HSLFTextBox) shape;
                            text.append(textBox.getText()).append("\n");
                        }
                    });
                text.append("\n");
            }
        } catch (Exception e) {
            logger.error("提取PowerPoint文件文本失败: {}", e.getMessage());
        }
        return text.toString();
    }

    // 辅助方法: 从PowerPoint文件提取文本（针对PPTX格式）
    private String extractTextFromPptx(InputStream inputStream) throws IOException {
        StringBuilder text = new StringBuilder();
        try (XMLSlideShow pptx = new XMLSlideShow(inputStream)) {
            for (XSLFSlide slide : pptx.getSlides()) {
                // 获取标题
                if (slide.getTitle() != null) {
                    text.append(slide.getTitle()).append("\n");
                }

                // 获取内容（遍历所有文本框和形状中的文本）
                slide
                    .getShapes()
                    .forEach(shape -> {
                        if (shape instanceof XSLFTextShape) {
                            XSLFTextShape textShape = (XSLFTextShape) shape;
                            text.append(textShape.getText()).append("\n");
                        }
                    });
                text.append("\n");
            }
        } catch (Exception e) {
            logger.error("提取PowerPoint文件文本失败: {}", e.getMessage());
        }
        return text.toString();
    }

    // 辅助方法: 从图像文件提取文本（使用OCR识别）
    private String extractTextFromImage(InputStream inputStream, String fileExtension) throws IOException {
        StringBuilder text = new StringBuilder();
        try {
            // 读取图像文件
            BufferedImage image = ImageIO.read(inputStream);
            if (image != null) {
                // 获取图像基本信息
                text.append("图像文件信息:\n");
                text.append("宽度: ").append(image.getWidth()).append("px\n");
                text.append("高度: ").append(image.getHeight()).append("px\n");
                text.append("类型: ").append(image.getType()).append("\n");
                text.append("注意: 当前版本不支持OCR文字识别，如需识别图像中的文字，请使用专门的OCR服务。\n");
            } else {
                text.append("无法读取图像文件");
            }
        } catch (Exception e) {
            logger.error("提取图像信息失败: {}", e.getMessage());
            text.append("图像文件处理失败: ").append(e.getMessage());
        }
        return text.toString();
    }

    // 辅助方法：判断是否为图片文件
    private boolean isImageFile(String fileExtension) {
        if (fileExtension == null || fileExtension.isEmpty()) {
            return false;
        }
        // 支持的图像文件格式列表
        Set<String> imageExtensions = new HashSet<>(
            Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "tif", "heic", "raw", "svg")
        );
        return imageExtensions.contains(fileExtension.toLowerCase());
    }
}
