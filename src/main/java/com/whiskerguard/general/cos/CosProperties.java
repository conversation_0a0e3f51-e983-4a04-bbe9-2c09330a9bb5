/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-common-service
 * 文件名称：null.java
 * 包    名：com.whiskerguard.common.cos
 * 描    述：猫伯伯合规管家公共微服务：提供通用工具类和帮助模块、云对象存储（COS）服务、分布式配置与安全组件、消息和事件驱动服务、缓存与分布式协同、国际化、多语言支持、公共 API 响应与文档功能
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/4/15
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.general.cos;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 腾讯云 COS 配置属性类
 */
@Component
@ConfigurationProperties(prefix = "cos.tencent")
public class CosProperties {

    /**
     * 腾讯云 COS 的 SecretId
     */
    private String secretId;

    /**
     * 腾讯云 COS 的 SecretKey
     */
    private String secretKey;

    /**
     * COS 所在地域，例如 ap-guangzhou
     */
    private String region;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 自定义域名，用于生成文件访问地址
     */
    private String domain;

    // Getters and Setters

    public String getSecretId() {
        return secretId;
    }

    public void setSecretId(String secretId) {
        this.secretId = secretId;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }
}
