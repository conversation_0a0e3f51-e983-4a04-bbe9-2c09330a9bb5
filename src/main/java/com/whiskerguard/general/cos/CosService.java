/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-general-service
 * 文件名称：CosService.java
 * 包    名：com.whiskerguard.general.cos
 * 描    述：猫伯伯合规管家公共微服务：提供通用工具类和帮助模块、云对象存储（COS）服务、分布式配置与安全组件、消息和事件驱动服务、缓存与分布式协同、国际化、多语言支持、公共 API 响应与文档功能
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/4/15
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.general.cos;

import com.whiskerguard.general.service.dto.FileOperationRequestDTO;
import java.io.File;
import java.util.List;

/**
 * COS 服务接口，定义文件上传、下载和删除的方法
 */
public interface CosService {
    /**
     * 上传文件到 COS
     *
     * @param file 需要上传的文件
     * @param key  在 COS 中存储的路径和文件名
     * @return 返回文件的访问 URL
     * @throws Exception 上传过程中的异常
     */
    String upload(File file, String key) throws Exception;

    /**
     * 获取文件的临时访问链接（带签名）
     *
     * @param key COS 中存储的文件路径和文件名
     * @return 临时有效的下载链接
     * @throws Exception 生成链接过程中的异常
     */
    String generatePresignedUrl(String key) throws Exception;

    /**
     * 删除 COS 中的文件
     *
     * @param key COS 中存储的文件路径和文件名
     * @return 删除成功返回 true，否则返回 false
     * @throws Exception 删除过程中的异常
     */
    boolean delete(String key) throws Exception;

    /**
     * 列出指定目录下的所有文件
     *
     * @param directoryPath 目录路径，以斜杠结尾（例如 "path/to/directory/"）
     * @return 文件路径列表
     */
    List<String> listDirectoryFiles(String directoryPath);

    /**
     * 根据文件名读取腾讯云COS中的文件内容
     * <p>
     * 通过文件名从腾讯云COS读取文件内容，支持文本文件、PDF、Word等格式。
     * 返回的内容已经过格式转换，可以直接用于AI分析。
     *
     * @param request 包含文件名和租户ID的请求DTO
     * @return 文件的文本内容，如果是二进制文件会转换为文本格式
     */
    String readFileContent(FileOperationRequestDTO request);

    /**
     * 检查腾讯云COS中的文件是否存在
     * <p>
     * 验证指定的文件名在腾讯云COS中是否存在，
     * 用于在读取文件内容前进行预检查。
     *
     * @param request 包含文件名和租户ID的请求DTO
     * @return 文件是否存在的布尔值
     */
    Boolean checkFileExists(FileOperationRequestDTO request);

    /**
     * 获取文件的基本信息
     * <p>
     * 获取文件的元数据信息，包括文件大小、类型、创建时间等。
     * 用于在处理文件前了解文件的基本属性。
     *
     * @param request 包含文件名和租户ID的请求DTO
     * @return 文件信息的JSON字符串
     */
    String getFileInfo(FileOperationRequestDTO request);
}
