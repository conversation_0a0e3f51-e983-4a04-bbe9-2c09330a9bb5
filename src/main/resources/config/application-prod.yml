# ===================================================================
# Spring Boot configuration for the "prod" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    com.whiskerguard.general: DEBUG
    jdk.internal.httpclient: INFO

management:
  prometheus:
    metrics:
      export:
        enabled: false
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
  tracing:
    sampling:
      probability: 1.0 # report 100% of traces

spring:
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false
  cloud:
    consul:
      discovery:
        prefer-ip-address: true
        ip-address: ***************
      host: dev.consule.mbbhg.com
      port: 8500
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: **************************************************************************************************************************************************************
    username: root
    password: MBB_root_2025@
    hikari:
      poolName: Hikari
      auto-commit: false
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
  # Replace by 'prod, faker' to add the faker context and have sample data loaded in production
  liquibase:
    contexts: prod
    enabled: false
  thymeleaf:
    cache: true
  # Elasticsearch 配置
  elasticsearch:
    uris: http://dev.es.mbbhg.com #真实的es地址
    username: elastic
    password: Ye29WTuf
    connection-timeout: 1s
    socket-timeout: 30s

# ===================================================================
# To enable TLS in production, generate a certificate using:
# keytool -genkey -alias whiskerguardgeneralservice -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# See details in topic "Create a Java Keystore (.JKS) from Let's Encrypt Certificates" on https://maximilian-boehm.com/en-gb/blog
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#   port: 443
#   ssl:
#     key-store: classpath:config/tls/keystore.p12
#     key-store-password: password
#     key-store-type: PKCS12
#     key-alias: selfsigned
#     # The ciphers suite enforce the security by deactivating some old and deprecated SSL cipher, this list was tested against SSL Labs (https://www.ssllabs.com/ssltest/)
#     ciphers: TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 ,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA,TLS_RSA_WITH_CAMELLIA_128_CBC_SHA
# ===================================================================
server:
  port: 8187
  shutdown: graceful # see https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-graceful-shutdown
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,application/javascript,application/json,image/svg+xml
    min-response-size: 1024

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  http:
    cache: # Used by the CachingHttpHeadersFilter
      timeToLiveInDays: 1461
  cache: # Cache configuration
    redis: # Redis configuration
      expiration: 3600 # By default objects stay 1 hour (in seconds) in the cache
      server: redis://dev.redis.mbbhg.com:6379
      cluster: false
      # server: redis://localhost:6379,redis://localhost:16379,redis://localhost:26379
      # cluster: true
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        # As this is the PRODUCTION configuration, you MUST change the default key, and store it securely:
        # - In the Consul configserver
        # - In a separate `application-prod.yml` file, in the same folder as your executable JAR file
        # - In the `JHIPSTER_SECURITY_AUTHENTICATION_JWT_BASE64_SECRET` environment variable
        base64-secret: ZGRiYjA3MjVmMzM2YzY3MzRhYWYxYWNhYTUxNDE5YWQ0NmZmZGI4MTVlYWYzNjg5YmY2NWE0MGU1NTc4NjMzYzkxMDI0ZTlhZjNkMTBkMjk5ZjA5ODE0OThiOTMzZDk4ZGM1NjIwNDkxNzA1YmQzMWNhYzQxNmEyM2VjMTc2NDE=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: true
      host: ***********
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

tianyancha:
  api-token: 'YOUR_TIANYANCHA_API_TOKEN' # 替换为您的天眼查 API Token
  base-url: 'http://open.api.tianyancha.com/services/open/ic/'
  cache-expiration-hours: 168 # 缓存过期时间（小时），默认7天
  connection-timeout-ms: 5000 # 连接超时时间（毫秒）
  read-timeout-ms: 10000 # 读取超时时间（毫秒）
  max-retry-attempts: 3 # 最大重试次数
  retry-delay-ms: 1000 # 重试延迟时间（毫秒）

# application:
application:
  notification:
    email:
      enabled: false # 禁用邮件服务
      from: <EMAIL>
    wechat:
      enabled: true
      app-id: 'wx101caef81fb1b3b9'
      app-secret: 'e3b5df7f7002f025b86b46c39032eda4'
      token: 'W9k3pQz7LmA2xHcVtR5Yb'
      aes-key: '0HvXG7fdUAKf1uhBvj54Tj8lpGXWNl8oxajtEgh4Grw'
      redirect-uri: 'https://dev.api.mbbhg.com/api/wechat/callback/oauth'

