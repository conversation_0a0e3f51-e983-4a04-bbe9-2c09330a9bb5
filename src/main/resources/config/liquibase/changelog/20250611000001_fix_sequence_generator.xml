<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!-- Fix sequence_generator table if it doesn't exist -->
    <changeSet id="20250611000001-1" author="manus">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="sequence_generator"/>
            </not>
        </preConditions>
        <createTable tableName="sequence_generator">
            <column name="sequence_name" type="VARCHAR(255)">
                <constraints primaryKey="true" primaryKeyName="sequence_generatorPK"/>
            </column>
            <column name="next_val" type="BIGINT"/>
        </createTable>
        <insert tableName="sequence_generator">
            <column name="sequence_name" value="default"/>
            <column name="next_val" value="1"/>
        </insert>
    </changeSet>

</databaseChangeLog>
