<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!-- This explicitly modifies the tables to ensure AUTO_INCREMENT is properly set -->
    <changeSet id="20250611112900-1" author="system">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
        </preConditions>
        <comment>Fix auto_increment for all company-related tables</comment>
        <sql>
            ALTER TABLE company MODIFY id BIGINT NOT NULL AUTO_INCREMENT;
            ALTER TABLE company_contact MODIFY id BIGINT NOT NULL AUTO_INCREMENT;
            ALTER TABLE company_risk MODIFY id BIGINT NOT NULL AUTO_INCREMENT;
            ALTER TABLE company_change_record MODIFY id BIGINT NOT NULL AUTO_INCREMENT;
            ALTER TABLE company_dishonest_person MODIFY id BIGINT NOT NULL AUTO_INCREMENT;
            ALTER TABLE company_case_filing MODIFY id BIGINT NOT NULL AUTO_INCREMENT;
        </sql>
    </changeSet>

</databaseChangeLog>
