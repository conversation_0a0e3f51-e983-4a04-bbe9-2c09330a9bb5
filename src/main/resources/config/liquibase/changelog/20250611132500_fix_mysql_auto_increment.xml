<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Fix MySQL auto-increment for all Tianyancha module tables
    -->
    <changeSet id="20250611132500-1" author="system">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
            <tableExists tableName="company"/>
        </preConditions>
        <comment>Fix auto-increment for company table</comment>
        <sql>ALTER TABLE company MODIFY COLUMN id BIGINT AUTO_INCREMENT</sql>
    </changeSet>

    <changeSet id="20250611132500-2" author="system">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
            <tableExists tableName="company_contact"/>
        </preConditions>
        <comment>Fix auto-increment for company_contact table</comment>
        <sql>ALTER TABLE company_contact MODIFY COLUMN id BIGINT AUTO_INCREMENT</sql>
    </changeSet>

    <changeSet id="20250611132500-3" author="system">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
            <tableExists tableName="company_risk"/>
        </preConditions>
        <comment>Fix auto-increment for company_risk table</comment>
        <sql>ALTER TABLE company_risk MODIFY COLUMN id BIGINT AUTO_INCREMENT</sql>
    </changeSet>

    <changeSet id="20250611132500-4" author="system">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
            <tableExists tableName="company_change_record"/>
        </preConditions>
        <comment>Fix auto-increment for company_change_record table</comment>
        <sql>ALTER TABLE company_change_record MODIFY COLUMN id BIGINT AUTO_INCREMENT</sql>
    </changeSet>

    <changeSet id="20250611132500-5" author="system">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
            <tableExists tableName="company_dishonest_person"/>
        </preConditions>
        <comment>Fix auto-increment for company_dishonest_person table</comment>
        <sql>ALTER TABLE company_dishonest_person MODIFY COLUMN id BIGINT AUTO_INCREMENT</sql>
    </changeSet>

    <changeSet id="20250611132500-6" author="system">
        <preConditions onFail="MARK_RAN">
            <dbms type="mysql"/>
            <tableExists tableName="company_case_filing"/>
        </preConditions>
        <comment>Fix auto-increment for company_case_filing table</comment>
        <sql>ALTER TABLE company_case_filing MODIFY COLUMN id BIGINT AUTO_INCREMENT</sql>
    </changeSet>

</databaseChangeLog>
