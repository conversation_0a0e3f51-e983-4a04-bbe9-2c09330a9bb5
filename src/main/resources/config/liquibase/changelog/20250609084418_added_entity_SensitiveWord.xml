<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity SensitiveWord.
    -->
    <changeSet id="20250609084418-1" author="jhipster">
        <createTable tableName="sensitive_word" remarks="存储平台级或租户级的敏感词及策略信息">
            <column name="id" type="bigint" remarks="主键ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID（0 = 平台级）">
                <constraints nullable="false" />
            </column>
            <column name="term" type="varchar(255)" remarks="敏感词条">
                <constraints nullable="false" />
            </column>
            <column name="lang" type="varchar(255)" remarks="语言">
                <constraints nullable="false" />
            </column>
            <column name="category" type="varchar(255)" remarks="分类">
                <constraints nullable="false" />
            </column>
            <column name="severity" type="varchar(255)" remarks="严重级别">
                <constraints nullable="false" />
            </column>
            <column name="valid_from" type="${datetimeType}" remarks="有效开始时间">
                <constraints nullable="false" />
            </column>
            <column name="valid_to" type="${datetimeType}" remarks="有效结束时间">
                <constraints nullable="false" />
            </column>
            <column name="notes" type="varchar(255)" remarks="备注">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="sensitive_word" columnName="valid_from" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="sensitive_word" columnName="valid_to" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="sensitive_word" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="sensitive_word" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250609084418-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/sensitive_word.csv"
                  separator=";"
                  tableName="sensitive_word"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="term" type="string"/>
            <column name="lang" type="string"/>
            <column name="category" type="string"/>
            <column name="severity" type="string"/>
            <column name="valid_from" type="date"/>
            <column name="valid_to" type="date"/>
            <column name="notes" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
