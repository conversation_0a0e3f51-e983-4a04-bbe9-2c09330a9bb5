<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Company.
    -->
    <changeSet id="20250610000001-1" author="manus">
        <createTable tableName="company">
            <column name="id" type="bigint" autoIncrement="true" startWith="1">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tianyancha_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="name" type="varchar(500)">
                <constraints nullable="false" />
            </column>
            <column name="unified_social_credit_code" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="legal_person_name" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="reg_status" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="reg_capital" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="reg_capital_currency" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="establish_time" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="company_org_type" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="reg_number" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="tax_number" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="org_number" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="industry" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="reg_location" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="business_scope" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="tianyancha_update_time" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="cache_time" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="history_names" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="cancel_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="revoke_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="revoke_reason" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="cancel_reason" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="approved_time" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="from_time" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="to_time" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="actual_capital" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="actual_capital_currency" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="reg_institute" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="city" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="district" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="staff_num_range" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="social_staff_num" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="bond_num" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="bond_name" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="bond_type" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="used_bond_name" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="alias" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="property3" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="tags" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="percentile_score" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="is_micro_ent" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="base" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="type" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="comp_form" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="industry_category" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="industry_category_big" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="industry_category_middle" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="industry_category_small" type="varchar(100)">
                <constraints nullable="true" />
            </column>
        </createTable>
    </changeSet>

    <changeSet id="20250610000001-2" author="manus">
        <createIndex indexName="idx_company_unified_social_credit_code" tableName="company">
            <column name="unified_social_credit_code"/>
        </createIndex>
        <createIndex indexName="idx_company_name" tableName="company">
            <column name="name"/>
        </createIndex>
        <createIndex indexName="idx_company_reg_number" tableName="company">
            <column name="reg_number"/>
        </createIndex>
        <createIndex indexName="idx_company_tax_number" tableName="company">
            <column name="tax_number"/>
        </createIndex>
        <createIndex indexName="idx_company_tianyancha_id" tableName="company">
            <column name="tianyancha_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>

