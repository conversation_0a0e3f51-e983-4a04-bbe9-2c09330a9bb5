<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity NotificationSendRecord.
    -->
    <changeSet id="20250623112937-1" author="jhipster">
        <createTable tableName="notification_send_record" remarks="存储每个接收者的通知发送详细记录">
            <column name="id" type="bigint" remarks="主键ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID（0 = 平台级）">
                <constraints nullable="false" />
            </column>
            <column name="recipient_id" type="bigint" remarks="接收者ID">
                <constraints nullable="false" />
            </column>
            <column name="recipient_type" type="varchar(255)" remarks="接收者类型">
                <constraints nullable="false" />
            </column>
            <column name="channel" type="varchar(255)" remarks="发送渠道">
                <constraints nullable="false" />
            </column>
            <column name="status" type="varchar(255)" remarks="发送状态">
                <constraints nullable="false" />
            </column>
            <column name="sent_time" type="${datetimeType}" remarks="发送时间">
                <constraints nullable="true" />
            </column>
            <column name="read_time" type="${datetimeType}" remarks="阅读时间">
                <constraints nullable="true" />
            </column>
            <column name="error_message" type="varchar(1000)" remarks="错误信息">
                <constraints nullable="true" />
            </column>
            <column name="external_id" type="varchar(100)" remarks="第三方服务返回的ID">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(50)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="notification_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="notification_send_record" columnName="sent_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="notification_send_record" columnName="read_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="notification_send_record" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="notification_send_record" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250623112937-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/notification_send_record.csv"
                  separator=";"
                  tableName="notification_send_record"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="recipient_id" type="numeric"/>
            <column name="recipient_type" type="string"/>
            <column name="channel" type="string"/>
            <column name="status" type="string"/>
            <column name="sent_time" type="date"/>
            <column name="read_time" type="date"/>
            <column name="error_message" type="string"/>
            <column name="external_id" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
