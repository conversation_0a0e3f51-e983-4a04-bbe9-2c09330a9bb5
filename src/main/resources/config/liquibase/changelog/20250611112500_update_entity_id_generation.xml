<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!--
        Update entity ID generation strategy from SEQUENCE to IDENTITY
    -->
    <changeSet id="20250611112500-1" author="system">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="company"/>
        </preConditions>
        <comment>Update checksum for ID generation changes in Company-related entities</comment>
    </changeSet>

    <changeSet id="20250611112500-2" author="system">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="20250610000002-1" author="manus" changeLogFile="config/liquibase/changelog/20250610000002_added_entity_CompanyContact.xml"/>
        </preConditions>
        <comment>Update checksum for CompanyContact</comment>
    </changeSet>

    <changeSet id="20250611112500-3" author="system">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="20250610000003-1" author="manus" changeLogFile="config/liquibase/changelog/20250610000003_added_entity_CompanyRisk.xml"/>
        </preConditions>
        <comment>Update checksum for CompanyRisk</comment>
    </changeSet>

    <changeSet id="20250611112500-4" author="system">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="20250610000004-1" author="manus" changeLogFile="config/liquibase/changelog/20250610000004_added_entity_CompanyChangeRecord.xml"/>
        </preConditions>
        <comment>Update checksum for CompanyChangeRecord</comment>
    </changeSet>

    <changeSet id="20250611112500-5" author="system">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="20250610000005-1" author="manus" changeLogFile="config/liquibase/changelog/20250610000005_added_entity_CompanyDishonestPerson.xml"/>
        </preConditions>
        <comment>Update checksum for CompanyDishonestPerson</comment>
    </changeSet>

    <changeSet id="20250611112500-6" author="system">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted id="20250610000006-1" author="manus" changeLogFile="config/liquibase/changelog/20250610000006_added_entity_CompanyCaseFiling.xml"/>
        </preConditions>
        <comment>Update checksum for CompanyCaseFiling</comment>
    </changeSet>

</databaseChangeLog>
