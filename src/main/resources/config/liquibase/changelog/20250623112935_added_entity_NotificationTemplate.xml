<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity NotificationTemplate.
    -->
    <changeSet id="20250623112935-1" author="jhipster">
        <createTable tableName="notification_template" remarks="存储通知模板配置信息">
            <column name="id" type="bigint" remarks="主键ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID（0 = 平台级）">
                <constraints nullable="false" />
            </column>
            <column name="code" type="varchar(100)" remarks="模板编码">
                <constraints nullable="false" unique="true" uniqueConstraintName="ux_notification_template__code" />
            </column>
            <column name="name" type="varchar(200)" remarks="模板名称">
                <constraints nullable="false" />
            </column>
            <column name="category" type="varchar(255)" remarks="通知分类">
                <constraints nullable="false" />
            </column>
            <column name="sub_type" type="varchar(255)" remarks="通知子类型">
                <constraints nullable="false" />
            </column>
            <column name="title_template" type="varchar(500)" remarks="标题模板">
                <constraints nullable="true" />
            </column>
            <column name="content_template" type="varchar(2000)" remarks="内容模板">
                <constraints nullable="true" />
            </column>
            <column name="sms_template" type="varchar(500)" remarks="短信模板">
                <constraints nullable="true" />
            </column>
            <column name="email_template" type="varchar(2000)" remarks="邮件模板">
                <constraints nullable="true" />
            </column>
            <column name="push_template" type="varchar(500)" remarks="推送模板">
                <constraints nullable="true" />
            </column>
            <column name="supported_channels" type="varchar(200)" remarks="支持的渠道(JSON格式)">
                <constraints nullable="true" />
            </column>
            <column name="default_channels" type="varchar(200)" remarks="默认渠道(JSON格式)">
                <constraints nullable="true" />
            </column>
            <column name="enabled" type="boolean" remarks="是否启用">
                <constraints nullable="false" />
            </column>
            <column name="language" type="varchar(10)" remarks="语言">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(50)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="notification_template" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="notification_template" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250623112935-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/notification_template.csv"
                  separator=";"
                  tableName="notification_template"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="code" type="string"/>
            <column name="name" type="string"/>
            <column name="category" type="string"/>
            <column name="sub_type" type="string"/>
            <column name="title_template" type="string"/>
            <column name="content_template" type="string"/>
            <column name="sms_template" type="string"/>
            <column name="email_template" type="string"/>
            <column name="push_template" type="string"/>
            <column name="supported_channels" type="string"/>
            <column name="default_channels" type="string"/>
            <column name="enabled" type="boolean"/>
            <column name="language" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
