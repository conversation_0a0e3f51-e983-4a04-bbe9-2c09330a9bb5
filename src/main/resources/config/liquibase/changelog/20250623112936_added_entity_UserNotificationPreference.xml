<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity UserNotificationPreference.
    -->
    <changeSet id="20250623112936-1" author="jhipster">
        <createTable tableName="user_notification_preference" remarks="存储用户的通知偏好设置">
            <column name="id" type="bigint" remarks="主键ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID（0 = 平台级）">
                <constraints nullable="false" />
            </column>
            <column name="user_id" type="bigint" remarks="用户ID">
                <constraints nullable="false" />
            </column>
            <column name="category" type="varchar(255)" remarks="通知分类">
                <constraints nullable="false" />
            </column>
            <column name="sub_type" type="varchar(255)" remarks="通知子类型">
                <constraints nullable="true" />
            </column>
            <column name="enabled_channels" type="varchar(200)" remarks="启用的渠道(JSON格式)">
                <constraints nullable="true" />
            </column>
            <column name="quiet_hours_start" type="varchar(5)" remarks="免打扰开始时间">
                <constraints nullable="true" />
            </column>
            <column name="quiet_hours_end" type="varchar(5)" remarks="免打扰结束时间">
                <constraints nullable="true" />
            </column>
            <column name="enabled" type="boolean" remarks="是否启用">
                <constraints nullable="false" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(50)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="user_notification_preference" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="user_notification_preference" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250623112936-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/user_notification_preference.csv"
                  separator=";"
                  tableName="user_notification_preference"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="user_id" type="numeric"/>
            <column name="category" type="string"/>
            <column name="sub_type" type="string"/>
            <column name="enabled_channels" type="string"/>
            <column name="quiet_hours_start" type="string"/>
            <column name="quiet_hours_end" type="string"/>
            <column name="enabled" type="boolean"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
