<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity SignatureDocument.
    -->
    <changeSet id="20250523091435-1" author="jhipster">
        <createTable tableName="signature_document" remarks="存储电子签名文档的相关信息">
            <column name="id" type="bigint" remarks="主键ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="title" type="varchar(255)" remarks="文档标题">
                <constraints nullable="false" />
            </column>
            <column name="description" type="varchar(255)" remarks="文档描述">
                <constraints nullable="true" />
            </column>
            <column name="document_url" type="varchar(255)" remarks="文档URL地址">
                <constraints nullable="false" />
            </column>
            <column name="status" type="varchar(255)" remarks="文档状态">
                <constraints nullable="false" />
            </column>
            <column name="provider" type="varchar(255)" remarks="签名服务提供商">
                <constraints nullable="false" />
            </column>
            <column name="external_id" type="varchar(255)" remarks="外部系统文档ID">
                <constraints nullable="true" />
            </column>
            <column name="transaction_id" type="varchar(255)" remarks="事务ID">
                <constraints nullable="true" />
            </column>
            <column name="user_id" type="varchar(255)" remarks="用户ID">
                <constraints nullable="false" />
            </column>
            <column name="expire_time" type="${datetimeType}" remarks="过期时间">
                <constraints nullable="true" />
            </column>
            <column name="signed_time" type="${datetimeType}" remarks="签署时间">
                <constraints nullable="true" />
            </column>
            <column name="signed_document_url" type="varchar(255)" remarks="签署后文档URL">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="varchar(255)" remarks="扩展元数据（JSON格式）">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="signature_document" columnName="expire_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="signature_document" columnName="signed_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="signature_document" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="signature_document" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250523091435-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/signature_document.csv"
                  separator=";"
                  tableName="signature_document"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="title" type="string"/>
            <column name="description" type="string"/>
            <column name="document_url" type="string"/>
            <column name="status" type="string"/>
            <column name="provider" type="string"/>
            <column name="external_id" type="string"/>
            <column name="transaction_id" type="string"/>
            <column name="user_id" type="string"/>
            <column name="expire_time" type="date"/>
            <column name="signed_time" type="date"/>
            <column name="signed_document_url" type="string"/>
            <column name="metadata" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
