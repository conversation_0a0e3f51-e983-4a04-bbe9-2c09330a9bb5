# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: whiskerguardgeneralservice
services:
  app:
    image: whiskerguardgeneralservice
    environment:
      - _JAVA_OPTIONS=-Xmx512m -Xms256m
      - SPRING_PROFILES_ACTIVE=prod,api-docs
      - MANAGEMENT_PROMETHEUS_METRICS_EXPORT_ENABLED=true
      - SPRING_CLOUD_CONSUL_HOST=consul
      - SPRING_CLOUD_CONSUL_PORT=8500
      - SPRING_DATASOURCE_URL=****************************************************************************************************************************************************************
      - SPRING_LIQUIBASE_URL=****************************************************************************************************************************************************************
      - JHIPSTER_CACHE_REDIS_SERVER=redis://redis:6379
      - JHIPSTER_CACHE_REDIS_CLUSTER=false
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8187/management/health
      interval: 5s
      timeout: 5s
      retries: 40
    depends_on:
      mysql:
        condition: service_healthy
  mysql:
    extends:
      file: ./mysql.yml
      service: mysql
  redis:
    extends:
      file: ./redis.yml
      service: redis
  consul:
    extends:
      file: ./consul.yml
      service: consul
  consul-config-loader:
    extends:
      file: ./consul.yml
      service: consul-config-loader
