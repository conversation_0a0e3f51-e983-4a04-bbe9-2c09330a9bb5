package com.whiskerguard.general.web.rest;

import static com.whiskerguard.general.domain.UserNotificationPreferenceAsserts.*;
import static com.whiskerguard.general.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.UserNotificationPreference;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationSubType;
import com.whiskerguard.general.repository.UserNotificationPreferenceRepository;
import com.whiskerguard.general.service.dto.UserNotificationPreferenceDTO;
import com.whiskerguard.general.service.mapper.UserNotificationPreferenceMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link UserNotificationPreferenceResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class UserNotificationPreferenceResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_USER_ID = 1L;
    private static final Long UPDATED_USER_ID = 2L;

    private static final NotificationCategory DEFAULT_CATEGORY = NotificationCategory.SYSTEM;
    private static final NotificationCategory UPDATED_CATEGORY = NotificationCategory.TASK;

    private static final NotificationSubType DEFAULT_SUB_TYPE = NotificationSubType.SYSTEM_ANNOUNCEMENT;
    private static final NotificationSubType UPDATED_SUB_TYPE = NotificationSubType.SYSTEM_MAINTENANCE;

    private static final String DEFAULT_ENABLED_CHANNELS = "AAAAAAAAAA";
    private static final String UPDATED_ENABLED_CHANNELS = "BBBBBBBBBB";

    private static final String DEFAULT_QUIET_HOURS_START = "AAAAA";
    private static final String UPDATED_QUIET_HOURS_START = "BBBBB";

    private static final String DEFAULT_QUIET_HOURS_END = "AAAAA";
    private static final String UPDATED_QUIET_HOURS_END = "BBBBB";

    private static final Boolean DEFAULT_ENABLED = false;
    private static final Boolean UPDATED_ENABLED = true;

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/user-notification-preferences";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private UserNotificationPreferenceRepository userNotificationPreferenceRepository;

    @Autowired
    private UserNotificationPreferenceMapper userNotificationPreferenceMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restUserNotificationPreferenceMockMvc;

    private UserNotificationPreference userNotificationPreference;

    private UserNotificationPreference insertedUserNotificationPreference;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static UserNotificationPreference createEntity() {
        return new UserNotificationPreference()
            .tenantId(DEFAULT_TENANT_ID)
            .userId(DEFAULT_USER_ID)
            .category(DEFAULT_CATEGORY)
            .subType(DEFAULT_SUB_TYPE)
            .enabledChannels(DEFAULT_ENABLED_CHANNELS)
            .quietHoursStart(DEFAULT_QUIET_HOURS_START)
            .quietHoursEnd(DEFAULT_QUIET_HOURS_END)
            .enabled(DEFAULT_ENABLED)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static UserNotificationPreference createUpdatedEntity() {
        return new UserNotificationPreference()
            .tenantId(UPDATED_TENANT_ID)
            .userId(UPDATED_USER_ID)
            .category(UPDATED_CATEGORY)
            .subType(UPDATED_SUB_TYPE)
            .enabledChannels(UPDATED_ENABLED_CHANNELS)
            .quietHoursStart(UPDATED_QUIET_HOURS_START)
            .quietHoursEnd(UPDATED_QUIET_HOURS_END)
            .enabled(UPDATED_ENABLED)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        userNotificationPreference = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedUserNotificationPreference != null) {
            userNotificationPreferenceRepository.delete(insertedUserNotificationPreference);
            insertedUserNotificationPreference = null;
        }
    }

    @Test
    @Transactional
    void createUserNotificationPreference() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the UserNotificationPreference
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);
        var returnedUserNotificationPreferenceDTO = om.readValue(
            restUserNotificationPreferenceMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(userNotificationPreferenceDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            UserNotificationPreferenceDTO.class
        );

        // Validate the UserNotificationPreference in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedUserNotificationPreference = userNotificationPreferenceMapper.toEntity(returnedUserNotificationPreferenceDTO);
        assertUserNotificationPreferenceUpdatableFieldsEquals(
            returnedUserNotificationPreference,
            getPersistedUserNotificationPreference(returnedUserNotificationPreference)
        );

        insertedUserNotificationPreference = returnedUserNotificationPreference;
    }

    @Test
    @Transactional
    void createUserNotificationPreferenceWithExistingId() throws Exception {
        // Create the UserNotificationPreference with an existing ID
        userNotificationPreference.setId(1L);
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserNotificationPreference in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userNotificationPreference.setTenantId(null);

        // Create the UserNotificationPreference, which fails.
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUserIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userNotificationPreference.setUserId(null);

        // Create the UserNotificationPreference, which fails.
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCategoryIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userNotificationPreference.setCategory(null);

        // Create the UserNotificationPreference, which fails.
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEnabledIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userNotificationPreference.setEnabled(null);

        // Create the UserNotificationPreference, which fails.
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userNotificationPreference.setVersion(null);

        // Create the UserNotificationPreference, which fails.
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userNotificationPreference.setCreatedAt(null);

        // Create the UserNotificationPreference, which fails.
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userNotificationPreference.setUpdatedAt(null);

        // Create the UserNotificationPreference, which fails.
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userNotificationPreference.setIsDeleted(null);

        // Create the UserNotificationPreference, which fails.
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        restUserNotificationPreferenceMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllUserNotificationPreferences() throws Exception {
        // Initialize the database
        insertedUserNotificationPreference = userNotificationPreferenceRepository.saveAndFlush(userNotificationPreference);

        // Get all the userNotificationPreferenceList
        restUserNotificationPreferenceMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(userNotificationPreference.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].userId").value(hasItem(DEFAULT_USER_ID.intValue())))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY.toString())))
            .andExpect(jsonPath("$.[*].subType").value(hasItem(DEFAULT_SUB_TYPE.toString())))
            .andExpect(jsonPath("$.[*].enabledChannels").value(hasItem(DEFAULT_ENABLED_CHANNELS)))
            .andExpect(jsonPath("$.[*].quietHoursStart").value(hasItem(DEFAULT_QUIET_HOURS_START)))
            .andExpect(jsonPath("$.[*].quietHoursEnd").value(hasItem(DEFAULT_QUIET_HOURS_END)))
            .andExpect(jsonPath("$.[*].enabled").value(hasItem(DEFAULT_ENABLED)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getUserNotificationPreference() throws Exception {
        // Initialize the database
        insertedUserNotificationPreference = userNotificationPreferenceRepository.saveAndFlush(userNotificationPreference);

        // Get the userNotificationPreference
        restUserNotificationPreferenceMockMvc
            .perform(get(ENTITY_API_URL_ID, userNotificationPreference.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(userNotificationPreference.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.userId").value(DEFAULT_USER_ID.intValue()))
            .andExpect(jsonPath("$.category").value(DEFAULT_CATEGORY.toString()))
            .andExpect(jsonPath("$.subType").value(DEFAULT_SUB_TYPE.toString()))
            .andExpect(jsonPath("$.enabledChannels").value(DEFAULT_ENABLED_CHANNELS))
            .andExpect(jsonPath("$.quietHoursStart").value(DEFAULT_QUIET_HOURS_START))
            .andExpect(jsonPath("$.quietHoursEnd").value(DEFAULT_QUIET_HOURS_END))
            .andExpect(jsonPath("$.enabled").value(DEFAULT_ENABLED))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingUserNotificationPreference() throws Exception {
        // Get the userNotificationPreference
        restUserNotificationPreferenceMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingUserNotificationPreference() throws Exception {
        // Initialize the database
        insertedUserNotificationPreference = userNotificationPreferenceRepository.saveAndFlush(userNotificationPreference);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userNotificationPreference
        UserNotificationPreference updatedUserNotificationPreference = userNotificationPreferenceRepository
            .findById(userNotificationPreference.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedUserNotificationPreference are not directly saved in db
        em.detach(updatedUserNotificationPreference);
        updatedUserNotificationPreference
            .tenantId(UPDATED_TENANT_ID)
            .userId(UPDATED_USER_ID)
            .category(UPDATED_CATEGORY)
            .subType(UPDATED_SUB_TYPE)
            .enabledChannels(UPDATED_ENABLED_CHANNELS)
            .quietHoursStart(UPDATED_QUIET_HOURS_START)
            .quietHoursEnd(UPDATED_QUIET_HOURS_END)
            .enabled(UPDATED_ENABLED)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(
            updatedUserNotificationPreference
        );

        restUserNotificationPreferenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, userNotificationPreferenceDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isOk());

        // Validate the UserNotificationPreference in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedUserNotificationPreferenceToMatchAllProperties(updatedUserNotificationPreference);
    }

    @Test
    @Transactional
    void putNonExistingUserNotificationPreference() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userNotificationPreference.setId(longCount.incrementAndGet());

        // Create the UserNotificationPreference
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUserNotificationPreferenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, userNotificationPreferenceDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserNotificationPreference in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchUserNotificationPreference() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userNotificationPreference.setId(longCount.incrementAndGet());

        // Create the UserNotificationPreference
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserNotificationPreferenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserNotificationPreference in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamUserNotificationPreference() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userNotificationPreference.setId(longCount.incrementAndGet());

        // Create the UserNotificationPreference
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserNotificationPreferenceMockMvc
            .perform(
                put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the UserNotificationPreference in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateUserNotificationPreferenceWithPatch() throws Exception {
        // Initialize the database
        insertedUserNotificationPreference = userNotificationPreferenceRepository.saveAndFlush(userNotificationPreference);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userNotificationPreference using partial update
        UserNotificationPreference partialUpdatedUserNotificationPreference = new UserNotificationPreference();
        partialUpdatedUserNotificationPreference.setId(userNotificationPreference.getId());

        partialUpdatedUserNotificationPreference
            .tenantId(UPDATED_TENANT_ID)
            .subType(UPDATED_SUB_TYPE)
            .quietHoursStart(UPDATED_QUIET_HOURS_START)
            .enabled(UPDATED_ENABLED)
            .version(UPDATED_VERSION)
            .updatedAt(UPDATED_UPDATED_AT);

        restUserNotificationPreferenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUserNotificationPreference.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUserNotificationPreference))
            )
            .andExpect(status().isOk());

        // Validate the UserNotificationPreference in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUserNotificationPreferenceUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedUserNotificationPreference, userNotificationPreference),
            getPersistedUserNotificationPreference(userNotificationPreference)
        );
    }

    @Test
    @Transactional
    void fullUpdateUserNotificationPreferenceWithPatch() throws Exception {
        // Initialize the database
        insertedUserNotificationPreference = userNotificationPreferenceRepository.saveAndFlush(userNotificationPreference);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userNotificationPreference using partial update
        UserNotificationPreference partialUpdatedUserNotificationPreference = new UserNotificationPreference();
        partialUpdatedUserNotificationPreference.setId(userNotificationPreference.getId());

        partialUpdatedUserNotificationPreference
            .tenantId(UPDATED_TENANT_ID)
            .userId(UPDATED_USER_ID)
            .category(UPDATED_CATEGORY)
            .subType(UPDATED_SUB_TYPE)
            .enabledChannels(UPDATED_ENABLED_CHANNELS)
            .quietHoursStart(UPDATED_QUIET_HOURS_START)
            .quietHoursEnd(UPDATED_QUIET_HOURS_END)
            .enabled(UPDATED_ENABLED)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restUserNotificationPreferenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUserNotificationPreference.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUserNotificationPreference))
            )
            .andExpect(status().isOk());

        // Validate the UserNotificationPreference in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUserNotificationPreferenceUpdatableFieldsEquals(
            partialUpdatedUserNotificationPreference,
            getPersistedUserNotificationPreference(partialUpdatedUserNotificationPreference)
        );
    }

    @Test
    @Transactional
    void patchNonExistingUserNotificationPreference() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userNotificationPreference.setId(longCount.incrementAndGet());

        // Create the UserNotificationPreference
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUserNotificationPreferenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, userNotificationPreferenceDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserNotificationPreference in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchUserNotificationPreference() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userNotificationPreference.setId(longCount.incrementAndGet());

        // Create the UserNotificationPreference
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserNotificationPreferenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserNotificationPreference in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamUserNotificationPreference() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userNotificationPreference.setId(longCount.incrementAndGet());

        // Create the UserNotificationPreference
        UserNotificationPreferenceDTO userNotificationPreferenceDTO = userNotificationPreferenceMapper.toDto(userNotificationPreference);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserNotificationPreferenceMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(userNotificationPreferenceDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the UserNotificationPreference in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteUserNotificationPreference() throws Exception {
        // Initialize the database
        insertedUserNotificationPreference = userNotificationPreferenceRepository.saveAndFlush(userNotificationPreference);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the userNotificationPreference
        restUserNotificationPreferenceMockMvc
            .perform(delete(ENTITY_API_URL_ID, userNotificationPreference.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return userNotificationPreferenceRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected UserNotificationPreference getPersistedUserNotificationPreference(UserNotificationPreference userNotificationPreference) {
        return userNotificationPreferenceRepository.findById(userNotificationPreference.getId()).orElseThrow();
    }

    protected void assertPersistedUserNotificationPreferenceToMatchAllProperties(
        UserNotificationPreference expectedUserNotificationPreference
    ) {
        assertUserNotificationPreferenceAllPropertiesEquals(
            expectedUserNotificationPreference,
            getPersistedUserNotificationPreference(expectedUserNotificationPreference)
        );
    }

    protected void assertPersistedUserNotificationPreferenceToMatchUpdatableProperties(
        UserNotificationPreference expectedUserNotificationPreference
    ) {
        assertUserNotificationPreferenceAllUpdatablePropertiesEquals(
            expectedUserNotificationPreference,
            getPersistedUserNotificationPreference(expectedUserNotificationPreference)
        );
    }
}
