package com.whiskerguard.general.web.rest;

import static com.whiskerguard.general.domain.UserWechatBindingAsserts.*;
import static com.whiskerguard.general.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.UserWechatBinding;
import com.whiskerguard.general.repository.UserWechatBindingRepository;
import com.whiskerguard.general.service.dto.UserWechatBindingDTO;
import com.whiskerguard.general.service.mapper.UserWechatBindingMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link UserWechatBindingResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class UserWechatBindingResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final String DEFAULT_OPEN_ID = "AAAAAAAAAA";
    private static final String UPDATED_OPEN_ID = "BBBBBBBBBB";

    private static final String DEFAULT_UNION_ID = "AAAAAAAAAA";
    private static final String UPDATED_UNION_ID = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/user-wechat-bindings";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private UserWechatBindingRepository userWechatBindingRepository;

    @Autowired
    private UserWechatBindingMapper userWechatBindingMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restUserWechatBindingMockMvc;

    private UserWechatBinding userWechatBinding;

    private UserWechatBinding insertedUserWechatBinding;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static UserWechatBinding createEntity() {
        return new UserWechatBinding()
            .tenantId(DEFAULT_TENANT_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .openId(DEFAULT_OPEN_ID)
            .unionId(DEFAULT_UNION_ID)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static UserWechatBinding createUpdatedEntity() {
        return new UserWechatBinding()
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .openId(UPDATED_OPEN_ID)
            .unionId(UPDATED_UNION_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        userWechatBinding = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedUserWechatBinding != null) {
            userWechatBindingRepository.delete(insertedUserWechatBinding);
            insertedUserWechatBinding = null;
        }
    }

    @Test
    @Transactional
    void createUserWechatBinding() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the UserWechatBinding
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);
        var returnedUserWechatBindingDTO = om.readValue(
            restUserWechatBindingMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            UserWechatBindingDTO.class
        );

        // Validate the UserWechatBinding in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedUserWechatBinding = userWechatBindingMapper.toEntity(returnedUserWechatBindingDTO);
        assertUserWechatBindingUpdatableFieldsEquals(returnedUserWechatBinding, getPersistedUserWechatBinding(returnedUserWechatBinding));

        insertedUserWechatBinding = returnedUserWechatBinding;
    }

    @Test
    @Transactional
    void createUserWechatBindingWithExistingId() throws Exception {
        // Create the UserWechatBinding with an existing ID
        userWechatBinding.setId(1L);
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restUserWechatBindingMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isBadRequest());

        // Validate the UserWechatBinding in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userWechatBinding.setEmployeeId(null);

        // Create the UserWechatBinding, which fails.
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        restUserWechatBindingMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkOpenIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userWechatBinding.setOpenId(null);

        // Create the UserWechatBinding, which fails.
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        restUserWechatBindingMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userWechatBinding.setVersion(null);

        // Create the UserWechatBinding, which fails.
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        restUserWechatBindingMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userWechatBinding.setCreatedAt(null);

        // Create the UserWechatBinding, which fails.
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        restUserWechatBindingMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userWechatBinding.setUpdatedAt(null);

        // Create the UserWechatBinding, which fails.
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        restUserWechatBindingMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        userWechatBinding.setIsDeleted(null);

        // Create the UserWechatBinding, which fails.
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        restUserWechatBindingMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllUserWechatBindings() throws Exception {
        // Initialize the database
        insertedUserWechatBinding = userWechatBindingRepository.saveAndFlush(userWechatBinding);

        // Get all the userWechatBindingList
        restUserWechatBindingMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(userWechatBinding.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].openId").value(hasItem(DEFAULT_OPEN_ID)))
            .andExpect(jsonPath("$.[*].unionId").value(hasItem(DEFAULT_UNION_ID)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getUserWechatBinding() throws Exception {
        // Initialize the database
        insertedUserWechatBinding = userWechatBindingRepository.saveAndFlush(userWechatBinding);

        // Get the userWechatBinding
        restUserWechatBindingMockMvc
            .perform(get(ENTITY_API_URL_ID, userWechatBinding.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(userWechatBinding.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.openId").value(DEFAULT_OPEN_ID))
            .andExpect(jsonPath("$.unionId").value(DEFAULT_UNION_ID))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingUserWechatBinding() throws Exception {
        // Get the userWechatBinding
        restUserWechatBindingMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingUserWechatBinding() throws Exception {
        // Initialize the database
        insertedUserWechatBinding = userWechatBindingRepository.saveAndFlush(userWechatBinding);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userWechatBinding
        UserWechatBinding updatedUserWechatBinding = userWechatBindingRepository.findById(userWechatBinding.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedUserWechatBinding are not directly saved in db
        em.detach(updatedUserWechatBinding);
        updatedUserWechatBinding
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .openId(UPDATED_OPEN_ID)
            .unionId(UPDATED_UNION_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(updatedUserWechatBinding);

        restUserWechatBindingMockMvc
            .perform(
                put(ENTITY_API_URL_ID, userWechatBindingDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userWechatBindingDTO))
            )
            .andExpect(status().isOk());

        // Validate the UserWechatBinding in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedUserWechatBindingToMatchAllProperties(updatedUserWechatBinding);
    }

    @Test
    @Transactional
    void putNonExistingUserWechatBinding() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userWechatBinding.setId(longCount.incrementAndGet());

        // Create the UserWechatBinding
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUserWechatBindingMockMvc
            .perform(
                put(ENTITY_API_URL_ID, userWechatBindingDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userWechatBindingDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserWechatBinding in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchUserWechatBinding() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userWechatBinding.setId(longCount.incrementAndGet());

        // Create the UserWechatBinding
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserWechatBindingMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(userWechatBindingDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserWechatBinding in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamUserWechatBinding() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userWechatBinding.setId(longCount.incrementAndGet());

        // Create the UserWechatBinding
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserWechatBindingMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the UserWechatBinding in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateUserWechatBindingWithPatch() throws Exception {
        // Initialize the database
        insertedUserWechatBinding = userWechatBindingRepository.saveAndFlush(userWechatBinding);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userWechatBinding using partial update
        UserWechatBinding partialUpdatedUserWechatBinding = new UserWechatBinding();
        partialUpdatedUserWechatBinding.setId(userWechatBinding.getId());

        partialUpdatedUserWechatBinding
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .openId(UPDATED_OPEN_ID)
            .unionId(UPDATED_UNION_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restUserWechatBindingMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUserWechatBinding.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUserWechatBinding))
            )
            .andExpect(status().isOk());

        // Validate the UserWechatBinding in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUserWechatBindingUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedUserWechatBinding, userWechatBinding),
            getPersistedUserWechatBinding(userWechatBinding)
        );
    }

    @Test
    @Transactional
    void fullUpdateUserWechatBindingWithPatch() throws Exception {
        // Initialize the database
        insertedUserWechatBinding = userWechatBindingRepository.saveAndFlush(userWechatBinding);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the userWechatBinding using partial update
        UserWechatBinding partialUpdatedUserWechatBinding = new UserWechatBinding();
        partialUpdatedUserWechatBinding.setId(userWechatBinding.getId());

        partialUpdatedUserWechatBinding
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .openId(UPDATED_OPEN_ID)
            .unionId(UPDATED_UNION_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restUserWechatBindingMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUserWechatBinding.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUserWechatBinding))
            )
            .andExpect(status().isOk());

        // Validate the UserWechatBinding in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUserWechatBindingUpdatableFieldsEquals(
            partialUpdatedUserWechatBinding,
            getPersistedUserWechatBinding(partialUpdatedUserWechatBinding)
        );
    }

    @Test
    @Transactional
    void patchNonExistingUserWechatBinding() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userWechatBinding.setId(longCount.incrementAndGet());

        // Create the UserWechatBinding
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUserWechatBindingMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, userWechatBindingDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(userWechatBindingDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserWechatBinding in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchUserWechatBinding() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userWechatBinding.setId(longCount.incrementAndGet());

        // Create the UserWechatBinding
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserWechatBindingMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(userWechatBindingDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the UserWechatBinding in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamUserWechatBinding() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        userWechatBinding.setId(longCount.incrementAndGet());

        // Create the UserWechatBinding
        UserWechatBindingDTO userWechatBindingDTO = userWechatBindingMapper.toDto(userWechatBinding);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserWechatBindingMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(userWechatBindingDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the UserWechatBinding in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteUserWechatBinding() throws Exception {
        // Initialize the database
        insertedUserWechatBinding = userWechatBindingRepository.saveAndFlush(userWechatBinding);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the userWechatBinding
        restUserWechatBindingMockMvc
            .perform(delete(ENTITY_API_URL_ID, userWechatBinding.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return userWechatBindingRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected UserWechatBinding getPersistedUserWechatBinding(UserWechatBinding userWechatBinding) {
        return userWechatBindingRepository.findById(userWechatBinding.getId()).orElseThrow();
    }

    protected void assertPersistedUserWechatBindingToMatchAllProperties(UserWechatBinding expectedUserWechatBinding) {
        assertUserWechatBindingAllPropertiesEquals(expectedUserWechatBinding, getPersistedUserWechatBinding(expectedUserWechatBinding));
    }

    protected void assertPersistedUserWechatBindingToMatchUpdatableProperties(UserWechatBinding expectedUserWechatBinding) {
        assertUserWechatBindingAllUpdatablePropertiesEquals(
            expectedUserWechatBinding,
            getPersistedUserWechatBinding(expectedUserWechatBinding)
        );
    }
}
