package com.whiskerguard.general.web.rest;

import static com.whiskerguard.general.domain.CompanyTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.Company;
import com.whiskerguard.general.repository.CompanyRepository;
import com.whiskerguard.general.service.TianyanchaQueryService;
import com.whiskerguard.general.service.dto.CompanyDTO;
import com.whiskerguard.general.service.dto.CompanyVerificationResultDTO;
import com.whiskerguard.general.service.exception.TianyanchaApiException;
import com.whiskerguard.general.service.mapper.CompanyMapper;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TianyanchaResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
public class TianyanchaResourceIT {

    private static final String ENTITY_API_URL = "/api/tianyancha";

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTianyanchaMockMvc;

    @Autowired
    private ObjectMapper om;

    @MockBean
    private TianyanchaQueryService tianyanchaQueryService;

    private Company company;
    private CompanyDTO companyDTO;

    @BeforeEach
    void initTest() {
        company = getCompanySample1();
        companyDTO = companyMapper.toDto(company);
    }

    @Test
    @Transactional
    void getCompanyBasicInfo() throws Exception {
        // Given
        String keyword = "中航重机股份有限公司";
        when(tianyanchaQueryService.getCompanyBasicInfo(keyword)).thenReturn(companyDTO);

        // When & Then
        restTianyanchaMockMvc
            .perform(get(ENTITY_API_URL + "/company/basic-info").param("keyword", keyword))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(companyDTO.getId().intValue()))
            .andExpect(jsonPath("$.name").value(companyDTO.getName()))
            .andExpect(jsonPath("$.unifiedSocialCreditCode").value(companyDTO.getUnifiedSocialCreditCode()))
            .andExpect(jsonPath("$.legalPersonName").value(companyDTO.getLegalPersonName()));

        verify(tianyanchaQueryService).getCompanyBasicInfo(keyword);
    }

    @Test
    @Transactional
    void getCompanyBasicInfo_NotFound() throws Exception {
        // Given
        String keyword = "nonexistent";
        when(tianyanchaQueryService.getCompanyBasicInfo(keyword)).thenThrow(new TianyanchaApiException("Company not found"));

        // When & Then
        restTianyanchaMockMvc
            .perform(get(ENTITY_API_URL + "/company/basic-info").param("keyword", keyword))
            .andExpect(status().isInternalServerError());
    }

    @Test
    @Transactional
    void getCompanyBasicInfo_MissingKeyword() throws Exception {
        // When & Then
        restTianyanchaMockMvc.perform(get(ENTITY_API_URL + "/company/basic-info")).andExpect(status().isBadRequest());
    }

    @Test
    @Transactional
    void verifyCompanyThreeElements() throws Exception {
        // Given
        CompanyVerificationResultDTO verificationResult = new CompanyVerificationResultDTO();
        verificationResult.setVerifyResult("一致");
        verificationResult.setCompanyName("中航重机股份有限公司");
        verificationResult.setCreditCode("91520000214434146R");
        verificationResult.setLegalPersonName("姬苏春");
        verificationResult.setMessage("企业三要素验证通过");

        String requestBody =
            """
            {
                "companyName": "中航重机股份有限公司",
                "creditCode": "91520000214434146R",
                "legalPersonName": "姬苏春"
            }
            """;

        when(tianyanchaQueryService.verifyCompanyThreeElements(anyString(), anyString(), anyString())).thenReturn(verificationResult);

        // When & Then
        restTianyanchaMockMvc
            .perform(post(ENTITY_API_URL + "/company/verify").contentType(MediaType.APPLICATION_JSON).content(requestBody))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.verifyResult").value("一致"))
            .andExpect(jsonPath("$.companyName").value("中航重机股份有限公司"))
            .andExpect(jsonPath("$.creditCode").value("91520000214434146R"))
            .andExpect(jsonPath("$.legalPersonName").value("姬苏春"))
            .andExpect(jsonPath("$.message").value("企业三要素验证通过"));
    }

    @Test
    @Transactional
    void refreshCompanyData() throws Exception {
        // Given
        String keyword = "中航重机股份有限公司";
        when(tianyanchaQueryService.refreshCompanyData(keyword)).thenReturn(companyDTO);

        // When & Then
        restTianyanchaMockMvc
            .perform(post(ENTITY_API_URL + "/company/refresh").param("keyword", keyword))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(companyDTO.getId().intValue()))
            .andExpect(jsonPath("$.name").value(companyDTO.getName()));

        verify(tianyanchaQueryService).refreshCompanyData(keyword);
    }

    @Test
    @Transactional
    void getCompanyRisk() throws Exception {
        // Given
        String keyword = "中航重机股份有限公司";
        String riskData = "{\"errorCode\":0,\"reason\":\"ok\",\"result\":{\"riskInfo\":\"low\"}}";
        when(tianyanchaQueryService.getCompanyRisk(keyword)).thenReturn(riskData);

        // When & Then
        restTianyanchaMockMvc
            .perform(get(ENTITY_API_URL + "/company/risk").param("keyword", keyword))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string(riskData));
    }

    @Test
    @Transactional
    void getCompanyContact() throws Exception {
        // Given
        String keyword = "中航重机股份有限公司";
        String contactData = "{\"errorCode\":0,\"reason\":\"ok\",\"result\":{\"phone\":\"*********\"}}";
        when(tianyanchaQueryService.getCompanyContactInfo(keyword)).thenReturn(contactData);

        // When & Then
        restTianyanchaMockMvc
            .perform(get(ENTITY_API_URL + "/company/contact").param("keyword", keyword))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string(contactData));
    }

    @Test
    @Transactional
    void getCompanyChangeRecords() throws Exception {
        // Given
        String keyword = "中航重机股份有限公司";
        String changeData = "{\"errorCode\":0,\"reason\":\"ok\",\"result\":{\"changes\":[]}}";
        when(tianyanchaQueryService.getCompanyChangeRecords(keyword)).thenReturn(changeData);

        // When & Then
        restTianyanchaMockMvc
            .perform(get(ENTITY_API_URL + "/company/change-records").param("keyword", keyword))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string(changeData));
    }

    @Test
    @Transactional
    void getCompanyType() throws Exception {
        // Given
        String keyword = "中航重机股份有限公司";
        String typeData = "{\"errorCode\":0,\"reason\":\"ok\",\"result\":{\"type\":\"股份有限公司\"}}";
        when(tianyanchaQueryService.getCompanyTypeInfo(keyword)).thenReturn(typeData);

        // When & Then
        restTianyanchaMockMvc
            .perform(get(ENTITY_API_URL + "/company/type").param("keyword", keyword))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string(typeData));
    }

    @Test
    @Transactional
    void getCompanyBusiness() throws Exception {
        // Given
        String keyword = "中航重机股份有限公司";
        String businessData = "{\"errorCode\":0,\"reason\":\"ok\",\"result\":{\"business\":\"manufacturing\"}}";
        when(tianyanchaQueryService.getCompanyBusinessInfo(keyword)).thenReturn(businessData);

        // When & Then
        restTianyanchaMockMvc
            .perform(get(ENTITY_API_URL + "/company/business").param("keyword", keyword))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(content().string(businessData));
    }
}
