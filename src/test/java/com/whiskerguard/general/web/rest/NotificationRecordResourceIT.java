package com.whiskerguard.general.web.rest;

import static com.whiskerguard.general.domain.NotificationRecordAsserts.*;
import static com.whiskerguard.general.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationPriority;
import com.whiskerguard.general.domain.enumeration.NotificationScope;
import com.whiskerguard.general.domain.enumeration.NotificationStatus;
import com.whiskerguard.general.domain.enumeration.NotificationSubType;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.repository.NotificationRecordRepository;
import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import com.whiskerguard.general.service.mapper.NotificationRecordMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NotificationRecordResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class NotificationRecordResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final NotificationCategory DEFAULT_CATEGORY = NotificationCategory.SYSTEM;
    private static final NotificationCategory UPDATED_CATEGORY = NotificationCategory.TASK;

    private static final NotificationSubType DEFAULT_SUB_TYPE = NotificationSubType.SYSTEM_ANNOUNCEMENT;
    private static final NotificationSubType UPDATED_SUB_TYPE = NotificationSubType.SYSTEM_MAINTENANCE;

    private static final NotificationScope DEFAULT_SCOPE = NotificationScope.GLOBAL;
    private static final NotificationScope UPDATED_SCOPE = NotificationScope.TENANT;

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_CONTENT = "BBBBBBBBBB";

    private static final RecipientType DEFAULT_RECIPIENT_TYPE = RecipientType.USER;
    private static final RecipientType UPDATED_RECIPIENT_TYPE = RecipientType.ROLE;

    private static final String DEFAULT_RECIPIENT_IDS = "AAAAAAAAAA";
    private static final String UPDATED_RECIPIENT_IDS = "BBBBBBBBBB";

    private static final String DEFAULT_CHANNELS = "AAAAAAAAAA";
    private static final String UPDATED_CHANNELS = "BBBBBBBBBB";

    private static final NotificationPriority DEFAULT_PRIORITY = NotificationPriority.LOW;
    private static final NotificationPriority UPDATED_PRIORITY = NotificationPriority.NORMAL;

    private static final NotificationStatus DEFAULT_STATUS = NotificationStatus.DRAFT;
    private static final NotificationStatus UPDATED_STATUS = NotificationStatus.SCHEDULED;

    private static final Instant DEFAULT_SCHEDULED_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_SCHEDULED_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_SENT_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_SENT_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_BUSINESS_ID = "AAAAAAAAAA";
    private static final String UPDATED_BUSINESS_ID = "BBBBBBBBBB";

    private static final String DEFAULT_BUSINESS_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_BUSINESS_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_TEMPLATE_PARAMS = "AAAAAAAAAA";
    private static final String UPDATED_TEMPLATE_PARAMS = "BBBBBBBBBB";

    private static final Integer DEFAULT_RETRY_COUNT = 0;
    private static final Integer UPDATED_RETRY_COUNT = 1;

    private static final String DEFAULT_ERROR_MESSAGE = "AAAAAAAAAA";
    private static final String UPDATED_ERROR_MESSAGE = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/notification-records";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NotificationRecordRepository notificationRecordRepository;

    @Autowired
    private NotificationRecordMapper notificationRecordMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNotificationRecordMockMvc;

    private NotificationRecord notificationRecord;

    private NotificationRecord insertedNotificationRecord;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NotificationRecord createEntity() {
        return new NotificationRecord()
            .tenantId(DEFAULT_TENANT_ID)
            .category(DEFAULT_CATEGORY)
            .subType(DEFAULT_SUB_TYPE)
            .scope(DEFAULT_SCOPE)
            .title(DEFAULT_TITLE)
            .content(DEFAULT_CONTENT)
            .recipientType(DEFAULT_RECIPIENT_TYPE)
            .recipientIds(DEFAULT_RECIPIENT_IDS)
            .channels(DEFAULT_CHANNELS)
            .priority(DEFAULT_PRIORITY)
            .status(DEFAULT_STATUS)
            .scheduledTime(DEFAULT_SCHEDULED_TIME)
            .sentTime(DEFAULT_SENT_TIME)
            .businessId(DEFAULT_BUSINESS_ID)
            .businessType(DEFAULT_BUSINESS_TYPE)
            .templateParams(DEFAULT_TEMPLATE_PARAMS)
            .retryCount(DEFAULT_RETRY_COUNT)
            .errorMessage(DEFAULT_ERROR_MESSAGE)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NotificationRecord createUpdatedEntity() {
        return new NotificationRecord()
            .tenantId(UPDATED_TENANT_ID)
            .category(UPDATED_CATEGORY)
            .subType(UPDATED_SUB_TYPE)
            .scope(UPDATED_SCOPE)
            .title(UPDATED_TITLE)
            .content(UPDATED_CONTENT)
            .recipientType(UPDATED_RECIPIENT_TYPE)
            .recipientIds(UPDATED_RECIPIENT_IDS)
            .channels(UPDATED_CHANNELS)
            .priority(UPDATED_PRIORITY)
            .status(UPDATED_STATUS)
            .scheduledTime(UPDATED_SCHEDULED_TIME)
            .sentTime(UPDATED_SENT_TIME)
            .businessId(UPDATED_BUSINESS_ID)
            .businessType(UPDATED_BUSINESS_TYPE)
            .templateParams(UPDATED_TEMPLATE_PARAMS)
            .retryCount(UPDATED_RETRY_COUNT)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        notificationRecord = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNotificationRecord != null) {
            notificationRecordRepository.delete(insertedNotificationRecord);
            insertedNotificationRecord = null;
        }
    }

    @Test
    @Transactional
    void createNotificationRecord() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NotificationRecord
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);
        var returnedNotificationRecordDTO = om.readValue(
            restNotificationRecordMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NotificationRecordDTO.class
        );

        // Validate the NotificationRecord in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNotificationRecord = notificationRecordMapper.toEntity(returnedNotificationRecordDTO);
        assertNotificationRecordUpdatableFieldsEquals(
            returnedNotificationRecord,
            getPersistedNotificationRecord(returnedNotificationRecord)
        );

        insertedNotificationRecord = returnedNotificationRecord;
    }

    @Test
    @Transactional
    void createNotificationRecordWithExistingId() throws Exception {
        // Create the NotificationRecord with an existing ID
        notificationRecord.setId(1L);
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NotificationRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setTenantId(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCategoryIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setCategory(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkSubTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setSubType(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkScopeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setScope(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTitleIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setTitle(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRecipientTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setRecipientType(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkPriorityIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setPriority(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setStatus(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setVersion(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setCreatedAt(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setUpdatedAt(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationRecord.setIsDeleted(null);

        // Create the NotificationRecord, which fails.
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        restNotificationRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNotificationRecords() throws Exception {
        // Initialize the database
        insertedNotificationRecord = notificationRecordRepository.saveAndFlush(notificationRecord);

        // Get all the notificationRecordList
        restNotificationRecordMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(notificationRecord.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY.toString())))
            .andExpect(jsonPath("$.[*].subType").value(hasItem(DEFAULT_SUB_TYPE.toString())))
            .andExpect(jsonPath("$.[*].scope").value(hasItem(DEFAULT_SCOPE.toString())))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].content").value(hasItem(DEFAULT_CONTENT)))
            .andExpect(jsonPath("$.[*].recipientType").value(hasItem(DEFAULT_RECIPIENT_TYPE.toString())))
            .andExpect(jsonPath("$.[*].recipientIds").value(hasItem(DEFAULT_RECIPIENT_IDS)))
            .andExpect(jsonPath("$.[*].channels").value(hasItem(DEFAULT_CHANNELS)))
            .andExpect(jsonPath("$.[*].priority").value(hasItem(DEFAULT_PRIORITY.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].scheduledTime").value(hasItem(DEFAULT_SCHEDULED_TIME.toString())))
            .andExpect(jsonPath("$.[*].sentTime").value(hasItem(DEFAULT_SENT_TIME.toString())))
            .andExpect(jsonPath("$.[*].businessId").value(hasItem(DEFAULT_BUSINESS_ID)))
            .andExpect(jsonPath("$.[*].businessType").value(hasItem(DEFAULT_BUSINESS_TYPE)))
            .andExpect(jsonPath("$.[*].templateParams").value(hasItem(DEFAULT_TEMPLATE_PARAMS)))
            .andExpect(jsonPath("$.[*].retryCount").value(hasItem(DEFAULT_RETRY_COUNT)))
            .andExpect(jsonPath("$.[*].errorMessage").value(hasItem(DEFAULT_ERROR_MESSAGE)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getNotificationRecord() throws Exception {
        // Initialize the database
        insertedNotificationRecord = notificationRecordRepository.saveAndFlush(notificationRecord);

        // Get the notificationRecord
        restNotificationRecordMockMvc
            .perform(get(ENTITY_API_URL_ID, notificationRecord.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(notificationRecord.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.category").value(DEFAULT_CATEGORY.toString()))
            .andExpect(jsonPath("$.subType").value(DEFAULT_SUB_TYPE.toString()))
            .andExpect(jsonPath("$.scope").value(DEFAULT_SCOPE.toString()))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.content").value(DEFAULT_CONTENT))
            .andExpect(jsonPath("$.recipientType").value(DEFAULT_RECIPIENT_TYPE.toString()))
            .andExpect(jsonPath("$.recipientIds").value(DEFAULT_RECIPIENT_IDS))
            .andExpect(jsonPath("$.channels").value(DEFAULT_CHANNELS))
            .andExpect(jsonPath("$.priority").value(DEFAULT_PRIORITY.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.scheduledTime").value(DEFAULT_SCHEDULED_TIME.toString()))
            .andExpect(jsonPath("$.sentTime").value(DEFAULT_SENT_TIME.toString()))
            .andExpect(jsonPath("$.businessId").value(DEFAULT_BUSINESS_ID))
            .andExpect(jsonPath("$.businessType").value(DEFAULT_BUSINESS_TYPE))
            .andExpect(jsonPath("$.templateParams").value(DEFAULT_TEMPLATE_PARAMS))
            .andExpect(jsonPath("$.retryCount").value(DEFAULT_RETRY_COUNT))
            .andExpect(jsonPath("$.errorMessage").value(DEFAULT_ERROR_MESSAGE))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNotificationRecord() throws Exception {
        // Get the notificationRecord
        restNotificationRecordMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNotificationRecord() throws Exception {
        // Initialize the database
        insertedNotificationRecord = notificationRecordRepository.saveAndFlush(notificationRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the notificationRecord
        NotificationRecord updatedNotificationRecord = notificationRecordRepository.findById(notificationRecord.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNotificationRecord are not directly saved in db
        em.detach(updatedNotificationRecord);
        updatedNotificationRecord
            .tenantId(UPDATED_TENANT_ID)
            .category(UPDATED_CATEGORY)
            .subType(UPDATED_SUB_TYPE)
            .scope(UPDATED_SCOPE)
            .title(UPDATED_TITLE)
            .content(UPDATED_CONTENT)
            .recipientType(UPDATED_RECIPIENT_TYPE)
            .recipientIds(UPDATED_RECIPIENT_IDS)
            .channels(UPDATED_CHANNELS)
            .priority(UPDATED_PRIORITY)
            .status(UPDATED_STATUS)
            .scheduledTime(UPDATED_SCHEDULED_TIME)
            .sentTime(UPDATED_SENT_TIME)
            .businessId(UPDATED_BUSINESS_ID)
            .businessType(UPDATED_BUSINESS_TYPE)
            .templateParams(UPDATED_TEMPLATE_PARAMS)
            .retryCount(UPDATED_RETRY_COUNT)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(updatedNotificationRecord);

        restNotificationRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, notificationRecordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(notificationRecordDTO))
            )
            .andExpect(status().isOk());

        // Validate the NotificationRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNotificationRecordToMatchAllProperties(updatedNotificationRecord);
    }

    @Test
    @Transactional
    void putNonExistingNotificationRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationRecord.setId(longCount.incrementAndGet());

        // Create the NotificationRecord
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNotificationRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, notificationRecordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(notificationRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NotificationRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNotificationRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationRecord.setId(longCount.incrementAndGet());

        // Create the NotificationRecord
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNotificationRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(notificationRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NotificationRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNotificationRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationRecord.setId(longCount.incrementAndGet());

        // Create the NotificationRecord
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNotificationRecordMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NotificationRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNotificationRecordWithPatch() throws Exception {
        // Initialize the database
        insertedNotificationRecord = notificationRecordRepository.saveAndFlush(notificationRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the notificationRecord using partial update
        NotificationRecord partialUpdatedNotificationRecord = new NotificationRecord();
        partialUpdatedNotificationRecord.setId(notificationRecord.getId());

        partialUpdatedNotificationRecord
            .tenantId(UPDATED_TENANT_ID)
            .category(UPDATED_CATEGORY)
            .scope(UPDATED_SCOPE)
            .title(UPDATED_TITLE)
            .content(UPDATED_CONTENT)
            .recipientType(UPDATED_RECIPIENT_TYPE)
            .priority(UPDATED_PRIORITY)
            .status(UPDATED_STATUS)
            .businessId(UPDATED_BUSINESS_ID)
            .retryCount(UPDATED_RETRY_COUNT)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restNotificationRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNotificationRecord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNotificationRecord))
            )
            .andExpect(status().isOk());

        // Validate the NotificationRecord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNotificationRecordUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedNotificationRecord, notificationRecord),
            getPersistedNotificationRecord(notificationRecord)
        );
    }

    @Test
    @Transactional
    void fullUpdateNotificationRecordWithPatch() throws Exception {
        // Initialize the database
        insertedNotificationRecord = notificationRecordRepository.saveAndFlush(notificationRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the notificationRecord using partial update
        NotificationRecord partialUpdatedNotificationRecord = new NotificationRecord();
        partialUpdatedNotificationRecord.setId(notificationRecord.getId());

        partialUpdatedNotificationRecord
            .tenantId(UPDATED_TENANT_ID)
            .category(UPDATED_CATEGORY)
            .subType(UPDATED_SUB_TYPE)
            .scope(UPDATED_SCOPE)
            .title(UPDATED_TITLE)
            .content(UPDATED_CONTENT)
            .recipientType(UPDATED_RECIPIENT_TYPE)
            .recipientIds(UPDATED_RECIPIENT_IDS)
            .channels(UPDATED_CHANNELS)
            .priority(UPDATED_PRIORITY)
            .status(UPDATED_STATUS)
            .scheduledTime(UPDATED_SCHEDULED_TIME)
            .sentTime(UPDATED_SENT_TIME)
            .businessId(UPDATED_BUSINESS_ID)
            .businessType(UPDATED_BUSINESS_TYPE)
            .templateParams(UPDATED_TEMPLATE_PARAMS)
            .retryCount(UPDATED_RETRY_COUNT)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNotificationRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNotificationRecord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNotificationRecord))
            )
            .andExpect(status().isOk());

        // Validate the NotificationRecord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNotificationRecordUpdatableFieldsEquals(
            partialUpdatedNotificationRecord,
            getPersistedNotificationRecord(partialUpdatedNotificationRecord)
        );
    }

    @Test
    @Transactional
    void patchNonExistingNotificationRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationRecord.setId(longCount.incrementAndGet());

        // Create the NotificationRecord
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNotificationRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, notificationRecordDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(notificationRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NotificationRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNotificationRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationRecord.setId(longCount.incrementAndGet());

        // Create the NotificationRecord
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNotificationRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(notificationRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NotificationRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNotificationRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationRecord.setId(longCount.incrementAndGet());

        // Create the NotificationRecord
        NotificationRecordDTO notificationRecordDTO = notificationRecordMapper.toDto(notificationRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNotificationRecordMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(notificationRecordDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NotificationRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNotificationRecord() throws Exception {
        // Initialize the database
        insertedNotificationRecord = notificationRecordRepository.saveAndFlush(notificationRecord);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the notificationRecord
        restNotificationRecordMockMvc
            .perform(delete(ENTITY_API_URL_ID, notificationRecord.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return notificationRecordRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NotificationRecord getPersistedNotificationRecord(NotificationRecord notificationRecord) {
        return notificationRecordRepository.findById(notificationRecord.getId()).orElseThrow();
    }

    protected void assertPersistedNotificationRecordToMatchAllProperties(NotificationRecord expectedNotificationRecord) {
        assertNotificationRecordAllPropertiesEquals(expectedNotificationRecord, getPersistedNotificationRecord(expectedNotificationRecord));
    }

    protected void assertPersistedNotificationRecordToMatchUpdatableProperties(NotificationRecord expectedNotificationRecord) {
        assertNotificationRecordAllUpdatablePropertiesEquals(
            expectedNotificationRecord,
            getPersistedNotificationRecord(expectedNotificationRecord)
        );
    }
}
