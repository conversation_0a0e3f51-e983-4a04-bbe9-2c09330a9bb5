package com.whiskerguard.general.web.rest;

import static com.whiskerguard.general.domain.NotificationSendRecordAsserts.*;
import static com.whiskerguard.general.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.NotificationSendRecord;
import com.whiskerguard.general.domain.enumeration.NotificationType;
import com.whiskerguard.general.domain.enumeration.RecipientType;
import com.whiskerguard.general.domain.enumeration.SendStatus;
import com.whiskerguard.general.repository.NotificationSendRecordRepository;
import com.whiskerguard.general.service.dto.NotificationSendRecordDTO;
import com.whiskerguard.general.service.mapper.NotificationSendRecordMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NotificationSendRecordResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class NotificationSendRecordResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_RECIPIENT_ID = 1L;
    private static final Long UPDATED_RECIPIENT_ID = 2L;

    private static final RecipientType DEFAULT_RECIPIENT_TYPE = RecipientType.USER;
    private static final RecipientType UPDATED_RECIPIENT_TYPE = RecipientType.ROLE;

    private static final NotificationType DEFAULT_CHANNEL = NotificationType.SMS;
    private static final NotificationType UPDATED_CHANNEL = NotificationType.EMAIL;

    private static final SendStatus DEFAULT_STATUS = SendStatus.PENDING;
    private static final SendStatus UPDATED_STATUS = SendStatus.SENT;

    private static final Instant DEFAULT_SENT_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_SENT_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_READ_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_READ_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_ERROR_MESSAGE = "AAAAAAAAAA";
    private static final String UPDATED_ERROR_MESSAGE = "BBBBBBBBBB";

    private static final String DEFAULT_EXTERNAL_ID = "AAAAAAAAAA";
    private static final String UPDATED_EXTERNAL_ID = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/notification-send-records";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NotificationSendRecordRepository notificationSendRecordRepository;

    @Autowired
    private NotificationSendRecordMapper notificationSendRecordMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNotificationSendRecordMockMvc;

    private NotificationSendRecord notificationSendRecord;

    private NotificationSendRecord insertedNotificationSendRecord;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NotificationSendRecord createEntity() {
        return new NotificationSendRecord()
            .tenantId(DEFAULT_TENANT_ID)
            .recipientId(DEFAULT_RECIPIENT_ID)
            .recipientType(DEFAULT_RECIPIENT_TYPE)
            .channel(DEFAULT_CHANNEL)
            .status(DEFAULT_STATUS)
            .sentTime(DEFAULT_SENT_TIME)
            .readTime(DEFAULT_READ_TIME)
            .errorMessage(DEFAULT_ERROR_MESSAGE)
            .externalId(DEFAULT_EXTERNAL_ID)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NotificationSendRecord createUpdatedEntity() {
        return new NotificationSendRecord()
            .tenantId(UPDATED_TENANT_ID)
            .recipientId(UPDATED_RECIPIENT_ID)
            .recipientType(UPDATED_RECIPIENT_TYPE)
            .channel(UPDATED_CHANNEL)
            .status(UPDATED_STATUS)
            .sentTime(UPDATED_SENT_TIME)
            .readTime(UPDATED_READ_TIME)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .externalId(UPDATED_EXTERNAL_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        notificationSendRecord = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNotificationSendRecord != null) {
            notificationSendRecordRepository.delete(insertedNotificationSendRecord);
            insertedNotificationSendRecord = null;
        }
    }

    @Test
    @Transactional
    void createNotificationSendRecord() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NotificationSendRecord
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);
        var returnedNotificationSendRecordDTO = om.readValue(
            restNotificationSendRecordMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NotificationSendRecordDTO.class
        );

        // Validate the NotificationSendRecord in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNotificationSendRecord = notificationSendRecordMapper.toEntity(returnedNotificationSendRecordDTO);
        assertNotificationSendRecordUpdatableFieldsEquals(
            returnedNotificationSendRecord,
            getPersistedNotificationSendRecord(returnedNotificationSendRecord)
        );

        insertedNotificationSendRecord = returnedNotificationSendRecord;
    }

    @Test
    @Transactional
    void createNotificationSendRecordWithExistingId() throws Exception {
        // Create the NotificationSendRecord with an existing ID
        notificationSendRecord.setId(1L);
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NotificationSendRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setTenantId(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRecipientIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setRecipientId(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRecipientTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setRecipientType(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkChannelIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setChannel(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setStatus(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setVersion(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setCreatedAt(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setUpdatedAt(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        notificationSendRecord.setIsDeleted(null);

        // Create the NotificationSendRecord, which fails.
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNotificationSendRecords() throws Exception {
        // Initialize the database
        insertedNotificationSendRecord = notificationSendRecordRepository.saveAndFlush(notificationSendRecord);

        // Get all the notificationSendRecordList
        restNotificationSendRecordMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(notificationSendRecord.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].recipientId").value(hasItem(DEFAULT_RECIPIENT_ID.intValue())))
            .andExpect(jsonPath("$.[*].recipientType").value(hasItem(DEFAULT_RECIPIENT_TYPE.toString())))
            .andExpect(jsonPath("$.[*].channel").value(hasItem(DEFAULT_CHANNEL.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].sentTime").value(hasItem(DEFAULT_SENT_TIME.toString())))
            .andExpect(jsonPath("$.[*].readTime").value(hasItem(DEFAULT_READ_TIME.toString())))
            .andExpect(jsonPath("$.[*].errorMessage").value(hasItem(DEFAULT_ERROR_MESSAGE)))
            .andExpect(jsonPath("$.[*].externalId").value(hasItem(DEFAULT_EXTERNAL_ID)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getNotificationSendRecord() throws Exception {
        // Initialize the database
        insertedNotificationSendRecord = notificationSendRecordRepository.saveAndFlush(notificationSendRecord);

        // Get the notificationSendRecord
        restNotificationSendRecordMockMvc
            .perform(get(ENTITY_API_URL_ID, notificationSendRecord.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(notificationSendRecord.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.recipientId").value(DEFAULT_RECIPIENT_ID.intValue()))
            .andExpect(jsonPath("$.recipientType").value(DEFAULT_RECIPIENT_TYPE.toString()))
            .andExpect(jsonPath("$.channel").value(DEFAULT_CHANNEL.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.sentTime").value(DEFAULT_SENT_TIME.toString()))
            .andExpect(jsonPath("$.readTime").value(DEFAULT_READ_TIME.toString()))
            .andExpect(jsonPath("$.errorMessage").value(DEFAULT_ERROR_MESSAGE))
            .andExpect(jsonPath("$.externalId").value(DEFAULT_EXTERNAL_ID))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNotificationSendRecord() throws Exception {
        // Get the notificationSendRecord
        restNotificationSendRecordMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNotificationSendRecord() throws Exception {
        // Initialize the database
        insertedNotificationSendRecord = notificationSendRecordRepository.saveAndFlush(notificationSendRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the notificationSendRecord
        NotificationSendRecord updatedNotificationSendRecord = notificationSendRecordRepository
            .findById(notificationSendRecord.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedNotificationSendRecord are not directly saved in db
        em.detach(updatedNotificationSendRecord);
        updatedNotificationSendRecord
            .tenantId(UPDATED_TENANT_ID)
            .recipientId(UPDATED_RECIPIENT_ID)
            .recipientType(UPDATED_RECIPIENT_TYPE)
            .channel(UPDATED_CHANNEL)
            .status(UPDATED_STATUS)
            .sentTime(UPDATED_SENT_TIME)
            .readTime(UPDATED_READ_TIME)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .externalId(UPDATED_EXTERNAL_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(updatedNotificationSendRecord);

        restNotificationSendRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, notificationSendRecordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(notificationSendRecordDTO))
            )
            .andExpect(status().isOk());

        // Validate the NotificationSendRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNotificationSendRecordToMatchAllProperties(updatedNotificationSendRecord);
    }

    @Test
    @Transactional
    void putNonExistingNotificationSendRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationSendRecord.setId(longCount.incrementAndGet());

        // Create the NotificationSendRecord
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNotificationSendRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, notificationSendRecordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(notificationSendRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NotificationSendRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNotificationSendRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationSendRecord.setId(longCount.incrementAndGet());

        // Create the NotificationSendRecord
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNotificationSendRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(notificationSendRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NotificationSendRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNotificationSendRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationSendRecord.setId(longCount.incrementAndGet());

        // Create the NotificationSendRecord
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNotificationSendRecordMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(notificationSendRecordDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NotificationSendRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNotificationSendRecordWithPatch() throws Exception {
        // Initialize the database
        insertedNotificationSendRecord = notificationSendRecordRepository.saveAndFlush(notificationSendRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the notificationSendRecord using partial update
        NotificationSendRecord partialUpdatedNotificationSendRecord = new NotificationSendRecord();
        partialUpdatedNotificationSendRecord.setId(notificationSendRecord.getId());

        partialUpdatedNotificationSendRecord
            .tenantId(UPDATED_TENANT_ID)
            .recipientType(UPDATED_RECIPIENT_TYPE)
            .channel(UPDATED_CHANNEL)
            .status(UPDATED_STATUS)
            .readTime(UPDATED_READ_TIME)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restNotificationSendRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNotificationSendRecord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNotificationSendRecord))
            )
            .andExpect(status().isOk());

        // Validate the NotificationSendRecord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNotificationSendRecordUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedNotificationSendRecord, notificationSendRecord),
            getPersistedNotificationSendRecord(notificationSendRecord)
        );
    }

    @Test
    @Transactional
    void fullUpdateNotificationSendRecordWithPatch() throws Exception {
        // Initialize the database
        insertedNotificationSendRecord = notificationSendRecordRepository.saveAndFlush(notificationSendRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the notificationSendRecord using partial update
        NotificationSendRecord partialUpdatedNotificationSendRecord = new NotificationSendRecord();
        partialUpdatedNotificationSendRecord.setId(notificationSendRecord.getId());

        partialUpdatedNotificationSendRecord
            .tenantId(UPDATED_TENANT_ID)
            .recipientId(UPDATED_RECIPIENT_ID)
            .recipientType(UPDATED_RECIPIENT_TYPE)
            .channel(UPDATED_CHANNEL)
            .status(UPDATED_STATUS)
            .sentTime(UPDATED_SENT_TIME)
            .readTime(UPDATED_READ_TIME)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .externalId(UPDATED_EXTERNAL_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNotificationSendRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNotificationSendRecord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNotificationSendRecord))
            )
            .andExpect(status().isOk());

        // Validate the NotificationSendRecord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNotificationSendRecordUpdatableFieldsEquals(
            partialUpdatedNotificationSendRecord,
            getPersistedNotificationSendRecord(partialUpdatedNotificationSendRecord)
        );
    }

    @Test
    @Transactional
    void patchNonExistingNotificationSendRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationSendRecord.setId(longCount.incrementAndGet());

        // Create the NotificationSendRecord
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNotificationSendRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, notificationSendRecordDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(notificationSendRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NotificationSendRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNotificationSendRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationSendRecord.setId(longCount.incrementAndGet());

        // Create the NotificationSendRecord
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNotificationSendRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(notificationSendRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NotificationSendRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNotificationSendRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        notificationSendRecord.setId(longCount.incrementAndGet());

        // Create the NotificationSendRecord
        NotificationSendRecordDTO notificationSendRecordDTO = notificationSendRecordMapper.toDto(notificationSendRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNotificationSendRecordMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(notificationSendRecordDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the NotificationSendRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNotificationSendRecord() throws Exception {
        // Initialize the database
        insertedNotificationSendRecord = notificationSendRecordRepository.saveAndFlush(notificationSendRecord);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the notificationSendRecord
        restNotificationSendRecordMockMvc
            .perform(delete(ENTITY_API_URL_ID, notificationSendRecord.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return notificationSendRecordRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NotificationSendRecord getPersistedNotificationSendRecord(NotificationSendRecord notificationSendRecord) {
        return notificationSendRecordRepository.findById(notificationSendRecord.getId()).orElseThrow();
    }

    protected void assertPersistedNotificationSendRecordToMatchAllProperties(NotificationSendRecord expectedNotificationSendRecord) {
        assertNotificationSendRecordAllPropertiesEquals(
            expectedNotificationSendRecord,
            getPersistedNotificationSendRecord(expectedNotificationSendRecord)
        );
    }

    protected void assertPersistedNotificationSendRecordToMatchUpdatableProperties(NotificationSendRecord expectedNotificationSendRecord) {
        assertNotificationSendRecordAllUpdatablePropertiesEquals(
            expectedNotificationSendRecord,
            getPersistedNotificationSendRecord(expectedNotificationSendRecord)
        );
    }
}
