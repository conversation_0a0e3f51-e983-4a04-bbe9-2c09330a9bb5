package com.whiskerguard.general.web.rest;

import static com.whiskerguard.general.domain.SignatureDocumentAsserts.*;
import static com.whiskerguard.general.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.SignatureDocument;
import com.whiskerguard.general.domain.enumeration.SignatureDocumentStatus;
import com.whiskerguard.general.domain.enumeration.SignatureProvider;
import com.whiskerguard.general.repository.SignatureDocumentRepository;
import com.whiskerguard.general.service.dto.SignatureDocumentDTO;
import com.whiskerguard.general.service.mapper.SignatureDocumentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link SignatureDocumentResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class SignatureDocumentResourceIT {

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final String DEFAULT_DOCUMENT_URL = "AAAAAAAAAA";
    private static final String UPDATED_DOCUMENT_URL = "BBBBBBBBBB";

    private static final SignatureDocumentStatus DEFAULT_STATUS = SignatureDocumentStatus.CREATED;
    private static final SignatureDocumentStatus UPDATED_STATUS = SignatureDocumentStatus.PENDING;

    private static final SignatureProvider DEFAULT_PROVIDER = SignatureProvider.FADADA;
    private static final SignatureProvider UPDATED_PROVIDER = SignatureProvider.ESIGN;

    private static final String DEFAULT_EXTERNAL_ID = "AAAAAAAAAA";
    private static final String UPDATED_EXTERNAL_ID = "BBBBBBBBBB";

    private static final String DEFAULT_TRANSACTION_ID = "AAAAAAAAAA";
    private static final String UPDATED_TRANSACTION_ID = "BBBBBBBBBB";

    private static final String DEFAULT_USER_ID = "AAAAAAAAAA";
    private static final String UPDATED_USER_ID = "BBBBBBBBBB";

    private static final Instant DEFAULT_EXPIRE_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_EXPIRE_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_SIGNED_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_SIGNED_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_SIGNED_DOCUMENT_URL = "AAAAAAAAAA";
    private static final String UPDATED_SIGNED_DOCUMENT_URL = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/signature-documents";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private SignatureDocumentRepository signatureDocumentRepository;

    @Autowired
    private SignatureDocumentMapper signatureDocumentMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restSignatureDocumentMockMvc;

    private SignatureDocument signatureDocument;

    private SignatureDocument insertedSignatureDocument;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static SignatureDocument createEntity() {
        return new SignatureDocument()
            .title(DEFAULT_TITLE)
            .description(DEFAULT_DESCRIPTION)
            .documentUrl(DEFAULT_DOCUMENT_URL)
            .status(DEFAULT_STATUS)
            .provider(DEFAULT_PROVIDER)
            .externalId(DEFAULT_EXTERNAL_ID)
            .transactionId(DEFAULT_TRANSACTION_ID)
            .userId(DEFAULT_USER_ID)
            .expireTime(DEFAULT_EXPIRE_TIME)
            .signedTime(DEFAULT_SIGNED_TIME)
            .signedDocumentUrl(DEFAULT_SIGNED_DOCUMENT_URL)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static SignatureDocument createUpdatedEntity() {
        return new SignatureDocument()
            .title(UPDATED_TITLE)
            .description(UPDATED_DESCRIPTION)
            .documentUrl(UPDATED_DOCUMENT_URL)
            .status(UPDATED_STATUS)
            .provider(UPDATED_PROVIDER)
            .externalId(UPDATED_EXTERNAL_ID)
            .transactionId(UPDATED_TRANSACTION_ID)
            .userId(UPDATED_USER_ID)
            .expireTime(UPDATED_EXPIRE_TIME)
            .signedTime(UPDATED_SIGNED_TIME)
            .signedDocumentUrl(UPDATED_SIGNED_DOCUMENT_URL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        signatureDocument = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedSignatureDocument != null) {
            signatureDocumentRepository.delete(insertedSignatureDocument);
            insertedSignatureDocument = null;
        }
    }

    @Test
    @Transactional
    void createSignatureDocument() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the SignatureDocument
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);
        var returnedSignatureDocumentDTO = om.readValue(
            restSignatureDocumentMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            SignatureDocumentDTO.class
        );

        // Validate the SignatureDocument in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedSignatureDocument = signatureDocumentMapper.toEntity(returnedSignatureDocumentDTO);
        assertSignatureDocumentUpdatableFieldsEquals(returnedSignatureDocument, getPersistedSignatureDocument(returnedSignatureDocument));

        insertedSignatureDocument = returnedSignatureDocument;
    }

    @Test
    @Transactional
    void createSignatureDocumentWithExistingId() throws Exception {
        // Create the SignatureDocument with an existing ID
        signatureDocument.setId(1L);
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        // Validate the SignatureDocument in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTitleIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setTitle(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDocumentUrlIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setDocumentUrl(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setStatus(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkProviderIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setProvider(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUserIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setUserId(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setVersion(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setCreatedAt(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setUpdatedAt(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        signatureDocument.setIsDeleted(null);

        // Create the SignatureDocument, which fails.
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        restSignatureDocumentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllSignatureDocuments() throws Exception {
        // Initialize the database
        insertedSignatureDocument = signatureDocumentRepository.saveAndFlush(signatureDocument);

        // Get all the signatureDocumentList
        restSignatureDocumentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(signatureDocument.getId().intValue())))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].documentUrl").value(hasItem(DEFAULT_DOCUMENT_URL)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].provider").value(hasItem(DEFAULT_PROVIDER.toString())))
            .andExpect(jsonPath("$.[*].externalId").value(hasItem(DEFAULT_EXTERNAL_ID)))
            .andExpect(jsonPath("$.[*].transactionId").value(hasItem(DEFAULT_TRANSACTION_ID)))
            .andExpect(jsonPath("$.[*].userId").value(hasItem(DEFAULT_USER_ID)))
            .andExpect(jsonPath("$.[*].expireTime").value(hasItem(DEFAULT_EXPIRE_TIME.toString())))
            .andExpect(jsonPath("$.[*].signedTime").value(hasItem(DEFAULT_SIGNED_TIME.toString())))
            .andExpect(jsonPath("$.[*].signedDocumentUrl").value(hasItem(DEFAULT_SIGNED_DOCUMENT_URL)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getSignatureDocument() throws Exception {
        // Initialize the database
        insertedSignatureDocument = signatureDocumentRepository.saveAndFlush(signatureDocument);

        // Get the signatureDocument
        restSignatureDocumentMockMvc
            .perform(get(ENTITY_API_URL_ID, signatureDocument.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(signatureDocument.getId().intValue()))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.documentUrl").value(DEFAULT_DOCUMENT_URL))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.provider").value(DEFAULT_PROVIDER.toString()))
            .andExpect(jsonPath("$.externalId").value(DEFAULT_EXTERNAL_ID))
            .andExpect(jsonPath("$.transactionId").value(DEFAULT_TRANSACTION_ID))
            .andExpect(jsonPath("$.userId").value(DEFAULT_USER_ID))
            .andExpect(jsonPath("$.expireTime").value(DEFAULT_EXPIRE_TIME.toString()))
            .andExpect(jsonPath("$.signedTime").value(DEFAULT_SIGNED_TIME.toString()))
            .andExpect(jsonPath("$.signedDocumentUrl").value(DEFAULT_SIGNED_DOCUMENT_URL))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingSignatureDocument() throws Exception {
        // Get the signatureDocument
        restSignatureDocumentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingSignatureDocument() throws Exception {
        // Initialize the database
        insertedSignatureDocument = signatureDocumentRepository.saveAndFlush(signatureDocument);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the signatureDocument
        SignatureDocument updatedSignatureDocument = signatureDocumentRepository.findById(signatureDocument.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedSignatureDocument are not directly saved in db
        em.detach(updatedSignatureDocument);
        updatedSignatureDocument
            .title(UPDATED_TITLE)
            .description(UPDATED_DESCRIPTION)
            .documentUrl(UPDATED_DOCUMENT_URL)
            .status(UPDATED_STATUS)
            .provider(UPDATED_PROVIDER)
            .externalId(UPDATED_EXTERNAL_ID)
            .transactionId(UPDATED_TRANSACTION_ID)
            .userId(UPDATED_USER_ID)
            .expireTime(UPDATED_EXPIRE_TIME)
            .signedTime(UPDATED_SIGNED_TIME)
            .signedDocumentUrl(UPDATED_SIGNED_DOCUMENT_URL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(updatedSignatureDocument);

        restSignatureDocumentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, signatureDocumentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(signatureDocumentDTO))
            )
            .andExpect(status().isOk());

        // Validate the SignatureDocument in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedSignatureDocumentToMatchAllProperties(updatedSignatureDocument);
    }

    @Test
    @Transactional
    void putNonExistingSignatureDocument() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        signatureDocument.setId(longCount.incrementAndGet());

        // Create the SignatureDocument
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restSignatureDocumentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, signatureDocumentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(signatureDocumentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the SignatureDocument in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchSignatureDocument() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        signatureDocument.setId(longCount.incrementAndGet());

        // Create the SignatureDocument
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restSignatureDocumentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(signatureDocumentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the SignatureDocument in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamSignatureDocument() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        signatureDocument.setId(longCount.incrementAndGet());

        // Create the SignatureDocument
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restSignatureDocumentMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the SignatureDocument in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateSignatureDocumentWithPatch() throws Exception {
        // Initialize the database
        insertedSignatureDocument = signatureDocumentRepository.saveAndFlush(signatureDocument);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the signatureDocument using partial update
        SignatureDocument partialUpdatedSignatureDocument = new SignatureDocument();
        partialUpdatedSignatureDocument.setId(signatureDocument.getId());

        partialUpdatedSignatureDocument
            .title(UPDATED_TITLE)
            .description(UPDATED_DESCRIPTION)
            .provider(UPDATED_PROVIDER)
            .transactionId(UPDATED_TRANSACTION_ID)
            .signedDocumentUrl(UPDATED_SIGNED_DOCUMENT_URL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .updatedBy(UPDATED_UPDATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restSignatureDocumentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedSignatureDocument.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedSignatureDocument))
            )
            .andExpect(status().isOk());

        // Validate the SignatureDocument in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertSignatureDocumentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedSignatureDocument, signatureDocument),
            getPersistedSignatureDocument(signatureDocument)
        );
    }

    @Test
    @Transactional
    void fullUpdateSignatureDocumentWithPatch() throws Exception {
        // Initialize the database
        insertedSignatureDocument = signatureDocumentRepository.saveAndFlush(signatureDocument);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the signatureDocument using partial update
        SignatureDocument partialUpdatedSignatureDocument = new SignatureDocument();
        partialUpdatedSignatureDocument.setId(signatureDocument.getId());

        partialUpdatedSignatureDocument
            .title(UPDATED_TITLE)
            .description(UPDATED_DESCRIPTION)
            .documentUrl(UPDATED_DOCUMENT_URL)
            .status(UPDATED_STATUS)
            .provider(UPDATED_PROVIDER)
            .externalId(UPDATED_EXTERNAL_ID)
            .transactionId(UPDATED_TRANSACTION_ID)
            .userId(UPDATED_USER_ID)
            .expireTime(UPDATED_EXPIRE_TIME)
            .signedTime(UPDATED_SIGNED_TIME)
            .signedDocumentUrl(UPDATED_SIGNED_DOCUMENT_URL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restSignatureDocumentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedSignatureDocument.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedSignatureDocument))
            )
            .andExpect(status().isOk());

        // Validate the SignatureDocument in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertSignatureDocumentUpdatableFieldsEquals(
            partialUpdatedSignatureDocument,
            getPersistedSignatureDocument(partialUpdatedSignatureDocument)
        );
    }

    @Test
    @Transactional
    void patchNonExistingSignatureDocument() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        signatureDocument.setId(longCount.incrementAndGet());

        // Create the SignatureDocument
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restSignatureDocumentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, signatureDocumentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(signatureDocumentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the SignatureDocument in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchSignatureDocument() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        signatureDocument.setId(longCount.incrementAndGet());

        // Create the SignatureDocument
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restSignatureDocumentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(signatureDocumentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the SignatureDocument in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamSignatureDocument() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        signatureDocument.setId(longCount.incrementAndGet());

        // Create the SignatureDocument
        SignatureDocumentDTO signatureDocumentDTO = signatureDocumentMapper.toDto(signatureDocument);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restSignatureDocumentMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(signatureDocumentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the SignatureDocument in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteSignatureDocument() throws Exception {
        // Initialize the database
        insertedSignatureDocument = signatureDocumentRepository.saveAndFlush(signatureDocument);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the signatureDocument
        restSignatureDocumentMockMvc
            .perform(delete(ENTITY_API_URL_ID, signatureDocument.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return signatureDocumentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected SignatureDocument getPersistedSignatureDocument(SignatureDocument signatureDocument) {
        return signatureDocumentRepository.findById(signatureDocument.getId()).orElseThrow();
    }

    protected void assertPersistedSignatureDocumentToMatchAllProperties(SignatureDocument expectedSignatureDocument) {
        assertSignatureDocumentAllPropertiesEquals(expectedSignatureDocument, getPersistedSignatureDocument(expectedSignatureDocument));
    }

    protected void assertPersistedSignatureDocumentToMatchUpdatableProperties(SignatureDocument expectedSignatureDocument) {
        assertSignatureDocumentAllUpdatablePropertiesEquals(
            expectedSignatureDocument,
            getPersistedSignatureDocument(expectedSignatureDocument)
        );
    }
}
