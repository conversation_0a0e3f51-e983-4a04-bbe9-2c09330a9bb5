package com.whiskerguard.general.web.rest;

import static com.whiskerguard.general.domain.SensitiveWordAsserts.*;
import static com.whiskerguard.general.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.SensitiveWord;
import com.whiskerguard.general.domain.enumeration.LanguageType;
import com.whiskerguard.general.domain.enumeration.SensitiveCategory;
import com.whiskerguard.general.domain.enumeration.SeverityType;
import com.whiskerguard.general.repository.SensitiveWordRepository;
import com.whiskerguard.general.service.dto.SensitiveWordDTO;
import com.whiskerguard.general.service.mapper.SensitiveWordMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link SensitiveWordResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class SensitiveWordResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_TERM = "AAAAAAAAAA";
    private static final String UPDATED_TERM = "BBBBBBBBBB";

    private static final LanguageType DEFAULT_LANG = LanguageType.ZH;
    private static final LanguageType UPDATED_LANG = LanguageType.EN;

    private static final SensitiveCategory DEFAULT_CATEGORY = SensitiveCategory.POLITICAL;
    private static final SensitiveCategory UPDATED_CATEGORY = SensitiveCategory.VIOLENCE;

    private static final SeverityType DEFAULT_SEVERITY = SeverityType.BLOCK;
    private static final SeverityType UPDATED_SEVERITY = SeverityType.REVIEW;

    private static final Instant DEFAULT_VALID_FROM = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_VALID_FROM = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_VALID_TO = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_VALID_TO = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_NOTES = "AAAAAAAAAA";
    private static final String UPDATED_NOTES = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/sensitive-words";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private SensitiveWordRepository sensitiveWordRepository;

    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restSensitiveWordMockMvc;

    private SensitiveWord sensitiveWord;

    private SensitiveWord insertedSensitiveWord;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static SensitiveWord createEntity() {
        return new SensitiveWord()
            .tenantId(DEFAULT_TENANT_ID)
            .term(DEFAULT_TERM)
            .lang(DEFAULT_LANG)
            .category(DEFAULT_CATEGORY)
            .severity(DEFAULT_SEVERITY)
            .validFrom(DEFAULT_VALID_FROM)
            .validTo(DEFAULT_VALID_TO)
            .notes(DEFAULT_NOTES)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static SensitiveWord createUpdatedEntity() {
        return new SensitiveWord()
            .tenantId(UPDATED_TENANT_ID)
            .term(UPDATED_TERM)
            .lang(UPDATED_LANG)
            .category(UPDATED_CATEGORY)
            .severity(UPDATED_SEVERITY)
            .validFrom(UPDATED_VALID_FROM)
            .validTo(UPDATED_VALID_TO)
            .notes(UPDATED_NOTES)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        sensitiveWord = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedSensitiveWord != null) {
            sensitiveWordRepository.delete(insertedSensitiveWord);
            insertedSensitiveWord = null;
        }
    }

    @Test
    @Transactional
    void createSensitiveWord() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the SensitiveWord
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);
        var returnedSensitiveWordDTO = om.readValue(
            restSensitiveWordMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            SensitiveWordDTO.class
        );

        // Validate the SensitiveWord in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedSensitiveWord = sensitiveWordMapper.toEntity(returnedSensitiveWordDTO);
        assertSensitiveWordUpdatableFieldsEquals(returnedSensitiveWord, getPersistedSensitiveWord(returnedSensitiveWord));

        insertedSensitiveWord = returnedSensitiveWord;
    }

    @Test
    @Transactional
    void createSensitiveWordWithExistingId() throws Exception {
        // Create the SensitiveWord with an existing ID
        sensitiveWord.setId(1L);
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        // Validate the SensitiveWord in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setTenantId(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTermIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setTerm(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkLangIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setLang(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCategoryIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setCategory(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkSeverityIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setSeverity(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkValidFromIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setValidFrom(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkValidToIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setValidTo(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setVersion(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setCreatedAt(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setUpdatedAt(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        sensitiveWord.setIsDeleted(null);

        // Create the SensitiveWord, which fails.
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        restSensitiveWordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllSensitiveWords() throws Exception {
        // Initialize the database
        insertedSensitiveWord = sensitiveWordRepository.saveAndFlush(sensitiveWord);

        // Get all the sensitiveWordList
        restSensitiveWordMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(sensitiveWord.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].term").value(hasItem(DEFAULT_TERM)))
            .andExpect(jsonPath("$.[*].lang").value(hasItem(DEFAULT_LANG.toString())))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY.toString())))
            .andExpect(jsonPath("$.[*].severity").value(hasItem(DEFAULT_SEVERITY.toString())))
            .andExpect(jsonPath("$.[*].validFrom").value(hasItem(DEFAULT_VALID_FROM.toString())))
            .andExpect(jsonPath("$.[*].validTo").value(hasItem(DEFAULT_VALID_TO.toString())))
            .andExpect(jsonPath("$.[*].notes").value(hasItem(DEFAULT_NOTES)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getSensitiveWord() throws Exception {
        // Initialize the database
        insertedSensitiveWord = sensitiveWordRepository.saveAndFlush(sensitiveWord);

        // Get the sensitiveWord
        restSensitiveWordMockMvc
            .perform(get(ENTITY_API_URL_ID, sensitiveWord.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(sensitiveWord.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.term").value(DEFAULT_TERM))
            .andExpect(jsonPath("$.lang").value(DEFAULT_LANG.toString()))
            .andExpect(jsonPath("$.category").value(DEFAULT_CATEGORY.toString()))
            .andExpect(jsonPath("$.severity").value(DEFAULT_SEVERITY.toString()))
            .andExpect(jsonPath("$.validFrom").value(DEFAULT_VALID_FROM.toString()))
            .andExpect(jsonPath("$.validTo").value(DEFAULT_VALID_TO.toString()))
            .andExpect(jsonPath("$.notes").value(DEFAULT_NOTES))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingSensitiveWord() throws Exception {
        // Get the sensitiveWord
        restSensitiveWordMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingSensitiveWord() throws Exception {
        // Initialize the database
        insertedSensitiveWord = sensitiveWordRepository.saveAndFlush(sensitiveWord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the sensitiveWord
        SensitiveWord updatedSensitiveWord = sensitiveWordRepository.findById(sensitiveWord.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedSensitiveWord are not directly saved in db
        em.detach(updatedSensitiveWord);
        updatedSensitiveWord
            .tenantId(UPDATED_TENANT_ID)
            .term(UPDATED_TERM)
            .lang(UPDATED_LANG)
            .category(UPDATED_CATEGORY)
            .severity(UPDATED_SEVERITY)
            .validFrom(UPDATED_VALID_FROM)
            .validTo(UPDATED_VALID_TO)
            .notes(UPDATED_NOTES)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(updatedSensitiveWord);

        restSensitiveWordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, sensitiveWordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(sensitiveWordDTO))
            )
            .andExpect(status().isOk());

        // Validate the SensitiveWord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedSensitiveWordToMatchAllProperties(updatedSensitiveWord);
    }

    @Test
    @Transactional
    void putNonExistingSensitiveWord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        sensitiveWord.setId(longCount.incrementAndGet());

        // Create the SensitiveWord
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restSensitiveWordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, sensitiveWordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(sensitiveWordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the SensitiveWord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchSensitiveWord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        sensitiveWord.setId(longCount.incrementAndGet());

        // Create the SensitiveWord
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restSensitiveWordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(sensitiveWordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the SensitiveWord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamSensitiveWord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        sensitiveWord.setId(longCount.incrementAndGet());

        // Create the SensitiveWord
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restSensitiveWordMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the SensitiveWord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateSensitiveWordWithPatch() throws Exception {
        // Initialize the database
        insertedSensitiveWord = sensitiveWordRepository.saveAndFlush(sensitiveWord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the sensitiveWord using partial update
        SensitiveWord partialUpdatedSensitiveWord = new SensitiveWord();
        partialUpdatedSensitiveWord.setId(sensitiveWord.getId());

        partialUpdatedSensitiveWord
            .term(UPDATED_TERM)
            .severity(UPDATED_SEVERITY)
            .validTo(UPDATED_VALID_TO)
            .createdBy(UPDATED_CREATED_BY)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restSensitiveWordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedSensitiveWord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedSensitiveWord))
            )
            .andExpect(status().isOk());

        // Validate the SensitiveWord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertSensitiveWordUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedSensitiveWord, sensitiveWord),
            getPersistedSensitiveWord(sensitiveWord)
        );
    }

    @Test
    @Transactional
    void fullUpdateSensitiveWordWithPatch() throws Exception {
        // Initialize the database
        insertedSensitiveWord = sensitiveWordRepository.saveAndFlush(sensitiveWord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the sensitiveWord using partial update
        SensitiveWord partialUpdatedSensitiveWord = new SensitiveWord();
        partialUpdatedSensitiveWord.setId(sensitiveWord.getId());

        partialUpdatedSensitiveWord
            .tenantId(UPDATED_TENANT_ID)
            .term(UPDATED_TERM)
            .lang(UPDATED_LANG)
            .category(UPDATED_CATEGORY)
            .severity(UPDATED_SEVERITY)
            .validFrom(UPDATED_VALID_FROM)
            .validTo(UPDATED_VALID_TO)
            .notes(UPDATED_NOTES)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restSensitiveWordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedSensitiveWord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedSensitiveWord))
            )
            .andExpect(status().isOk());

        // Validate the SensitiveWord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertSensitiveWordUpdatableFieldsEquals(partialUpdatedSensitiveWord, getPersistedSensitiveWord(partialUpdatedSensitiveWord));
    }

    @Test
    @Transactional
    void patchNonExistingSensitiveWord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        sensitiveWord.setId(longCount.incrementAndGet());

        // Create the SensitiveWord
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restSensitiveWordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, sensitiveWordDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(sensitiveWordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the SensitiveWord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchSensitiveWord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        sensitiveWord.setId(longCount.incrementAndGet());

        // Create the SensitiveWord
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restSensitiveWordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(sensitiveWordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the SensitiveWord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamSensitiveWord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        sensitiveWord.setId(longCount.incrementAndGet());

        // Create the SensitiveWord
        SensitiveWordDTO sensitiveWordDTO = sensitiveWordMapper.toDto(sensitiveWord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restSensitiveWordMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(sensitiveWordDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the SensitiveWord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteSensitiveWord() throws Exception {
        // Initialize the database
        insertedSensitiveWord = sensitiveWordRepository.saveAndFlush(sensitiveWord);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the sensitiveWord
        restSensitiveWordMockMvc
            .perform(delete(ENTITY_API_URL_ID, sensitiveWord.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return sensitiveWordRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected SensitiveWord getPersistedSensitiveWord(SensitiveWord sensitiveWord) {
        return sensitiveWordRepository.findById(sensitiveWord.getId()).orElseThrow();
    }

    protected void assertPersistedSensitiveWordToMatchAllProperties(SensitiveWord expectedSensitiveWord) {
        assertSensitiveWordAllPropertiesEquals(expectedSensitiveWord, getPersistedSensitiveWord(expectedSensitiveWord));
    }

    protected void assertPersistedSensitiveWordToMatchUpdatableProperties(SensitiveWord expectedSensitiveWord) {
        assertSensitiveWordAllUpdatablePropertiesEquals(expectedSensitiveWord, getPersistedSensitiveWord(expectedSensitiveWord));
    }
}
