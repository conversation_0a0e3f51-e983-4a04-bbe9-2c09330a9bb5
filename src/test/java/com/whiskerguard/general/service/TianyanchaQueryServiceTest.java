package com.whiskerguard.general.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.client.TianyanchaApiClient;
import com.whiskerguard.general.config.TianyanchaProperties;
import com.whiskerguard.general.domain.Company;
import com.whiskerguard.general.service.dto.CompanyDTO;
import com.whiskerguard.general.service.dto.CompanyVerificationResultDTO;
import com.whiskerguard.general.service.dto.TianyanchaCompanyBasicInfoResponseDTO;
import com.whiskerguard.general.service.dto.TianyanchaCompanyVerificationResponseDTO;
import com.whiskerguard.general.service.exception.TianyanchaApiException;
import com.whiskerguard.general.service.mapper.CompanyMapper;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link TianyanchaQueryService}.
 */
@ExtendWith(MockitoExtension.class)
public class TianyanchaQueryServiceTest {

    @Mock
    private TianyanchaApiClient tianyanchaApiClient;

    @Mock
    private CompanyService companyService;

    @Mock
    private CompanyMapper companyMapper;

    @Mock
    private TianyanchaProperties tianyanchaProperties;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private TianyanchaQueryService tianyanchaQueryService;

    private Company company;
    private CompanyDTO companyDTO;
    private TianyanchaCompanyBasicInfoResponseDTO apiResponse;

    @BeforeEach
    void setUp() {
        // Create a sample company for testing
        company = new Company()
            .id(1L)
            .tianyanchaId(11684584L)
            .name("中航重机股份有限公司")
            .unifiedSocialCreditCode("91520000214434146R")
            .legalPersonName("姬苏春")
            .regStatus("存续")
            .regCapital("77800.32万人民币")
            .companyOrgType("其他股份有限公司(上市)")
            .industry("汽车制造业")
            .regLocation("贵州双龙航空港经济区机场路9号太升国际A栋3单元5层")
            .businessScope("汽车零部件制造")
            .regNumber("520000000012345")
            .taxNumber("91520000214434146R");
        companyDTO = new CompanyDTO();
        companyDTO.setId(company.getId());
        companyDTO.setName(company.getName());
        companyDTO.setUnifiedSocialCreditCode(company.getUnifiedSocialCreditCode());

        apiResponse = new TianyanchaCompanyBasicInfoResponseDTO();
        apiResponse.setErrorCode(0);
        apiResponse.setReason("ok");

        // Create a mock result object
        TianyanchaCompanyBasicInfoResponseDTO.CompanyBasicInfo result = new TianyanchaCompanyBasicInfoResponseDTO.CompanyBasicInfo();
        result.setId(company.getTianyanchaId());
        result.setName(company.getName());
        result.setCreditCode(company.getUnifiedSocialCreditCode());
        apiResponse.setResult(result);
    }

    @Test
    void testGetCompanyBasicInfo_CacheHit() {
        // Given
        String keyword = "中航重机股份有限公司";
        company.setCacheTime(Instant.now().minus(1, ChronoUnit.HOURS));

        when(companyService.findByAnyIdentifier(keyword)).thenReturn(Optional.of(company));
        when(tianyanchaProperties.getCacheExpirationHours()).thenReturn(24);
        when(companyService.isDataStale(company, 24)).thenReturn(false);
        when(companyMapper.toDto(company)).thenReturn(companyDTO);

        // When
        CompanyDTO result = tianyanchaQueryService.getCompanyBasicInfo(keyword);

        // Then
        assertThat(result).isEqualTo(companyDTO);
        verify(tianyanchaApiClient, never()).getCompanyBasicInfo(anyString());
    }

    @Test
    void testGetCompanyBasicInfo_CacheMiss() {
        // Given
        String keyword = "中航重机股份有限公司";

        when(companyService.findByAnyIdentifier(keyword)).thenReturn(Optional.empty());
        when(tianyanchaApiClient.getCompanyBasicInfo(keyword)).thenReturn(apiResponse);
        when(companyMapper.fromTianyanchaResponse(apiResponse)).thenReturn(company);
        when(companyService.save(company)).thenReturn(company);
        when(companyMapper.toDto(company)).thenReturn(companyDTO);

        // When
        CompanyDTO result = tianyanchaQueryService.getCompanyBasicInfo(keyword);

        // Then
        assertThat(result).isEqualTo(companyDTO);
        verify(tianyanchaApiClient).getCompanyBasicInfo(keyword);
        verify(companyService).save(company);
    }

    @Test
    void testGetCompanyBasicInfo_StaleCache() {
        // Given
        String keyword = "中航重机股份有限公司";
        company.setCacheTime(Instant.now().minus(25, ChronoUnit.HOURS));

        when(companyService.findByAnyIdentifier(keyword)).thenReturn(Optional.of(company));
        when(tianyanchaProperties.getCacheExpirationHours()).thenReturn(24);
        when(companyService.isDataStale(company, 24)).thenReturn(true);
        when(tianyanchaApiClient.getCompanyBasicInfo(keyword)).thenReturn(apiResponse);
        when(companyMapper.fromTianyanchaResponse(apiResponse)).thenReturn(company);
        when(companyService.save(company)).thenReturn(company);
        when(companyMapper.toDto(company)).thenReturn(companyDTO);

        // When
        CompanyDTO result = tianyanchaQueryService.getCompanyBasicInfo(keyword);

        // Then
        assertThat(result).isEqualTo(companyDTO);
        verify(tianyanchaApiClient).getCompanyBasicInfo(keyword);
        verify(companyService).save(company);
    }

    @Test
    void testGetCompanyBasicInfo_ApiException() {
        // Given
        String keyword = "nonexistent";

        when(companyService.findByAnyIdentifier(keyword)).thenReturn(Optional.empty());
        when(tianyanchaApiClient.getCompanyBasicInfo(keyword)).thenThrow(new TianyanchaApiException("Company not found"));

        // When & Then
        assertThatThrownBy(() -> tianyanchaQueryService.getCompanyBasicInfo(keyword))
            .isInstanceOf(TianyanchaApiException.class)
            .hasMessageContaining("Company not found");
    }

    @Test
    void testGetCompanyBasicInfo_NullResult() {
        // Given
        String keyword = "test";
        apiResponse.setResult(null);

        when(companyService.findByAnyIdentifier(keyword)).thenReturn(Optional.empty());
        when(tianyanchaApiClient.getCompanyBasicInfo(keyword)).thenReturn(apiResponse);

        // When & Then
        assertThatThrownBy(() -> tianyanchaQueryService.getCompanyBasicInfo(keyword))
            .isInstanceOf(TianyanchaApiException.class)
            .hasMessageContaining("No company data found");
    }

    @Test
    void testVerifyCompanyThreeElements() {
        // Given
        String companyName = "中航重机股份有限公司";
        String creditCode = "91520000214434146R";
        String legalPersonName = "姬苏春";

        TianyanchaCompanyVerificationResponseDTO verificationResponse = new TianyanchaCompanyVerificationResponseDTO();
        verificationResponse.setErrorCode(0);
        verificationResponse.setReason("ok");

        TianyanchaCompanyVerificationResponseDTO.VerificationResult verificationResult =
            new TianyanchaCompanyVerificationResponseDTO.VerificationResult();
        verificationResult.setVerifyResult("一致");
        verificationResponse.setResult(verificationResult);

        when(tianyanchaApiClient.verifyCompanyThreeElements(companyName, creditCode, legalPersonName)).thenReturn(verificationResponse);

        // When
        CompanyVerificationResultDTO result = tianyanchaQueryService.verifyCompanyThreeElements(companyName, creditCode, legalPersonName);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getVerifyResult()).isEqualTo("一致");
        assertThat(result.getCompanyName()).isEqualTo(companyName);
        assertThat(result.getCreditCode()).isEqualTo(creditCode);
        assertThat(result.getLegalPersonName()).isEqualTo(legalPersonName);
    }

    @Test
    void testRefreshCompanyData() {
        // Given
        String keyword = "中航重机股份有限公司";

        when(tianyanchaApiClient.getCompanyBasicInfo(keyword)).thenReturn(apiResponse);
        when(companyMapper.fromTianyanchaResponse(apiResponse)).thenReturn(company);
        when(companyService.findByAnyIdentifier(keyword)).thenReturn(Optional.empty());
        when(companyService.save(company)).thenReturn(company);
        when(companyMapper.toDto(company)).thenReturn(companyDTO);

        // When
        CompanyDTO result = tianyanchaQueryService.refreshCompanyData(keyword);

        // Then
        assertThat(result).isEqualTo(companyDTO);
        verify(tianyanchaApiClient).getCompanyBasicInfo(keyword);
        verify(companyService).save(company);
    }

    @Test
    void testGetCompanyRisk() {
        // Given
        String keyword = "test";
        String expectedJson = "{\"errorCode\":0,\"reason\":\"ok\",\"result\":{}}";

        when(tianyanchaApiClient.getCompanyInfo("risk/company", keyword)).thenReturn(expectedJson);

        // When
        String result = tianyanchaQueryService.getCompanyRisk(keyword);

        // Then
        assertThat(result).isEqualTo(expectedJson);
        verify(tianyanchaApiClient).getCompanyInfo("risk/company", keyword);
    }
}
