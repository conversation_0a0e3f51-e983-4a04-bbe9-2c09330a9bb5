package com.whiskerguard.general.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class SignatureDocumentDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(SignatureDocumentDTO.class);
        SignatureDocumentDTO signatureDocumentDTO1 = new SignatureDocumentDTO();
        signatureDocumentDTO1.setId(1L);
        SignatureDocumentDTO signatureDocumentDTO2 = new SignatureDocumentDTO();
        assertThat(signatureDocumentDTO1).isNotEqualTo(signatureDocumentDTO2);
        signatureDocumentDTO2.setId(signatureDocumentDTO1.getId());
        assertThat(signatureDocumentDTO1).isEqualTo(signatureDocumentDTO2);
        signatureDocumentDTO2.setId(2L);
        assertThat(signatureDocumentDTO1).isNotEqualTo(signatureDocumentDTO2);
        signatureDocumentDTO1.setId(null);
        assertThat(signatureDocumentDTO1).isNotEqualTo(signatureDocumentDTO2);
    }
}
