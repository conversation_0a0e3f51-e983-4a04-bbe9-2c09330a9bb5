package com.whiskerguard.general.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class UserNotificationPreferenceDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(UserNotificationPreferenceDTO.class);
        UserNotificationPreferenceDTO userNotificationPreferenceDTO1 = new UserNotificationPreferenceDTO();
        userNotificationPreferenceDTO1.setId(1L);
        UserNotificationPreferenceDTO userNotificationPreferenceDTO2 = new UserNotificationPreferenceDTO();
        assertThat(userNotificationPreferenceDTO1).isNotEqualTo(userNotificationPreferenceDTO2);
        userNotificationPreferenceDTO2.setId(userNotificationPreferenceDTO1.getId());
        assertThat(userNotificationPreferenceDTO1).isEqualTo(userNotificationPreferenceDTO2);
        userNotificationPreferenceDTO2.setId(2L);
        assertThat(userNotificationPreferenceDTO1).isNotEqualTo(userNotificationPreferenceDTO2);
        userNotificationPreferenceDTO1.setId(null);
        assertThat(userNotificationPreferenceDTO1).isNotEqualTo(userNotificationPreferenceDTO2);
    }
}
