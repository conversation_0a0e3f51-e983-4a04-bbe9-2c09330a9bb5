package com.whiskerguard.general.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NotificationRecordDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(NotificationRecordDTO.class);
        NotificationRecordDTO notificationRecordDTO1 = new NotificationRecordDTO();
        notificationRecordDTO1.setId(1L);
        NotificationRecordDTO notificationRecordDTO2 = new NotificationRecordDTO();
        assertThat(notificationRecordDTO1).isNotEqualTo(notificationRecordDTO2);
        notificationRecordDTO2.setId(notificationRecordDTO1.getId());
        assertThat(notificationRecordDTO1).isEqualTo(notificationRecordDTO2);
        notificationRecordDTO2.setId(2L);
        assertThat(notificationRecordDTO1).isNotEqualTo(notificationRecordDTO2);
        notificationRecordDTO1.setId(null);
        assertThat(notificationRecordDTO1).isNotEqualTo(notificationRecordDTO2);
    }
}
