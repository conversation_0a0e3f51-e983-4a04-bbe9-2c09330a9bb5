package com.whiskerguard.general.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NotificationSendRecordDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(NotificationSendRecordDTO.class);
        NotificationSendRecordDTO notificationSendRecordDTO1 = new NotificationSendRecordDTO();
        notificationSendRecordDTO1.setId(1L);
        NotificationSendRecordDTO notificationSendRecordDTO2 = new NotificationSendRecordDTO();
        assertThat(notificationSendRecordDTO1).isNotEqualTo(notificationSendRecordDTO2);
        notificationSendRecordDTO2.setId(notificationSendRecordDTO1.getId());
        assertThat(notificationSendRecordDTO1).isEqualTo(notificationSendRecordDTO2);
        notificationSendRecordDTO2.setId(2L);
        assertThat(notificationSendRecordDTO1).isNotEqualTo(notificationSendRecordDTO2);
        notificationSendRecordDTO1.setId(null);
        assertThat(notificationSendRecordDTO1).isNotEqualTo(notificationSendRecordDTO2);
    }
}
