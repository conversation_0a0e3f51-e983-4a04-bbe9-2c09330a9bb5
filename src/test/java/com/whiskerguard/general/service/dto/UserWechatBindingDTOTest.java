package com.whiskerguard.general.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class UserWechatBindingDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(UserWechatBindingDTO.class);
        UserWechatBindingDTO userWechatBindingDTO1 = new UserWechatBindingDTO();
        userWechatBindingDTO1.setId(1L);
        UserWechatBindingDTO userWechatBindingDTO2 = new UserWechatBindingDTO();
        assertThat(userWechatBindingDTO1).isNotEqualTo(userWechatBindingDTO2);
        userWechatBindingDTO2.setId(userWechatBindingDTO1.getId());
        assertThat(userWechatBindingDTO1).isEqualTo(userWechatBindingDTO2);
        userWechatBindingDTO2.setId(2L);
        assertThat(userWechatBindingDTO1).isNotEqualTo(userWechatBindingDTO2);
        userWechatBindingDTO1.setId(null);
        assertThat(userWechatBindingDTO1).isNotEqualTo(userWechatBindingDTO2);
    }
}
