package com.whiskerguard.general.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import java.time.Instant;
import org.junit.jupiter.api.Test;

public class CompanyDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(CompanyDTO.class);
        CompanyDTO companyDTO1 = new CompanyDTO();
        companyDTO1.setId(1L);
        CompanyDTO companyDTO2 = new CompanyDTO();
        assertThat(companyDTO1).isNotEqualTo(companyDTO2);
        companyDTO2.setId(companyDTO1.getId());
        assertThat(companyDTO1).isEqualTo(companyDTO2);
        companyDTO2.setId(2L);
        assertThat(companyDTO1).isNotEqualTo(companyDTO2);
        companyDTO1.setId(null);
        assertThat(companyDTO1).isNotEqualTo(companyDTO2);
    }

    @Test
    void testGettersAndSetters() {
        // Given
        CompanyDTO companyDTO = new CompanyDTO();
        Long id = 1L;
        Long tianyanchaId = 11684584L;
        String name = "中航重机股份有限公司";
        String unifiedSocialCreditCode = "91520000214434146R";
        String legalPersonName = "姬苏春";
        String regStatus = "存续";
        String regCapital = "77800.32万人民币";
        Instant establishTime = Instant.parse("1996-11-20T00:00:00Z");
        String companyOrgType = "其他股份有限公司(上市)";
        String industry = "汽车制造业";
        String regLocation = "贵州双龙航空港经济区机场路9号太升国际A栋3单元5层";
        String businessScope = "汽车零部件制造";
        String regNumber = "520000000012345";
        String taxNumber = "91520000214434146R";
        Instant cacheTime = Instant.now();

        // When
        companyDTO.setId(id);
        companyDTO.setTianyanchaId(tianyanchaId);
        companyDTO.setName(name);
        companyDTO.setUnifiedSocialCreditCode(unifiedSocialCreditCode);
        companyDTO.setLegalPersonName(legalPersonName);
        companyDTO.setRegStatus(regStatus);
        companyDTO.setRegCapital(regCapital);
        companyDTO.setEstablishTime(establishTime);
        companyDTO.setCompanyOrgType(companyOrgType);
        companyDTO.setIndustry(industry);
        companyDTO.setRegLocation(regLocation);
        companyDTO.setBusinessScope(businessScope);
        companyDTO.setRegNumber(regNumber);
        companyDTO.setTaxNumber(taxNumber);
        companyDTO.setCacheTime(cacheTime);

        // Then
        assertThat(companyDTO.getId()).isEqualTo(id);
        assertThat(companyDTO.getTianyanchaId()).isEqualTo(tianyanchaId);
        assertThat(companyDTO.getName()).isEqualTo(name);
        assertThat(companyDTO.getUnifiedSocialCreditCode()).isEqualTo(unifiedSocialCreditCode);
        assertThat(companyDTO.getLegalPersonName()).isEqualTo(legalPersonName);
        assertThat(companyDTO.getRegStatus()).isEqualTo(regStatus);
        assertThat(companyDTO.getRegCapital()).isEqualTo(regCapital);
        assertThat(companyDTO.getEstablishTime()).isEqualTo(establishTime);
        assertThat(companyDTO.getCompanyOrgType()).isEqualTo(companyOrgType);
        assertThat(companyDTO.getIndustry()).isEqualTo(industry);
        assertThat(companyDTO.getRegLocation()).isEqualTo(regLocation);
        assertThat(companyDTO.getBusinessScope()).isEqualTo(businessScope);
        assertThat(companyDTO.getRegNumber()).isEqualTo(regNumber);
        assertThat(companyDTO.getTaxNumber()).isEqualTo(taxNumber);
        assertThat(companyDTO.getCacheTime()).isEqualTo(cacheTime);
    }

    @Test
    void testToString() {
        // Given
        CompanyDTO companyDTO = new CompanyDTO();
        companyDTO.setId(1L);
        companyDTO.setName("Test Company");

        // When
        String toString = companyDTO.toString();

        // Then
        assertThat(toString).contains("CompanyDTO");
        assertThat(toString).contains("id=1");
        assertThat(toString).contains("name=Test Company");
    }

    @Test
    void testHashCode() {
        // Given
        CompanyDTO companyDTO1 = new CompanyDTO();
        companyDTO1.setId(1L);
        CompanyDTO companyDTO2 = new CompanyDTO();
        companyDTO2.setId(1L);

        // When & Then
        assertThat(companyDTO1.hashCode()).isEqualTo(companyDTO2.hashCode());
    }
}
