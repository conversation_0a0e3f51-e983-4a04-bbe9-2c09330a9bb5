package com.whiskerguard.general.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class SensitiveWordDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(SensitiveWordDTO.class);
        SensitiveWordDTO sensitiveWordDTO1 = new SensitiveWordDTO();
        sensitiveWordDTO1.setId(1L);
        SensitiveWordDTO sensitiveWordDTO2 = new SensitiveWordDTO();
        assertThat(sensitiveWordDTO1).isNotEqualTo(sensitiveWordDTO2);
        sensitiveWordDTO2.setId(sensitiveWordDTO1.getId());
        assertThat(sensitiveWordDTO1).isEqualTo(sensitiveWordDTO2);
        sensitiveWordDTO2.setId(2L);
        assertThat(sensitiveWordDTO1).isNotEqualTo(sensitiveWordDTO2);
        sensitiveWordDTO1.setId(null);
        assertThat(sensitiveWordDTO1).isNotEqualTo(sensitiveWordDTO2);
    }
}
