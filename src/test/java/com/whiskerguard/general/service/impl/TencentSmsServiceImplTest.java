package com.whiskerguard.general.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.NotificationType;
import com.whiskerguard.general.model.SmsProviderType;
import com.whiskerguard.general.model.SmsRequest;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * 腾讯云短信服务实现测试类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-23
 */
class TencentSmsServiceImplTest {

    @Mock
    private ApplicationProperties applicationProperties;

    @Mock
    private ApplicationProperties.Notification notification;

    @Mock
    private ApplicationProperties.Notification.Sms smsProperties;

    @Mock
    private ApplicationProperties.Notification.Sms.TencentSms tencentSmsProperties;

    private TencentSmsServiceImpl tencentSmsService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 设置Mock对象的返回值
        when(applicationProperties.getNotification()).thenReturn(notification);
        when(notification.getSms()).thenReturn(smsProperties);
        when(smsProperties.getTencent()).thenReturn(tencentSmsProperties);

        tencentSmsService = new TencentSmsServiceImpl(applicationProperties);
    }

    @Test
    void testSendSmsSuccess() {
        // 准备测试数据
        when(tencentSmsProperties.isEnabled()).thenReturn(true);
        when(tencentSmsProperties.getSecretId()).thenReturn("test-secret-id");
        when(tencentSmsProperties.getSecretKey()).thenReturn("test-secret-key");
        when(tencentSmsProperties.getAppId()).thenReturn("**********");
        when(tencentSmsProperties.getSignName()).thenReturn("测试签名");
        when(tencentSmsProperties.getRegion()).thenReturn("ap-guangzhou");

        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("1234567");
        request.setRegionCode("86");
        request.setProviderType(SmsProviderType.TENCENT);

        Map<String, Object> params = new HashMap<>();
        params.put("code", "123456");
        params.put("time", "5");
        request.setTemplateParams(params);

        // 注意：由于腾讯云SDK需要真实的网络请求，这里我们主要测试配置和参数处理逻辑
        // 实际的网络请求测试需要在集成测试中进行

        // 验证配置是否正确加载
        assertTrue(tencentSmsProperties.isEnabled());
        assertEquals("test-secret-id", tencentSmsProperties.getSecretId());
        assertEquals("test-secret-key", tencentSmsProperties.getSecretKey());
        assertEquals("**********", tencentSmsProperties.getAppId());
        assertEquals("测试签名", tencentSmsProperties.getSignName());
        assertEquals("ap-guangzhou", tencentSmsProperties.getRegion());
    }

    @Test
    void testSendSmsWhenDisabled() {
        // 设置腾讯云短信服务为禁用状态
        when(tencentSmsProperties.isEnabled()).thenReturn(false);

        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("1234567");
        request.setProviderType(SmsProviderType.TENCENT);

        // 执行测试
        NotificationResponse response = tencentSmsService.send(request);

        // 验证结果
        assertFalse(response.isSuccess());
        assertEquals("腾讯云短信服务未启用", response.getMessage());
    }

    @Test
    void testConvertMapToArrayWithValidParams() {
        // 使用反射测试私有方法 convertMapToArray
        when(tencentSmsProperties.isEnabled()).thenReturn(true);
        when(tencentSmsProperties.getSecretId()).thenReturn("test-secret-id");
        when(tencentSmsProperties.getSecretKey()).thenReturn("test-secret-key");
        when(tencentSmsProperties.getAppId()).thenReturn("**********");
        when(tencentSmsProperties.getSignName()).thenReturn("测试签名");
        when(tencentSmsProperties.getRegion()).thenReturn("ap-guangzhou");

        Map<String, Object> params = new HashMap<>();
        params.put("code", "123456");
        params.put("time", "5");
        params.put("product", "WhiskerGuard");

        // 创建请求对象来测试参数转换
        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("1234567");
        request.setTemplateParams(params);
        request.setProviderType(SmsProviderType.TENCENT);

        // 验证参数不为空
        assertNotNull(request.getTemplateParams());
        assertEquals(3, request.getTemplateParams().size());
        assertTrue(request.getTemplateParams().containsKey("code"));
        assertTrue(request.getTemplateParams().containsKey("time"));
        assertTrue(request.getTemplateParams().containsKey("product"));
    }

    @Test
    void testConvertMapToArrayWithEmptyParams() {
        // 测试空参数的情况
        when(tencentSmsProperties.isEnabled()).thenReturn(true);

        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("1234567");
        request.setTemplateParams(new HashMap<>());
        request.setProviderType(SmsProviderType.TENCENT);

        // 验证空参数处理
        assertNotNull(request.getTemplateParams());
        assertTrue(request.getTemplateParams().isEmpty());
    }

    @Test
    void testConvertMapToArrayWithNullParams() {
        // 测试null参数的情况
        when(tencentSmsProperties.isEnabled()).thenReturn(true);

        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("1234567");
        request.setTemplateParams(null);
        request.setProviderType(SmsProviderType.TENCENT);

        // 验证null参数处理
        assertNull(request.getTemplateParams());
    }

    @Test
    void testPhoneNumberFormatting() {
        // 测试手机号格式化
        when(tencentSmsProperties.isEnabled()).thenReturn(true);

        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setRegionCode("86");
        request.setTemplateId("1234567");
        request.setProviderType(SmsProviderType.TENCENT);

        // 验证手机号和区号设置
        assertEquals("13800138000", request.getRecipient());
        assertEquals("86", request.getRegionCode());

        // 预期的完整手机号格式应该是 +86 + 手机号
        String expectedPhoneNumber = "+86" + request.getRecipient();
        assertEquals("+8613800138000", expectedPhoneNumber);
    }

    @Test
    void testRequestTypeAndProvider() {
        // 测试请求类型和提供商设置
        SmsRequest request = new SmsRequest();
        request.setProviderType(SmsProviderType.TENCENT);

        // 验证请求类型和提供商
        assertEquals(NotificationType.SMS, request.getType());
        assertEquals(SmsProviderType.TENCENT, request.getProviderType());
    }

    @Test
    void testDefaultRegionCode() {
        // 测试默认区号
        SmsRequest request = new SmsRequest();

        // 验证默认区号为中国大陆 +86
        assertEquals("86", request.getRegionCode());
    }

    @Test
    void testInternationalPhoneNumber() {
        // 测试国际手机号
        when(tencentSmsProperties.isEnabled()).thenReturn(true);

        SmsRequest request = new SmsRequest();
        request.setRecipient("**********");
        request.setRegionCode("1"); // 美国区号
        request.setTemplateId("1234567");
        request.setProviderType(SmsProviderType.TENCENT);

        // 验证国际手机号格式
        assertEquals("**********", request.getRecipient());
        assertEquals("1", request.getRegionCode());

        String expectedPhoneNumber = "+1" + request.getRecipient();
        assertEquals("+1**********", expectedPhoneNumber);
    }
}
