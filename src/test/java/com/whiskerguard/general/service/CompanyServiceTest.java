package com.whiskerguard.general.service;

import static com.whiskerguard.general.domain.CompanyTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.whiskerguard.general.domain.Company;
import com.whiskerguard.general.repository.CompanyRepository;
import com.whiskerguard.general.service.dto.CompanyDTO;
import com.whiskerguard.general.service.mapper.CompanyMapper;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

/**
 * Unit tests for {@link CompanyService}.
 */
@ExtendWith(MockitoExtension.class)
public class CompanyServiceTest {

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private CompanyMapper companyMapper;

    @InjectMocks
    private CompanyService companyService;

    private Company company;
    private CompanyDTO companyDTO;

    @BeforeEach
    void setUp() {
        company = getCompanySample1();
        companyDTO = new CompanyDTO();
        companyDTO.setId(company.getId());
        companyDTO.setName(company.getName());
        companyDTO.setUnifiedSocialCreditCode(company.getUnifiedSocialCreditCode());
    }

    @Test
    void testSave() {
        // Given
        when(companyMapper.toEntity(companyDTO)).thenReturn(company);
        when(companyRepository.save(company)).thenReturn(company);
        when(companyMapper.toDto(company)).thenReturn(companyDTO);

        // When
        CompanyDTO result = companyService.save(companyDTO);

        // Then
        assertThat(result).isEqualTo(companyDTO);
        verify(companyRepository).save(company);
    }

    @Test
    void testSaveEntity() {
        // Given
        when(companyRepository.save(company)).thenReturn(company);

        // When
        Company result = companyService.save(company);

        // Then
        assertThat(result).isEqualTo(company);
        verify(companyRepository).save(company);
    }

    @Test
    void testFindAll() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<Company> companyPage = new PageImpl<>(Arrays.asList(company));
        when(companyRepository.findAll(pageable)).thenReturn(companyPage);
        when(companyMapper.toDto(company)).thenReturn(companyDTO);

        // When
        Page<CompanyDTO> result = companyService.findAll(pageable);

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0)).isEqualTo(companyDTO);
    }

    @Test
    void testFindOne() {
        // Given
        when(companyRepository.findById(1L)).thenReturn(Optional.of(company));
        when(companyMapper.toDto(company)).thenReturn(companyDTO);

        // When
        Optional<CompanyDTO> result = companyService.findOne(1L);

        // Then
        assertThat(result).isPresent();
        assertThat(result.orElseThrow()).isEqualTo(companyDTO);
    }

    @Test
    void testFindOneEntity() {
        // Given
        when(companyRepository.findById(1L)).thenReturn(Optional.of(company));

        // When
        Optional<Company> result = companyService.findOneEntity(1L);

        // Then
        assertThat(result).isPresent();
        assertThat(result.orElseThrow()).isEqualTo(company);
    }

    @Test
    void testDelete() {
        // When
        companyService.delete(1L);

        // Then
        verify(companyRepository).deleteById(1L);
    }

    @Test
    void testFindByUnifiedSocialCreditCode() {
        // Given
        String creditCode = "91520000214434146R";
        when(companyRepository.findByUnifiedSocialCreditCode(creditCode)).thenReturn(Optional.of(company));

        // When
        Optional<Company> result = companyService.findByUnifiedSocialCreditCode(creditCode);

        // Then
        assertThat(result).isPresent();
        assertThat(result.orElseThrow()).isEqualTo(company);
    }

    @Test
    void testFindByName() {
        // Given
        String name = "中航重机股份有限公司";
        when(companyRepository.findByName(name)).thenReturn(Optional.of(company));

        // When
        Optional<Company> result = companyService.findByName(name);

        // Then
        assertThat(result).isPresent();
        assertThat(result.orElseThrow()).isEqualTo(company);
    }

    @Test
    void testFindByAnyIdentifier() {
        // Given
        String keyword = "91520000214434146R";
        when(companyRepository.findByUnifiedSocialCreditCode(keyword)).thenReturn(Optional.of(company));

        // When
        Optional<Company> result = companyService.findByAnyIdentifier(keyword);

        // Then
        assertThat(result).isPresent();
        assertThat(result.orElseThrow()).isEqualTo(company);
    }

    @Test
    void testIsDataStale_Fresh() {
        // Given
        company.setCacheTime(Instant.now().minus(1, ChronoUnit.HOURS));

        // When
        boolean isStale = companyService.isDataStale(company, 24);

        // Then
        assertThat(isStale).isFalse();
    }

    @Test
    void testIsDataStale_Stale() {
        // Given
        company.setCacheTime(Instant.now().minus(25, ChronoUnit.HOURS));

        // When
        boolean isStale = companyService.isDataStale(company, 24);

        // Then
        assertThat(isStale).isTrue();
    }

    @Test
    void testIsDataStale_NullCacheTime() {
        // Given
        company.setCacheTime(null);

        // When
        boolean isStale = companyService.isDataStale(company, 24);

        // Then
        assertThat(isStale).isTrue();
    }

    @Test
    void testUpdateCacheTime() {
        // Given
        when(companyRepository.save(company)).thenReturn(company);

        // When
        companyService.updateCacheTime(company);

        // Then
        assertThat(company.getCacheTime()).isNotNull();
        verify(companyRepository).save(company);
    }
}
