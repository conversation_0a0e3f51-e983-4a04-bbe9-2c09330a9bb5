package com.whiskerguard.general.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.domain.enumeration.*;
import com.whiskerguard.general.repository.NotificationRecordRepository;
import com.whiskerguard.general.repository.NotificationSendRecordRepository;
import com.whiskerguard.general.repository.NotificationTemplateRepository;
import com.whiskerguard.general.repository.UserNotificationPreferenceRepository;
import com.whiskerguard.general.service.dto.*;
import com.whiskerguard.general.service.impl.NotificationCenterServiceImpl;
import com.whiskerguard.general.service.mapper.NotificationRecordMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 通知中心服务测试类
 *
 * <AUTHOR> Yan
 * @version 1.0
 * @date 2025-06-23
 */
@ExtendWith(MockitoExtension.class)
class NotificationCenterServiceTest {

    @Mock
    private NotificationRecordRepository notificationRecordRepository;

    @Mock
    private NotificationSendRecordRepository notificationSendRecordRepository;

    @Mock
    private NotificationTemplateRepository notificationTemplateRepository;

    @Mock
    private UserNotificationPreferenceRepository userNotificationPreferenceRepository;

    @Mock
    private NotificationRecordService notificationRecordService;

    @Mock
    private NotificationRecordMapper notificationRecordMapper;

    private ObjectMapper objectMapper;

    private NotificationCenterService notificationCenterService;

    private HttpServletRequest request;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        notificationCenterService = new NotificationCenterServiceImpl(
            notificationRecordRepository,
            notificationSendRecordRepository,
            notificationRecordService,
            notificationRecordMapper,
            objectMapper,
            request
        );
    }

    @Test
    void testCreateNotificationRequest() {
        // 测试创建通知请求DTO
        NotificationRequestDTO request = new NotificationRequestDTO();
        request.setCategory(NotificationCategory.SYSTEM);
        request.setSubType(NotificationSubType.SYSTEM_ANNOUNCEMENT);
        request.setScope(NotificationScope.TENANT);
        request.setTitle("系统公告测试");
        request.setContent("这是一个系统公告测试内容");
        request.setRecipientType(RecipientType.ALL);
        request.setRecipientIds(Arrays.asList(1L, 2L, 3L));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.NORMAL);
        request.setScheduledTime(Instant.now());
        request.setBusinessId("TEST_001");
        request.setBusinessType("TEST");

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("userName", "测试用户");
        templateParams.put("systemName", "WhiskerGuard");
        request.setTemplateParams(templateParams);

        // 验证请求对象
        assertThat(request.getCategory()).isEqualTo(NotificationCategory.SYSTEM);
        assertThat(request.getSubType()).isEqualTo(NotificationSubType.SYSTEM_ANNOUNCEMENT);
        assertThat(request.getTitle()).isEqualTo("系统公告测试");
        assertThat(request.getRecipientIds()).hasSize(3);
        assertThat(request.getChannels()).hasSize(2);
        assertThat(request.getTemplateParams()).containsKey("userName");
    }

    @Test
    void testCreateUserNotificationRequest() {
        // 测试创建用户通知请求DTO
        UserNotificationRequestDTO request = new UserNotificationRequestDTO();
        request.setUserId(12345L);
        request.setSubType(NotificationSubType.PASSWORD_CHANGED);
        request.setTitle("密码修改通知");
        request.setContent("您的密码已成功修改");
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.SMS));
        request.setPriority(NotificationPriority.HIGH);

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("changeTime", Instant.now().toString());
        templateParams.put("ipAddress", "*************");
        request.setTemplateParams(templateParams);

        // 验证请求对象
        assertThat(request.getUserId()).isEqualTo(12345L);
        assertThat(request.getSubType()).isEqualTo(NotificationSubType.PASSWORD_CHANGED);
        assertThat(request.getTitle()).isEqualTo("密码修改通知");
        assertThat(request.getChannels()).contains(NotificationType.EMAIL, NotificationType.SMS);
        assertThat(request.getPriority()).isEqualTo(NotificationPriority.HIGH);
    }

    @Test
    void testCreateTaskNotificationRequest() {
        // 测试创建任务通知请求DTO
        TaskNotificationRequestDTO request = new TaskNotificationRequestDTO();
        request.setSubType(NotificationSubType.APPROVAL_PENDING);
        request.setTitle("待审批任务");
        request.setContent("您有一个待审批的任务");
        request.setRecipients(Arrays.asList(100L, 101L, 102L));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.HIGH);
        request.setBusinessId("APPROVAL_001");
        request.setBusinessType("APPROVAL");

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("taskName", "合同审批");
        templateParams.put("applicant", "张三");
        templateParams.put("submitTime", Instant.now().toString());
        request.setTemplateParams(templateParams);

        // 验证请求对象
        assertThat(request.getSubType()).isEqualTo(NotificationSubType.APPROVAL_PENDING);
        assertThat(request.getRecipients()).hasSize(3);
        assertThat(request.getBusinessId()).isEqualTo("APPROVAL_001");
        assertThat(request.getBusinessType()).isEqualTo("APPROVAL");
        assertThat(request.getTemplateParams()).containsKey("taskName");
    }

    @Test
    void testCreateBatchNotificationRequest() {
        // 测试创建批量通知请求DTO
        BatchNotificationRequestDTO request = new BatchNotificationRequestDTO();
        request.setCategory(NotificationCategory.BUSINESS);
        request.setSubType(NotificationSubType.TRAINING_REMINDER);
        request.setTitle("培训提醒");
        request.setContent("您有一个培训即将开始");
        request.setRecipientIds(Arrays.asList(1L, 2L, 3L, 4L, 5L));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.NORMAL);
        request.setBatchSize(2);

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("trainingName", "安全培训");
        templateParams.put("startTime", Instant.now().toString());
        request.setTemplateParams(templateParams);

        // 验证请求对象
        assertThat(request.getCategory()).isEqualTo(NotificationCategory.BUSINESS);
        assertThat(request.getSubType()).isEqualTo(NotificationSubType.TRAINING_REMINDER);
        assertThat(request.getRecipientIds()).hasSize(5);
        assertThat(request.getBatchSize()).isEqualTo(2);
        assertThat(request.getTemplateParams()).containsKey("trainingName");
    }

    @Test
    void testCreateSystemNotificationRequest() {
        // 测试创建系统通知请求DTO
        SystemNotificationRequestDTO request = new SystemNotificationRequestDTO();
        request.setSubType(NotificationSubType.SYSTEM_MAINTENANCE);
        request.setScope(NotificationScope.GLOBAL);
        request.setTitle("系统维护通知");
        request.setContent("系统将于今晚进行维护");
        request.setTargetIds(Arrays.asList(1L));
        request.setChannels(Arrays.asList(NotificationType.EMAIL, NotificationType.PUSH));
        request.setPriority(NotificationPriority.HIGH);
        request.setScheduledTime(Instant.now().plusSeconds(3600)); // 1小时后

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("maintenanceTime", "2025-06-23 22:00:00");
        templateParams.put("duration", "2小时");
        request.setTemplateParams(templateParams);

        // 验证请求对象
        assertThat(request.getSubType()).isEqualTo(NotificationSubType.SYSTEM_MAINTENANCE);
        assertThat(request.getScope()).isEqualTo(NotificationScope.GLOBAL);
        assertThat(request.getTitle()).isEqualTo("系统维护通知");
        assertThat(request.getScheduledTime()).isAfter(Instant.now());
        assertThat(request.getTemplateParams()).containsKey("maintenanceTime");
    }

    @Test
    void testBatchNotificationStatusDTO() {
        // 测试批量通知状态DTO
        BatchNotificationStatusDTO status = new BatchNotificationStatusDTO();
        status.setBatchId("batch-001");
        status.setStatus("PROCESSING");
        status.setTotalCount(100);
        status.setSentCount(50);
        status.setSuccessCount(45);
        status.setFailedCount(5);
        status.setStartTime(Instant.now().minusSeconds(300));

        // 计算进度
        status.calculateProgress();

        // 验证状态对象
        assertThat(status.getBatchId()).isEqualTo("batch-001");
        assertThat(status.getStatus()).isEqualTo("PROCESSING");
        assertThat(status.getTotalCount()).isEqualTo(100);
        assertThat(status.getSentCount()).isEqualTo(50);
        assertThat(status.getProgressPercentage()).isEqualTo(50.0);
    }

    @Test
    void testNotificationEnums() {
        // 测试通知相关枚举
        assertNotNull(NotificationCategory.SYSTEM);
        assertNotNull(NotificationCategory.TASK);
        assertNotNull(NotificationCategory.USER);
        assertNotNull(NotificationCategory.BUSINESS);

        assertNotNull(NotificationSubType.SYSTEM_ANNOUNCEMENT);
        assertNotNull(NotificationSubType.APPROVAL_PENDING);
        assertNotNull(NotificationSubType.PASSWORD_CHANGED);
        assertNotNull(NotificationSubType.TRAINING_REMINDER);

        assertNotNull(NotificationScope.GLOBAL);
        assertNotNull(NotificationScope.TENANT);
        assertNotNull(NotificationScope.USER);

        assertNotNull(NotificationPriority.LOW);
        assertNotNull(NotificationPriority.NORMAL);
        assertNotNull(NotificationPriority.HIGH);
        assertNotNull(NotificationPriority.URGENT);

        assertNotNull(NotificationStatus.DRAFT);
        assertNotNull(NotificationStatus.SCHEDULED);
        assertNotNull(NotificationStatus.SENT);

        assertNotNull(RecipientType.USER);
        assertNotNull(RecipientType.ROLE);
        assertNotNull(RecipientType.DEPARTMENT);
        assertNotNull(RecipientType.ALL);
    }
}
