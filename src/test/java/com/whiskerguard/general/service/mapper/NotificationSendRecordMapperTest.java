package com.whiskerguard.general.service.mapper;

import static com.whiskerguard.general.domain.NotificationSendRecordAsserts.*;
import static com.whiskerguard.general.domain.NotificationSendRecordTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NotificationSendRecordMapperTest {

    private NotificationSendRecordMapper notificationSendRecordMapper;

    @BeforeEach
    void setUp() {
        notificationSendRecordMapper = new NotificationSendRecordMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNotificationSendRecordSample1();
        var actual = notificationSendRecordMapper.toEntity(notificationSendRecordMapper.toDto(expected));
        assertNotificationSendRecordAllPropertiesEquals(expected, actual);
    }
}
