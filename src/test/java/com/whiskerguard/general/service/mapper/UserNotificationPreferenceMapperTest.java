package com.whiskerguard.general.service.mapper;

import static com.whiskerguard.general.domain.UserNotificationPreferenceAsserts.*;
import static com.whiskerguard.general.domain.UserNotificationPreferenceTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class UserNotificationPreferenceMapperTest {

    private UserNotificationPreferenceMapper userNotificationPreferenceMapper;

    @BeforeEach
    void setUp() {
        userNotificationPreferenceMapper = new UserNotificationPreferenceMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getUserNotificationPreferenceSample1();
        var actual = userNotificationPreferenceMapper.toEntity(userNotificationPreferenceMapper.toDto(expected));
        assertUserNotificationPreferenceAllPropertiesEquals(expected, actual);
    }
}
