package com.whiskerguard.general.service.mapper;

import static com.whiskerguard.general.domain.UserWechatBindingAsserts.*;
import static com.whiskerguard.general.domain.UserWechatBindingTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class UserWechatBindingMapperTest {

    private UserWechatBindingMapper userWechatBindingMapper;

    @BeforeEach
    void setUp() {
        userWechatBindingMapper = new UserWechatBindingMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getUserWechatBindingSample1();
        var actual = userWechatBindingMapper.toEntity(userWechatBindingMapper.toDto(expected));
        assertUserWechatBindingAllPropertiesEquals(expected, actual);
    }
}
