package com.whiskerguard.general.service.mapper;

import static com.whiskerguard.general.domain.NotificationRecordAsserts.*;
import static com.whiskerguard.general.domain.NotificationRecordTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NotificationRecordMapperTest {

    private NotificationRecordMapper notificationRecordMapper;

    @BeforeEach
    void setUp() {
        notificationRecordMapper = new NotificationRecordMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNotificationRecordSample1();
        var actual = notificationRecordMapper.toEntity(notificationRecordMapper.toDto(expected));
        assertNotificationRecordAllPropertiesEquals(expected, actual);
    }
}
