package com.whiskerguard.general.service.mapper;

import static com.whiskerguard.general.domain.SensitiveWordAsserts.*;
import static com.whiskerguard.general.domain.SensitiveWordTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class SensitiveWordMapperTest {

    private SensitiveWordMapper sensitiveWordMapper;

    @BeforeEach
    void setUp() {
        sensitiveWordMapper = new SensitiveWordMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getSensitiveWordSample1();
        var actual = sensitiveWordMapper.toEntity(sensitiveWordMapper.toDto(expected));
        assertSensitiveWordAllPropertiesEquals(expected, actual);
    }
}
