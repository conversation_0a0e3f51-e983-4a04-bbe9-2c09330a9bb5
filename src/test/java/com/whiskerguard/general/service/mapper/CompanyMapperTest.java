package com.whiskerguard.general.service.mapper;

import static com.whiskerguard.general.domain.CompanyAsserts.*;
import static com.whiskerguard.general.domain.CompanyTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.IntegrationTest;
import com.whiskerguard.general.domain.Company;
import com.whiskerguard.general.service.dto.CompanyDTO;
import com.whiskerguard.general.service.dto.TianyanchaCompanyBasicInfoResponseDTO;
import java.time.Instant;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@IntegrationTest
public class CompanyMapperTest {

    @Autowired
    private CompanyMapper companyMapper;

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getCompanySample1();
        var actual = companyMapper.toEntity(companyMapper.toDto(expected));
        assertCompanyAllPropertiesEquals(expected, actual);
    }

    @Test
    void shouldMapDtoToEntity() {
        // Given
        CompanyDTO companyDTO = new CompanyDTO();
        companyDTO.setId(1L);
        companyDTO.setTianyanchaId(11684584L);
        companyDTO.setName("中航重机股份有限公司");
        companyDTO.setUnifiedSocialCreditCode("91520000214434146R");
        companyDTO.setLegalPersonName("姬苏春");
        companyDTO.setRegStatus("存续");
        companyDTO.setRegCapital("77800.32万人民币");
        companyDTO.setEstablishTime(Instant.parse("1996-11-20T00:00:00Z"));
        companyDTO.setCompanyOrgType("其他股份有限公司(上市)");
        companyDTO.setIndustry("汽车制造业");
        companyDTO.setRegLocation("贵州双龙航空港经济区机场路9号太升国际A栋3单元5层");
        companyDTO.setBusinessScope("汽车零部件制造");
        companyDTO.setRegNumber("520000000012345");
        companyDTO.setTaxNumber("91520000214434146R");
        companyDTO.setCacheTime(Instant.now());

        // When
        Company company = companyMapper.toEntity(companyDTO);

        // Then
        assertThat(company).isNotNull();
        assertThat(company.getId()).isEqualTo(companyDTO.getId());
        assertThat(company.getTianyanchaId()).isEqualTo(companyDTO.getTianyanchaId());
        assertThat(company.getName()).isEqualTo(companyDTO.getName());
        assertThat(company.getUnifiedSocialCreditCode()).isEqualTo(companyDTO.getUnifiedSocialCreditCode());
        assertThat(company.getLegalPersonName()).isEqualTo(companyDTO.getLegalPersonName());
        assertThat(company.getRegStatus()).isEqualTo(companyDTO.getRegStatus());
        assertThat(company.getRegCapital()).isEqualTo(companyDTO.getRegCapital());
        assertThat(company.getEstablishTime()).isEqualTo(companyDTO.getEstablishTime());
        assertThat(company.getCompanyOrgType()).isEqualTo(companyDTO.getCompanyOrgType());
        assertThat(company.getIndustry()).isEqualTo(companyDTO.getIndustry());
        assertThat(company.getRegLocation()).isEqualTo(companyDTO.getRegLocation());
        assertThat(company.getBusinessScope()).isEqualTo(companyDTO.getBusinessScope());
        assertThat(company.getRegNumber()).isEqualTo(companyDTO.getRegNumber());
        assertThat(company.getTaxNumber()).isEqualTo(companyDTO.getTaxNumber());
        assertThat(company.getCacheTime()).isEqualTo(companyDTO.getCacheTime());
    }

    @Test
    void shouldMapEntityToDto() {
        // Given
        Company company = getCompanySample1();

        // When
        CompanyDTO companyDTO = companyMapper.toDto(company);

        // Then
        assertThat(companyDTO).isNotNull();
        assertThat(companyDTO.getId()).isEqualTo(company.getId());
        assertThat(companyDTO.getTianyanchaId()).isEqualTo(company.getTianyanchaId());
        assertThat(companyDTO.getName()).isEqualTo(company.getName());
        assertThat(companyDTO.getUnifiedSocialCreditCode()).isEqualTo(company.getUnifiedSocialCreditCode());
        assertThat(companyDTO.getLegalPersonName()).isEqualTo(company.getLegalPersonName());
        assertThat(companyDTO.getRegStatus()).isEqualTo(company.getRegStatus());
        assertThat(companyDTO.getRegCapital()).isEqualTo(company.getRegCapital());
        assertThat(companyDTO.getEstablishTime()).isEqualTo(company.getEstablishTime());
        assertThat(companyDTO.getCompanyOrgType()).isEqualTo(company.getCompanyOrgType());
        assertThat(companyDTO.getIndustry()).isEqualTo(company.getIndustry());
        assertThat(companyDTO.getRegLocation()).isEqualTo(company.getRegLocation());
        assertThat(companyDTO.getBusinessScope()).isEqualTo(company.getBusinessScope());
        assertThat(companyDTO.getRegNumber()).isEqualTo(company.getRegNumber());
        assertThat(companyDTO.getTaxNumber()).isEqualTo(company.getTaxNumber());
        assertThat(companyDTO.getCacheTime()).isEqualTo(company.getCacheTime());
    }

    @Test
    void shouldMapFromTianyanchaResponse() {
        // Given
        TianyanchaCompanyBasicInfoResponseDTO response = new TianyanchaCompanyBasicInfoResponseDTO();
        response.setErrorCode(0);
        response.setReason("ok");

        TianyanchaCompanyBasicInfoResponseDTO.CompanyBasicInfo result = new TianyanchaCompanyBasicInfoResponseDTO.CompanyBasicInfo();
        result.setId(11684584L);
        result.setName("中航重机股份有限公司");
        result.setCreditCode("91520000214434146R");
        result.setLegalPersonName("姬苏春");
        result.setRegStatus("存续");
        result.setRegCapital("77800.32万人民币");
        result.setEstiblishTime(1996112000000L); // Unix timestamp for 1996-11-20
        result.setCompanyOrgType("其他股份有限公司(上市)");
        result.setIndustry("汽车制造业");
        result.setRegLocation("贵州双龙航空港经济区机场路9号太升国际A栋3单元5层");
        result.setBusinessScope("汽车零部件制造");
        result.setRegNumber("520000000012345");
        result.setTaxNumber("91520000214434146R");

        response.setResult(result);

        // When
        Company company = companyMapper.fromTianyanchaResponse(response);

        // Then
        assertThat(company).isNotNull();
        assertThat(company.getTianyanchaId()).isEqualTo(result.getId());
        assertThat(company.getName()).isEqualTo(result.getName());
        assertThat(company.getUnifiedSocialCreditCode()).isEqualTo(result.getCreditCode());
        assertThat(company.getLegalPersonName()).isEqualTo(result.getLegalPersonName());
        assertThat(company.getRegStatus()).isEqualTo(result.getRegStatus());
        assertThat(company.getRegCapital()).isEqualTo(result.getRegCapital());
        assertThat(company.getCompanyOrgType()).isEqualTo(result.getCompanyOrgType());
        assertThat(company.getIndustry()).isEqualTo(result.getIndustry());
        assertThat(company.getRegLocation()).isEqualTo(result.getRegLocation());
        assertThat(company.getBusinessScope()).isEqualTo(result.getBusinessScope());
        assertThat(company.getRegNumber()).isEqualTo(result.getRegNumber());
        assertThat(company.getTaxNumber()).isEqualTo(result.getTaxNumber());
        assertThat(company.getCacheTime()).isNotNull();
    }

    @Test
    void shouldHandleNullValues() {
        // Given
        CompanyDTO nullDto = null;
        Company nullEntity = null;

        // When
        Company entityFromNull = companyMapper.toEntity(nullDto);
        CompanyDTO dtoFromNull = companyMapper.toDto(nullEntity);

        // Then
        assertThat(entityFromNull).isNull();
        assertThat(dtoFromNull).isNull();
    }

    @Test
    void shouldPartialUpdate() {
        // Given
        Company existingCompany = getCompanySample1();
        CompanyDTO updateDto = new CompanyDTO();
        updateDto.setName("Updated Company Name");
        updateDto.setRegStatus("注销");

        // When
        companyMapper.partialUpdate(existingCompany, updateDto);

        // Then
        assertThat(existingCompany.getName()).isEqualTo("Updated Company Name");
        assertThat(existingCompany.getRegStatus()).isEqualTo("注销");
        // Other fields should remain unchanged
        assertThat(existingCompany.getUnifiedSocialCreditCode()).isEqualTo("91520000214434146R");
        assertThat(existingCompany.getLegalPersonName()).isEqualTo("姬苏春");
    }
}
