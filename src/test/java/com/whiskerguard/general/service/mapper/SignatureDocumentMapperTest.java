package com.whiskerguard.general.service.mapper;

import static com.whiskerguard.general.domain.SignatureDocumentAsserts.*;
import static com.whiskerguard.general.domain.SignatureDocumentTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class SignatureDocumentMapperTest {

    private SignatureDocumentMapper signatureDocumentMapper;

    @BeforeEach
    void setUp() {
        signatureDocumentMapper = new SignatureDocumentMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getSignatureDocumentSample1();
        var actual = signatureDocumentMapper.toEntity(signatureDocumentMapper.toDto(expected));
        assertSignatureDocumentAllPropertiesEquals(expected, actual);
    }
}
