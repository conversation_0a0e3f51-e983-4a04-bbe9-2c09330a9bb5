package com.whiskerguard.general.service;

import com.whiskerguard.general.model.EmailAttachment;
import com.whiskerguard.general.model.EmailRequest;
import com.whiskerguard.general.model.NotificationResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 邮件服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
class EmailServiceTest {

    @Autowired
    private EmailService emailService;

    @Test
    void testSendSimpleEmail() {
        // 测试发送简单文本邮件
        NotificationResponse response = emailService.sendSimpleEmail(
            "<EMAIL>",
            "测试邮件",
            "这是一封测试邮件"
        );

        System.out.println("简单邮件发送结果: " + response);
        // 注意：在测试环境中可能没有配置真实的邮件服务器，所以这里只测试方法调用
    }

    @Test
    void testSendHtmlEmail() {
        // 测试发送HTML邮件
        String htmlContent = """
            <html>
            <body>
                <h1>测试HTML邮件</h1>
                <p>这是一封<strong>HTML格式</strong>的测试邮件。</p>
                <ul>
                    <li>功能1</li>
                    <li>功能2</li>
                    <li>功能3</li>
                </ul>
            </body>
            </html>
            """;

        NotificationResponse response = emailService.sendHtmlEmail(
            "<EMAIL>",
            "HTML测试邮件",
            htmlContent
        );

        System.out.println("HTML邮件发送结果: " + response);
    }

    @Test
    void testSendEmailWithAttachments() {
        // 测试发送带附件的邮件
        EmailRequest request = new EmailRequest();
        request.setTo(Arrays.asList("<EMAIL>"));
        request.setSubject("带附件的测试邮件");
        request.setContent("<h1>这是一封带附件的邮件</h1><p>请查看附件内容。</p>");
        request.setHtml(true);

        // 添加文本附件
        EmailAttachment textAttachment = new EmailAttachment();
        textAttachment.setFileName("test.txt");
        textAttachment.setContentType("text/plain");
        textAttachment.setData("这是附件内容".getBytes());

        request.addAttachment(textAttachment);

        NotificationResponse response = emailService.sendEmailWithAttachments(request);

        System.out.println("带附件邮件发送结果: " + response);
    }

    @Test
    void testAsyncSendEmail() throws Exception {
        // 测试异步发送邮件
        EmailRequest request = new EmailRequest();
        request.setTo(Arrays.asList("<EMAIL>"));
        request.setSubject("异步测试邮件");
        request.setContent("这是一封异步发送的测试邮件");
        request.setHtml(false);

        CompletableFuture<NotificationResponse> future = emailService.sendEmailAsync(request);

        // 等待异步执行完成
        NotificationResponse response = future.get();

        System.out.println("异步邮件发送结果: " + response);
    }

    @Test
    void testBatchSendEmails() {
        // 测试批量发送邮件
        EmailRequest email1 = new EmailRequest();
        email1.setTo(Arrays.asList("<EMAIL>"));
        email1.setSubject("批量邮件1");
        email1.setContent("这是第一封批量邮件");

        EmailRequest email2 = new EmailRequest();
        email2.setTo(Arrays.asList("<EMAIL>"));
        email2.setSubject("批量邮件2");
        email2.setContent("这是第二封批量邮件");

        EmailRequest email3 = new EmailRequest();
        email3.setTo(Arrays.asList("<EMAIL>"));
        email3.setSubject("批量邮件3");
        email3.setContent("这是第三封批量邮件");

        var responses = emailService.sendBatchEmails(Arrays.asList(email1, email2, email3));

        System.out.println("批量邮件发送结果: " + responses);
        assertThat(responses).hasSize(3);
    }

    @Test
    void testEmailValidation() {
        // 测试邮箱地址验证
        assertThat(emailService.isValidEmail("<EMAIL>")).isTrue();
        assertThat(emailService.isValidEmail("<EMAIL>")).isTrue();
        assertThat(emailService.isValidEmail("invalid-email")).isFalse();
        assertThat(emailService.isValidEmail("@example.com")).isFalse();
        assertThat(emailService.isValidEmail("test@")).isFalse();
        assertThat(emailService.isValidEmail("")).isFalse();
        assertThat(emailService.isValidEmail(null)).isFalse();

        System.out.println("邮箱验证测试通过");
    }

    @Test
    void testEmailRequestValidation() {
        // 测试邮件请求验证
        EmailRequest request = new EmailRequest();

        // 测试空请求
        Map<String, String> errors = emailService.validateEmailRequest(request);
        assertThat(errors).isNotEmpty();
        assertThat(errors).containsKey("to");
        assertThat(errors).containsKey("subject");
        assertThat(errors).containsKey("content");

        // 测试有效请求
        request.setTo(Arrays.asList("<EMAIL>"));
        request.setSubject("测试主题");
        request.setContent("测试内容");

        errors = emailService.validateEmailRequest(request);
        assertThat(errors).isEmpty();

        System.out.println("邮件请求验证测试通过");
    }

}
