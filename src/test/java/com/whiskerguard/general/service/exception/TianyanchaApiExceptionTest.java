package com.whiskerguard.general.service.exception;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

public class TianyanchaApiExceptionTest {

    @Test
    void testConstructorWithMessage() {
        // Given
        String message = "API call failed";

        // When
        TianyanchaApiException exception = new TianyanchaApiException(message);

        // Then
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getErrorCode()).isNull();
        assertThat(exception.getEndpoint()).isNull();
        assertThat(exception.getCause()).isNull();
    }

    @Test
    void testConstructorWithMessageAndCause() {
        // Given
        String message = "API call failed";
        Throwable cause = new RuntimeException("Network error");

        // When
        TianyanchaApiException exception = new TianyanchaApiException(message, cause);

        // Then
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getCause()).isEqualTo(cause);
        assertThat(exception.getErrorCode()).isNull();
        assertThat(exception.getEndpoint()).isNull();
    }

    @Test
    void testConstructorWithMessageErrorCodeAndEndpoint() {
        // Given
        String message = "API call failed";
        Integer errorCode = 1001;
        String endpoint = "/baseinfo/normal";

        // When
        TianyanchaApiException exception = new TianyanchaApiException(message, errorCode, endpoint);

        // Then
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getErrorCode()).isEqualTo(errorCode);
        assertThat(exception.getEndpoint()).isEqualTo(endpoint);
        assertThat(exception.getCause()).isNull();
    }

    @Test
    void testConstructorWithAllParameters() {
        // Given
        String message = "API call failed";
        Integer errorCode = 1001;
        String endpoint = "/baseinfo/normal";
        Throwable cause = new RuntimeException("Network error");

        // When
        TianyanchaApiException exception = new TianyanchaApiException(message, errorCode, endpoint, cause);

        // Then
        assertThat(exception.getMessage()).isEqualTo(message);
        assertThat(exception.getErrorCode()).isEqualTo(errorCode);
        assertThat(exception.getEndpoint()).isEqualTo(endpoint);
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    void testGettersAndSetters() {
        // Given
        TianyanchaApiException exception = new TianyanchaApiException("Test message");
        Integer errorCode = 500;
        String endpoint = "/test/endpoint";

        // When
        exception.setErrorCode(errorCode);
        exception.setEndpoint(endpoint);

        // Then
        assertThat(exception.getErrorCode()).isEqualTo(errorCode);
        assertThat(exception.getEndpoint()).isEqualTo(endpoint);
    }
}
