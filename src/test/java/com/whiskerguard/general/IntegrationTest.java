package com.whiskerguard.general;

import com.whiskerguard.general.config.AsyncSyncConfiguration;
import com.whiskerguard.general.config.EmbeddedRedis;
import com.whiskerguard.general.config.EmbeddedSQL;
import com.whiskerguard.general.config.JacksonConfiguration;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Base composite annotation for integration tests.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@SpringBootTest(classes = { WhiskerguardGeneralServiceApp.class, JacksonConfiguration.class, AsyncSyncConfiguration.class })
@EmbeddedRedis
@EmbeddedSQL
public @interface IntegrationTest {
}
