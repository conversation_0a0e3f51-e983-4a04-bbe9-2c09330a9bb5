package com.whiskerguard.general.notification.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.*;
import com.whiskerguard.general.service.EmailService;
import com.whiskerguard.general.service.NotificationService;
import com.whiskerguard.general.service.PushService;
import com.whiskerguard.general.service.SmsService;
import com.whiskerguard.general.service.WechatService;
import com.whiskerguard.general.service.impl.NotificationServiceImpl;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * 通知服务单元测试
 */
class NotificationServiceTest {

    @Mock
    private ApplicationProperties applicationProperties;

    @Mock
    private ApplicationProperties.Notification notification;

    @Mock
    private ApplicationProperties.Notification.Sms smsProperties;

    @Mock
    private EmailService emailService;

    @Mock
    private PushService pushService;

    @Mock
    private WechatService wechatService;

    @Mock
    private SmsService aliyunSmsService;

    @Mock
    private SmsService tencentSmsService;

    private NotificationService notificationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        when(applicationProperties.getNotification()).thenReturn(notification);
        when(notification.getSms()).thenReturn(smsProperties);
        when(smsProperties.getDefaultProvider()).thenReturn(SmsProviderType.ALIYUN);

        notificationService = new NotificationServiceImpl(
            applicationProperties,
            emailService,
            pushService,
            wechatService,
            aliyunSmsService,
            tencentSmsService
        );
    }

    @Test
    void testSendSmsWithAliyun() {
        // 准备测试数据
        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("SMS_123456");
        Map<String, Object> params = new HashMap<>();
        params.put("code", "123456");
        request.setTemplateParams(params);
        request.setProviderType(SmsProviderType.ALIYUN);

        NotificationResponse expectedResponse = NotificationResponse.success("短信发送成功");
        when(aliyunSmsService.send(any(SmsRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendSms(request);

        // 验证结果
        verify(aliyunSmsService, times(1)).send(request);
        verify(tencentSmsService, never()).send(any(SmsRequest.class));
        assertEquals(expectedResponse, response);
    }

    @Test
    void testSendSmsWithTencent() {
        // 准备测试数据
        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("1234567");
        Map<String, Object> params = new HashMap<>();
        params.put("code", "123456");
        request.setTemplateParams(params);
        request.setProviderType(SmsProviderType.TENCENT);

        NotificationResponse expectedResponse = NotificationResponse.success("短信发送成功");
        when(tencentSmsService.send(any(SmsRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendSms(request);

        // 验证结果
        verify(tencentSmsService, times(1)).send(request);
        verify(aliyunSmsService, never()).send(any(SmsRequest.class));
        assertEquals(expectedResponse, response);
        assertTrue(response.isSuccess());
    }

    @Test
    void testSendSmsWithDefaultTencentProvider() {
        // 重置之前的所有mock配置，确保本测试用例的设置不受其他测试的影响
        reset(smsProperties, tencentSmsService, aliyunSmsService);

        // 设置默认提供商为腾讯云
        when(applicationProperties.getNotification()).thenReturn(notification);
        when(notification.getSms()).thenReturn(smsProperties);
        when(smsProperties.getDefaultProvider()).thenReturn(SmsProviderType.TENCENT);

        // 准备测试数据（不指定提供商类型，使用默认）
        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("1234567");
        Map<String, Object> params = new HashMap<>();
        params.put("code", "123456");
        request.setTemplateParams(params);
        // 不设置 providerType，使用默认配置

        NotificationResponse expectedResponse = NotificationResponse.success("短信发送成功");
        when(tencentSmsService.send(any(SmsRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendSms(request);

        // 验证结果
        verify(tencentSmsService, times(1)).send(any(SmsRequest.class));
        verify(aliyunSmsService, never()).send(any(SmsRequest.class));
        assertEquals(expectedResponse, response);
        assertTrue(response.isSuccess());
    }

    @Test
    void testSendEmail() {
        // 准备测试数据
        EmailRequest request = new EmailRequest();
        request.setRecipient("<EMAIL>");
        request.setSubject("测试邮件");
        request.setContent("这是一封测试邮件");

        NotificationResponse expectedResponse = NotificationResponse.success("邮件发送成功");
        when(emailService.send(any(EmailRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendEmail(request);

        // 验证结果
        verify(emailService, times(1)).send(request);
        assertEquals(expectedResponse, response);
    }

    @Test
    void testSendPush() {
        // 准备测试数据
        PushRequest request = new PushRequest();
        request.setTitle("测试推送");
        request.setContent("这是一条测试推送");
        request.getTargets().add("user123");

        NotificationResponse expectedResponse = NotificationResponse.success("推送发送成功");
        when(pushService.send(any(PushRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendPush(request);

        // 验证结果
        verify(pushService, times(1)).send(request);
        assertEquals(expectedResponse, response);
    }

    @Test
    void testSendWechatCustomMessage() {
        // 准备测试数据
        WechatRequest request = new WechatRequest();
        request.setMessageType("text");
        request.getToUsers().add("openid123");
        request.setContent("这是一条微信测试消息");

        NotificationResponse expectedResponse = NotificationResponse.success("微信消息发送成功");
        when(wechatService.sendCustomMessage(any(WechatRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendWechat(request);

        // 验证结果
        verify(wechatService, times(1)).sendCustomMessage(request);
        assertEquals(expectedResponse, response);
    }

    @Test
    void testSendWechatTemplateMessage() {
        // 准备测试数据
        WechatRequest request = new WechatRequest();
        request.getToUsers().add("openid123");
        request.setTemplateId("template123");
        request.setUrl("https://example.com");

        NotificationResponse expectedResponse = NotificationResponse.success("微信模板消息发送成功");
        when(wechatService.sendTemplateMessage(any(WechatRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendWechat(request);

        // 验证结果
        verify(wechatService, times(1)).sendTemplateMessage(request);
        assertEquals(expectedResponse, response);
    }
}
