package com.whiskerguard.general.client;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.service.dto.TianyanchaCompanyBasicInfoResponseDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * Integration test for {@link TianyanchaApiClient}.
 *
 * 这个测试需要真实的天眼查API Token才能运行。
 * 设置环境变量 TIANYANCHA_API_TOKEN 来启用这个测试。
 *
 * This test requires a real Tianyancha API token to run.
 * Set the TIANYANCHA_API_TOKEN environment variable to enable this test.
 */
@SpringBootTest
@ActiveProfiles("test")
@EnabledIfEnvironmentVariable(named = "TIANYANCHA_API_TOKEN", matches = ".*")
public class TianyanchaApiIntegrationTest {

    @Autowired
    private TianyanchaApiClient tianyanchaApiClient;

    @Test
    void testGetCompanyBasicInfo_RealApi() {
        // Given - 使用天眼查官方例子中的企业
        String keyword = "中合数联（苏州）科技有限公司";

        // When
        TianyanchaCompanyBasicInfoResponseDTO response = tianyanchaApiClient.getCompanyBasicInfo(keyword);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.isSuccess()).isTrue();
        assertThat(response.getErrorCode()).isEqualTo(0);
        assertThat(response.getReason()).isEqualTo("ok");

        // 验证返回的企业信息
        assertThat(response.getResult()).isNotNull();
        assertThat(response.getResult().getName()).isEqualTo("中合数联（苏州）科技有限公司");
        assertThat(response.getResult().getCreditCode()).isEqualTo("91320505MAE9MJCD5Q");
        assertThat(response.getResult().getLegalPersonName()).isEqualTo("罗琼");
        assertThat(response.getResult().getRegStatus()).isEqualTo("存续");

        // 打印一些基本信息用于验证
        System.out.println("企业名称: " + response.getResult().getName());
        System.out.println("统一社会信用代码: " + response.getResult().getCreditCode());
        System.out.println("法定代表人: " + response.getResult().getLegalPersonName());
        System.out.println("登记状态: " + response.getResult().getRegStatus());
        System.out.println("注册资本: " + response.getResult().getRegCapital());
        System.out.println("行业: " + response.getResult().getIndustry());
    }

    @Test
    void testGetCompanyBasicInfo_SimpleCompany() {
        // Given - 使用一个简单的企业名称进行测试
        String keyword = "腾讯";

        // When
        TianyanchaCompanyBasicInfoResponseDTO response = tianyanchaApiClient.getCompanyBasicInfo(keyword);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.isSuccess()).isTrue();
        assertThat(response.getErrorCode()).isEqualTo(0);

        // 验证返回的企业信息不为空
        assertThat(response.getResult()).isNotNull();
        assertThat(response.getResult().getName()).isNotNull();
        assertThat(response.getResult().getId()).isNotNull();

        // 打印企业信息
        System.out.println("搜索关键词: " + keyword);
        System.out.println("找到企业: " + response.getResult().getName());
        System.out.println("天眼查ID: " + response.getResult().getId());
        if (response.getResult().getCreditCode() != null) {
            System.out.println("统一社会信用代码: " + response.getResult().getCreditCode());
        }
    }
}
