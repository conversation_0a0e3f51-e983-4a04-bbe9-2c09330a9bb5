package com.whiskerguard.general.client;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.config.TianyanchaProperties;
import com.whiskerguard.general.service.dto.TianyanchaCompanyBasicInfoResponseDTO;
import com.whiskerguard.general.service.exception.TianyanchaApiException;
import java.net.URI;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

/**
 * Unit tests for {@link TianyanchaApiClient}.
 */
@ExtendWith(MockitoExtension.class)
public class TianyanchaApiClientTest {

    @Mock
    private TianyanchaProperties tianyanchaProperties;

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private TianyanchaApiClient tianyanchaApiClient;

    @BeforeEach
    void setUp() {
        when(tianyanchaProperties.getBaseUrl()).thenReturn("http://test.api.com/");
        when(tianyanchaProperties.getApiToken()).thenReturn("test-token");
        when(tianyanchaProperties.getMaxRetryAttempts()).thenReturn(3);
        when(tianyanchaProperties.getRetryDelayMs()).thenReturn(100L);
    }

    @Test
    void testGetCompanyBasicInfo_Success() throws Exception {
        // Given
        String keyword = "test-company";
        String responseJson = "{\"errorCode\":0,\"reason\":\"ok\",\"result\":{\"id\":123,\"name\":\"Test Company\"}}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);

        TianyanchaCompanyBasicInfoResponseDTO expectedResponse = new TianyanchaCompanyBasicInfoResponseDTO();
        expectedResponse.setErrorCode(0);
        expectedResponse.setReason("ok");

        when(restTemplate.exchange(any(URI.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class))).thenReturn(responseEntity);
        when(objectMapper.readValue(responseJson, TianyanchaCompanyBasicInfoResponseDTO.class)).thenReturn(expectedResponse);

        // When
        TianyanchaCompanyBasicInfoResponseDTO result = tianyanchaApiClient.getCompanyBasicInfo(keyword);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getErrorCode()).isEqualTo(0);
        assertThat(result.getReason()).isEqualTo("ok");
        verify(restTemplate).exchange(any(URI.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class));
    }

    @Test
    void testGetCompanyBasicInfo_HttpClientError() {
        // Given
        String keyword = "test-company";
        HttpClientErrorException exception = new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Bad Request");

        when(restTemplate.exchange(any(URI.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class))).thenThrow(exception);

        // When & Then
        assertThatThrownBy(() -> tianyanchaApiClient.getCompanyBasicInfo(keyword))
            .isInstanceOf(TianyanchaApiException.class)
            .hasMessageContaining("Client error");
    }

    @Test
    void testGetCompanyBasicInfo_NetworkError() {
        // Given
        String keyword = "test-company";
        ResourceAccessException exception = new ResourceAccessException("Network timeout");

        when(restTemplate.exchange(any(URI.class), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class))).thenThrow(exception);

        // When & Then
        assertThatThrownBy(() -> tianyanchaApiClient.getCompanyBasicInfo(keyword))
            .isInstanceOf(TianyanchaApiException.class)
            .hasMessageContaining("Network error");
    }
}
