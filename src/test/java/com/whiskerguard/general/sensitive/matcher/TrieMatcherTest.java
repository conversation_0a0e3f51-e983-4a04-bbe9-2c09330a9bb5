package com.whiskerguard.general.sensitive.matcher;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import com.whiskerguard.general.domain.SensitiveWord;
import com.whiskerguard.general.domain.enumeration.LanguageType;
import com.whiskerguard.general.domain.enumeration.SensitiveCategory;
import com.whiskerguard.general.domain.enumeration.SeverityType;
import com.whiskerguard.general.repository.SensitiveWordRepository;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class TrieMatcherTest {

    @Mock
    private SensitiveWordRepository sensitiveWordRepository;

    @InjectMocks
    private TrieMatcher matcher;

    private List<SensitiveWord> sensitiveWords;
    private final Long platformTenantId = 0L;
    private final Long testTenantId = 123L;

    @BeforeEach
    void setUp() {
        // Create a list of 100 sensitive words with different severity levels
        sensitiveWords = new ArrayList<>();

        // Generate words for each severity level (约33个词语/级别)
        generateWords("replace", SeverityType.REPLACE, 33); // 替代 REPLACE 代替原来的 LOW
        generateWords("review", SeverityType.REVIEW, 33); // 审查 REVIEW 代替原来的 MEDIUM
        generateWords("block", SeverityType.BLOCK, 34); // 阻断 BLOCK 代替原来的 HIGH/CRITICAL

        // Mock the repository to return our test sensitive words
        when(sensitiveWordRepository.findValidByTenant(anyLong())).thenReturn(sensitiveWords);

        // Manually initialize the matcher (normally done by @PostConstruct)
        matcher.init();
    }

    private void generateWords(String prefix, SeverityType severity, int count) {
        Instant now = Instant.now();
        Instant futureDate = now.plus(30, ChronoUnit.DAYS);

        for (int i = 1; i <= count; i++) {
            SensitiveWord word = new SensitiveWord()
                .term(prefix + i)
                .tenantId(platformTenantId)
                .lang(LanguageType.ZH)
                .category(SensitiveCategory.OTHER) // 使用存在的枚举值 OTHER 代替不存在的 PROFANITY
                .severity(severity)
                .validFrom(now)
                .validTo(futureDate)
                .version(1)
                .isDeleted(false)
                .createdAt(now)
                .updatedAt(now);

            sensitiveWords.add(word);
        }
    }

    @Test
    void shouldDetectSensitiveWords() {
        // Create text containing some sensitive words
        String text = "This is replace2 text with review6 words that are block15 and should be detected.";

        // Check the text for sensitive words
        MatchResult result = matcher.check(text);

        // Verify that sensitive words were detected
        assertThat(result).isNotNull();
        assertThat(result.isBlocked()).isTrue(); // Should be blocked due to BLOCK severity
        assertThat(result.getTerms()).hasSize(3);
        assertThat(result.getTerms()).contains("replace2", "review6", "block15");
        assertThat(result.getSeverity()).isEqualTo(SeverityType.BLOCK);
    }

    @Test
    void shouldMaskSensitiveWords() {
        // Create text containing some sensitive words
        String text = "This is replace12 text with review13 words.";

        // Mask the sensitive words
        String masked = matcher.mask(text, '*');

        // Verify that sensitive words were masked (replace12 = 9 chars, review13 = 8 chars)
        assertThat(masked).isEqualTo("This is ********* text with ******** words.");
    }

    @Test
    void shouldNotBlockTextWithReplaceSeverity() {
        // Create text with only REPLACE severity words (lowest level)
        String text = "This text only contains replace6 and replace11 sensitive words.";

        // Check the text for sensitive words
        MatchResult result = matcher.check(text);

        // Verify that the text is not blocked
        assertThat(result).isNotNull();
        assertThat(result.isBlocked()).isFalse();
        assertThat(result.getTerms()).hasSize(2);
        assertThat(result.getSeverity()).isEqualTo(SeverityType.REPLACE);
    }

    @Test
    void shouldBlockTextWithBlockSeverity() {
        // Create text with BLOCK severity words
        String text = "This text contains block5 sensitive words.";

        // Check the text for sensitive words
        MatchResult result = matcher.check(text);

        // Verify that the text is blocked
        assertThat(result).isNotNull();
        assertThat(result.isBlocked()).isTrue();
        assertThat(result.getTerms()).hasSize(1);
        assertThat(result.getSeverity()).isEqualTo(SeverityType.BLOCK);
    }

    @Test
    void shouldNotDetectNonSensitiveWords() {
        // Create text with no sensitive words
        String text = "This is a perfectly safe text without any problematic content.";

        // Check the text for sensitive words
        MatchResult result = matcher.check(text);

        // Verify that no sensitive words were detected
        assertThat(result).isNotNull();
        assertThat(result.isBlocked()).isFalse();
        assertThat(result.getTerms()).isEmpty();
    }

    @Test
    void shouldHandleEmptyInput() {
        // Check empty text
        MatchResult result = matcher.check("");

        // Verify proper handling
        assertThat(result).isNotNull();
        assertThat(result.isBlocked()).isFalse();
        assertThat(result.getTerms()).isEmpty();

        // Check null input
        result = matcher.check(null);

        // Verify proper handling
        assertThat(result).isNotNull();
        assertThat(result.isBlocked()).isFalse();
        assertThat(result.getTerms()).isEmpty();
    }

    @Test
    void benchmarkPerformance() {
        // Create a text with sensitive words
        String text =
            "This is a review10 text with some block15 sensitive replace5 words repeated multiple times. " +
            "We want to test if the matcher can handle longer texts efficiently " +
            "and ensure that performance is block12 acceptable for production use.";

        // Perform 1000 checks and measure time
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 1000; i++) {
            matcher.check(text);
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        // Calculate average time per check (should be less than 5ms)
        double avgTimePerCheck = totalTime / 1000.0;

        System.out.println("Average check time: " + avgTimePerCheck + "ms");

        // Assert that average time is less than 5ms
        assertThat(avgTimePerCheck).isLessThan(5.0);
    }
}
