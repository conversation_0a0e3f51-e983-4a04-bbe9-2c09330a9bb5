package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NotificationSendRecordAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationSendRecordAllPropertiesEquals(NotificationSendRecord expected, NotificationSendRecord actual) {
        assertNotificationSendRecordAutoGeneratedPropertiesEquals(expected, actual);
        assertNotificationSendRecordAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationSendRecordAllUpdatablePropertiesEquals(
        NotificationSendRecord expected,
        NotificationSendRecord actual
    ) {
        assertNotificationSendRecordUpdatableFieldsEquals(expected, actual);
        assertNotificationSendRecordUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationSendRecordAutoGeneratedPropertiesEquals(
        NotificationSendRecord expected,
        NotificationSendRecord actual
    ) {
        assertThat(actual)
            .as("Verify NotificationSendRecord auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationSendRecordUpdatableFieldsEquals(NotificationSendRecord expected, NotificationSendRecord actual) {
        assertThat(actual)
            .as("Verify NotificationSendRecord relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getRecipientId()).as("check recipientId").isEqualTo(expected.getRecipientId()))
            .satisfies(a -> assertThat(a.getRecipientType()).as("check recipientType").isEqualTo(expected.getRecipientType()))
            .satisfies(a -> assertThat(a.getChannel()).as("check channel").isEqualTo(expected.getChannel()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getSentTime()).as("check sentTime").isEqualTo(expected.getSentTime()))
            .satisfies(a -> assertThat(a.getReadTime()).as("check readTime").isEqualTo(expected.getReadTime()))
            .satisfies(a -> assertThat(a.getErrorMessage()).as("check errorMessage").isEqualTo(expected.getErrorMessage()))
            .satisfies(a -> assertThat(a.getExternalId()).as("check externalId").isEqualTo(expected.getExternalId()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationSendRecordUpdatableRelationshipsEquals(
        NotificationSendRecord expected,
        NotificationSendRecord actual
    ) {
        assertThat(actual)
            .as("Verify NotificationSendRecord relationships")
            .satisfies(a -> assertThat(a.getNotification()).as("check notification").isEqualTo(expected.getNotification()));
    }
}
