package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

public class CompanyTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Company.class);
        Company company1 = createCompanySample1();
        Company company2 = new Company();
        assertThat(company1).isNotEqualTo(company2);

        company2.setId(company1.getId());
        assertThat(company1).isEqualTo(company2);

        company2 = createCompanySample2();
        assertThat(company1).isNotEqualTo(company2);
    }

    @Test
    void hashCodeVerifier() throws Exception {
        Company company = new Company();
        assertThat(company.hashCode()).isZero();

        Company company1 = createCompanySample1();
        company.setId(company1.getId());
        assertThat(company).hasSameHashCodeAs(company1);
    }

    private Company createCompanySample1() {
        return new Company().id(1L).name("Sample Company 1").unifiedSocialCreditCode("*********");
    }

    private Company createCompanySample2() {
        return new Company().id(2L).name("Sample Company 2").unifiedSocialCreditCode("*********");
    }
}
