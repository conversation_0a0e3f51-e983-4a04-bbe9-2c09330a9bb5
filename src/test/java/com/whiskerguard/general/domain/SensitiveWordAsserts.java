package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class SensitiveWordAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSensitiveWordAllPropertiesEquals(SensitiveWord expected, SensitiveWord actual) {
        assertSensitiveWordAutoGeneratedPropertiesEquals(expected, actual);
        assertSensitiveWordAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSensitiveWordAllUpdatablePropertiesEquals(SensitiveWord expected, SensitiveWord actual) {
        assertSensitiveWordUpdatableFieldsEquals(expected, actual);
        assertSensitiveWordUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSensitiveWordAutoGeneratedPropertiesEquals(SensitiveWord expected, SensitiveWord actual) {
        assertThat(actual)
            .as("Verify SensitiveWord auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSensitiveWordUpdatableFieldsEquals(SensitiveWord expected, SensitiveWord actual) {
        assertThat(actual)
            .as("Verify SensitiveWord relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getTerm()).as("check term").isEqualTo(expected.getTerm()))
            .satisfies(a -> assertThat(a.getLang()).as("check lang").isEqualTo(expected.getLang()))
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()))
            .satisfies(a -> assertThat(a.getSeverity()).as("check severity").isEqualTo(expected.getSeverity()))
            .satisfies(a -> assertThat(a.getValidFrom()).as("check validFrom").isEqualTo(expected.getValidFrom()))
            .satisfies(a -> assertThat(a.getValidTo()).as("check validTo").isEqualTo(expected.getValidTo()))
            .satisfies(a -> assertThat(a.getNotes()).as("check notes").isEqualTo(expected.getNotes()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSensitiveWordUpdatableRelationshipsEquals(SensitiveWord expected, SensitiveWord actual) {
        // empty method
    }
}
