package com.whiskerguard.general.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class SensitiveWordTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static SensitiveWord getSensitiveWordSample1() {
        return new SensitiveWord()
            .id(1L)
            .tenantId(1L)
            .term("term1")
            .notes("notes1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static SensitiveWord getSensitiveWordSample2() {
        return new SensitiveWord()
            .id(2L)
            .tenantId(2L)
            .term("term2")
            .notes("notes2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static SensitiveWord getSensitiveWordRandomSampleGenerator() {
        return new SensitiveWord()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .term(UUID.randomUUID().toString())
            .notes(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
