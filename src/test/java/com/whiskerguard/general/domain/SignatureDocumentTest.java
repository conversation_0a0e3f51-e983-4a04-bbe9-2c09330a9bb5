package com.whiskerguard.general.domain;

import static com.whiskerguard.general.domain.SignatureDocumentTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class SignatureDocumentTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(SignatureDocument.class);
        SignatureDocument signatureDocument1 = getSignatureDocumentSample1();
        SignatureDocument signatureDocument2 = new SignatureDocument();
        assertThat(signatureDocument1).isNotEqualTo(signatureDocument2);

        signatureDocument2.setId(signatureDocument1.getId());
        assertThat(signatureDocument1).isEqualTo(signatureDocument2);

        signatureDocument2 = getSignatureDocumentSample2();
        assertThat(signatureDocument1).isNotEqualTo(signatureDocument2);
    }
}
