package com.whiskerguard.general.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NotificationTemplateTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NotificationTemplate getNotificationTemplateSample1() {
        return new NotificationTemplate()
            .id(1L)
            .tenantId(1L)
            .code("code1")
            .name("name1")
            .titleTemplate("titleTemplate1")
            .contentTemplate("contentTemplate1")
            .smsTemplate("smsTemplate1")
            .emailTemplate("emailTemplate1")
            .pushTemplate("pushTemplate1")
            .supportedChannels("supportedChannels1")
            .defaultChannels("defaultChannels1")
            .language("language1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static NotificationTemplate getNotificationTemplateSample2() {
        return new NotificationTemplate()
            .id(2L)
            .tenantId(2L)
            .code("code2")
            .name("name2")
            .titleTemplate("titleTemplate2")
            .contentTemplate("contentTemplate2")
            .smsTemplate("smsTemplate2")
            .emailTemplate("emailTemplate2")
            .pushTemplate("pushTemplate2")
            .supportedChannels("supportedChannels2")
            .defaultChannels("defaultChannels2")
            .language("language2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static NotificationTemplate getNotificationTemplateRandomSampleGenerator() {
        return new NotificationTemplate()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .code(UUID.randomUUID().toString())
            .name(UUID.randomUUID().toString())
            .titleTemplate(UUID.randomUUID().toString())
            .contentTemplate(UUID.randomUUID().toString())
            .smsTemplate(UUID.randomUUID().toString())
            .emailTemplate(UUID.randomUUID().toString())
            .pushTemplate(UUID.randomUUID().toString())
            .supportedChannels(UUID.randomUUID().toString())
            .defaultChannels(UUID.randomUUID().toString())
            .language(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
