package com.whiskerguard.general.domain;

import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class CompanyContactTestSamples {

    public static CompanyContact getCompanyContactSample1() {
        return new CompanyContact()
            .id(1L)
            .phoneNumber("010-12345678")
            .email("<EMAIL>")
            .website("www.company.com")
            .address("北京市朝阳区测试路123号");
    }

    public static CompanyContact getCompanyContactSample2() {
        return new CompanyContact()
            .id(2L)
            .phoneNumber("021-87654321")
            .email("<EMAIL>")
            .website("www.testcompany.com")
            .address("上海市浦东新区测试大道456号");
    }

    public static CompanyContact getCompanyContactRandomSampleGenerator() {
        return new CompanyContact()
            .id(longCount.incrementAndGet())
            .phoneNumber(UUID.randomUUID().toString())
            .email(UUID.randomUUID().toString())
            .website(UUID.randomUUID().toString())
            .address(UUID.randomUUID().toString());
    }

    private static final AtomicLong longCount = new AtomicLong(new java.util.Random().nextInt() + (2 * Integer.MAX_VALUE));
}
