package com.whiskerguard.general.domain;

import static com.whiskerguard.general.domain.NotificationRecordTestSamples.*;
import static com.whiskerguard.general.domain.NotificationSendRecordTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NotificationSendRecordTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NotificationSendRecord.class);
        NotificationSendRecord notificationSendRecord1 = getNotificationSendRecordSample1();
        NotificationSendRecord notificationSendRecord2 = new NotificationSendRecord();
        assertThat(notificationSendRecord1).isNotEqualTo(notificationSendRecord2);

        notificationSendRecord2.setId(notificationSendRecord1.getId());
        assertThat(notificationSendRecord1).isEqualTo(notificationSendRecord2);

        notificationSendRecord2 = getNotificationSendRecordSample2();
        assertThat(notificationSendRecord1).isNotEqualTo(notificationSendRecord2);
    }

    @Test
    void notificationTest() {
        NotificationSendRecord notificationSendRecord = getNotificationSendRecordRandomSampleGenerator();
        NotificationRecord notificationRecordBack = getNotificationRecordRandomSampleGenerator();

        notificationSendRecord.setNotification(notificationRecordBack);
        assertThat(notificationSendRecord.getNotification()).isEqualTo(notificationRecordBack);

        notificationSendRecord.notification(null);
        assertThat(notificationSendRecord.getNotification()).isNull();
    }
}
