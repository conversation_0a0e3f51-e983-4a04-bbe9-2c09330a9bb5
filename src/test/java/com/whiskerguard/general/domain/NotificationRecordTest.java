package com.whiskerguard.general.domain;

import static com.whiskerguard.general.domain.NotificationRecordTestSamples.*;
import static com.whiskerguard.general.domain.NotificationSendRecordTestSamples.*;
import static com.whiskerguard.general.domain.NotificationTemplateTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class NotificationRecordTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NotificationRecord.class);
        NotificationRecord notificationRecord1 = getNotificationRecordSample1();
        NotificationRecord notificationRecord2 = new NotificationRecord();
        assertThat(notificationRecord1).isNotEqualTo(notificationRecord2);

        notificationRecord2.setId(notificationRecord1.getId());
        assertThat(notificationRecord1).isEqualTo(notificationRecord2);

        notificationRecord2 = getNotificationRecordSample2();
        assertThat(notificationRecord1).isNotEqualTo(notificationRecord2);
    }

    @Test
    void templateTest() {
        NotificationRecord notificationRecord = getNotificationRecordRandomSampleGenerator();
        NotificationTemplate notificationTemplateBack = getNotificationTemplateRandomSampleGenerator();

        notificationRecord.setTemplate(notificationTemplateBack);
        assertThat(notificationRecord.getTemplate()).isEqualTo(notificationTemplateBack);

        notificationRecord.template(null);
        assertThat(notificationRecord.getTemplate()).isNull();
    }

    @Test
    void sendRecordsTest() {
        NotificationRecord notificationRecord = getNotificationRecordRandomSampleGenerator();
        NotificationSendRecord notificationSendRecordBack = getNotificationSendRecordRandomSampleGenerator();

        notificationRecord.addSendRecords(notificationSendRecordBack);
        assertThat(notificationRecord.getSendRecords()).containsOnly(notificationSendRecordBack);
        assertThat(notificationSendRecordBack.getNotification()).isEqualTo(notificationRecord);

        notificationRecord.removeSendRecords(notificationSendRecordBack);
        assertThat(notificationRecord.getSendRecords()).doesNotContain(notificationSendRecordBack);
        assertThat(notificationSendRecordBack.getNotification()).isNull();

        notificationRecord.sendRecords(new HashSet<>(Set.of(notificationSendRecordBack)));
        assertThat(notificationRecord.getSendRecords()).containsOnly(notificationSendRecordBack);
        assertThat(notificationSendRecordBack.getNotification()).isEqualTo(notificationRecord);

        notificationRecord.setSendRecords(new HashSet<>());
        assertThat(notificationRecord.getSendRecords()).doesNotContain(notificationSendRecordBack);
        assertThat(notificationSendRecordBack.getNotification()).isNull();
    }
}
