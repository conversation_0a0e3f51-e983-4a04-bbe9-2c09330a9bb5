package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class UserNotificationPreferenceAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserNotificationPreferenceAllPropertiesEquals(
        UserNotificationPreference expected,
        UserNotificationPreference actual
    ) {
        assertUserNotificationPreferenceAutoGeneratedPropertiesEquals(expected, actual);
        assertUserNotificationPreferenceAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserNotificationPreferenceAllUpdatablePropertiesEquals(
        UserNotificationPreference expected,
        UserNotificationPreference actual
    ) {
        assertUserNotificationPreferenceUpdatableFieldsEquals(expected, actual);
        assertUserNotificationPreferenceUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserNotificationPreferenceAutoGeneratedPropertiesEquals(
        UserNotificationPreference expected,
        UserNotificationPreference actual
    ) {
        assertThat(actual)
            .as("Verify UserNotificationPreference auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserNotificationPreferenceUpdatableFieldsEquals(
        UserNotificationPreference expected,
        UserNotificationPreference actual
    ) {
        assertThat(actual)
            .as("Verify UserNotificationPreference relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getUserId()).as("check userId").isEqualTo(expected.getUserId()))
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()))
            .satisfies(a -> assertThat(a.getSubType()).as("check subType").isEqualTo(expected.getSubType()))
            .satisfies(a -> assertThat(a.getEnabledChannels()).as("check enabledChannels").isEqualTo(expected.getEnabledChannels()))
            .satisfies(a -> assertThat(a.getQuietHoursStart()).as("check quietHoursStart").isEqualTo(expected.getQuietHoursStart()))
            .satisfies(a -> assertThat(a.getQuietHoursEnd()).as("check quietHoursEnd").isEqualTo(expected.getQuietHoursEnd()))
            .satisfies(a -> assertThat(a.getEnabled()).as("check enabled").isEqualTo(expected.getEnabled()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUserNotificationPreferenceUpdatableRelationshipsEquals(
        UserNotificationPreference expected,
        UserNotificationPreference actual
    ) {
        // empty method
    }
}
