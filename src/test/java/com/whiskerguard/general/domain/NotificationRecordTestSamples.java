package com.whiskerguard.general.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NotificationRecordTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NotificationRecord getNotificationRecordSample1() {
        return new NotificationRecord()
            .id(1L)
            .tenantId(1L)
            .title("title1")
            .content("content1")
            .recipientIds("recipientIds1")
            .channels("channels1")
            .businessId("businessId1")
            .businessType("businessType1")
            .templateParams("templateParams1")
            .retryCount(1)
            .errorMessage("errorMessage1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static NotificationRecord getNotificationRecordSample2() {
        return new NotificationRecord()
            .id(2L)
            .tenantId(2L)
            .title("title2")
            .content("content2")
            .recipientIds("recipientIds2")
            .channels("channels2")
            .businessId("businessId2")
            .businessType("businessType2")
            .templateParams("templateParams2")
            .retryCount(2)
            .errorMessage("errorMessage2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static NotificationRecord getNotificationRecordRandomSampleGenerator() {
        return new NotificationRecord()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .title(UUID.randomUUID().toString())
            .content(UUID.randomUUID().toString())
            .recipientIds(UUID.randomUUID().toString())
            .channels(UUID.randomUUID().toString())
            .businessId(UUID.randomUUID().toString())
            .businessType(UUID.randomUUID().toString())
            .templateParams(UUID.randomUUID().toString())
            .retryCount(intCount.incrementAndGet())
            .errorMessage(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
