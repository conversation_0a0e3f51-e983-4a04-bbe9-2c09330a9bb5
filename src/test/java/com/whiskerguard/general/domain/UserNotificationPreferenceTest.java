package com.whiskerguard.general.domain;

import static com.whiskerguard.general.domain.UserNotificationPreferenceTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class UserNotificationPreferenceTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(UserNotificationPreference.class);
        UserNotificationPreference userNotificationPreference1 = getUserNotificationPreferenceSample1();
        UserNotificationPreference userNotificationPreference2 = new UserNotificationPreference();
        assertThat(userNotificationPreference1).isNotEqualTo(userNotificationPreference2);

        userNotificationPreference2.setId(userNotificationPreference1.getId());
        assertThat(userNotificationPreference1).isEqualTo(userNotificationPreference2);

        userNotificationPreference2 = getUserNotificationPreferenceSample2();
        assertThat(userNotificationPreference1).isNotEqualTo(userNotificationPreference2);
    }
}
