package com.whiskerguard.general.domain;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class CompanyTestSamples {

    public static Company getCompanySample1() {
        return new Company()
            .id(1L)
            .tianyanchaId(11684584L)
            .name("中航重机股份有限公司")
            .unifiedSocialCreditCode("91520000214434146R")
            .legalPersonName("姬苏春")
            .regStatus("存续")
            .regCapital("77800.32万人民币")
            .establishTime(Instant.parse("1996-11-20T00:00:00Z"))
            .companyOrgType("其他股份有限公司(上市)")
            .industry("汽车制造业")
            .regLocation("贵州双龙航空港经济区机场路9号太升国际A栋3单元5层")
            .businessScope("汽车零部件制造")
            .regNumber("520000000012345")
            .taxNumber("91520000214434146R")
            .cacheTime(Instant.now().truncatedTo(ChronoUnit.MILLIS));
    }

    public static Company getCompanySample2() {
        return new Company()
            .id(2L)
            .tianyanchaId(22345678L)
            .name("测试企业有限公司")
            .unifiedSocialCreditCode("91110000123456789X")
            .legalPersonName("张三")
            .regStatus("存续")
            .regCapital("1000万人民币")
            .establishTime(Instant.parse("2020-01-01T00:00:00Z"))
            .companyOrgType("有限责任公司")
            .industry("软件和信息技术服务业")
            .regLocation("北京市朝阳区测试路123号")
            .businessScope("软件开发")
            .regNumber("110000000098765")
            .taxNumber("91110000123456789X")
            .cacheTime(Instant.now().truncatedTo(ChronoUnit.MILLIS));
    }

    public static Company getCompanyRandomSampleGenerator() {
        return new Company()
            .tianyanchaId(longCount.incrementAndGet())
            .name("Company-" + UUID.randomUUID().toString().substring(0, 8))
            .unifiedSocialCreditCode("91" + String.format("%016d", longCount.incrementAndGet()))
            .legalPersonName("Person-" + UUID.randomUUID().toString().substring(0, 8))
            .regStatus("存续")
            .regCapital(longCount.incrementAndGet() + "万人民币")
            .establishTime(Instant.now().minus(longCount.incrementAndGet() % 365, ChronoUnit.DAYS))
            .companyOrgType("有限责任公司")
            .industry("软件和信息技术服务业")
            .regLocation("测试地址-" + UUID.randomUUID().toString().substring(0, 8))
            .businessScope("软件开发")
            .regNumber(String.format("%015d", longCount.incrementAndGet()))
            .taxNumber("91" + String.format("%016d", longCount.incrementAndGet()))
            .cacheTime(Instant.now().truncatedTo(ChronoUnit.MILLIS));
    }

    private static final AtomicLong longCount = new AtomicLong(
        new java.util.Random().nextInt() + (2 * Integer.MAX_VALUE)
    );
}
