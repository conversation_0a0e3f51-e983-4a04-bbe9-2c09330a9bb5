package com.whiskerguard.general.domain;

import static com.whiskerguard.general.domain.SensitiveWordTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class SensitiveWordTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(SensitiveWord.class);
        SensitiveWord sensitiveWord1 = getSensitiveWordSample1();
        SensitiveWord sensitiveWord2 = new SensitiveWord();
        assertThat(sensitiveWord1).isNotEqualTo(sensitiveWord2);

        sensitiveWord2.setId(sensitiveWord1.getId());
        assertThat(sensitiveWord1).isEqualTo(sensitiveWord2);

        sensitiveWord2 = getSensitiveWordSample2();
        assertThat(sensitiveWord1).isNotEqualTo(sensitiveWord2);
    }
}
