package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NotificationTemplateAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationTemplateAllPropertiesEquals(NotificationTemplate expected, NotificationTemplate actual) {
        assertNotificationTemplateAutoGeneratedPropertiesEquals(expected, actual);
        assertNotificationTemplateAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationTemplateAllUpdatablePropertiesEquals(NotificationTemplate expected, NotificationTemplate actual) {
        assertNotificationTemplateUpdatableFieldsEquals(expected, actual);
        assertNotificationTemplateUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationTemplateAutoGeneratedPropertiesEquals(NotificationTemplate expected, NotificationTemplate actual) {
        assertThat(actual)
            .as("Verify NotificationTemplate auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationTemplateUpdatableFieldsEquals(NotificationTemplate expected, NotificationTemplate actual) {
        assertThat(actual)
            .as("Verify NotificationTemplate relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getCode()).as("check code").isEqualTo(expected.getCode()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()))
            .satisfies(a -> assertThat(a.getSubType()).as("check subType").isEqualTo(expected.getSubType()))
            .satisfies(a -> assertThat(a.getTitleTemplate()).as("check titleTemplate").isEqualTo(expected.getTitleTemplate()))
            .satisfies(a -> assertThat(a.getContentTemplate()).as("check contentTemplate").isEqualTo(expected.getContentTemplate()))
            .satisfies(a -> assertThat(a.getSmsTemplate()).as("check smsTemplate").isEqualTo(expected.getSmsTemplate()))
            .satisfies(a -> assertThat(a.getEmailTemplate()).as("check emailTemplate").isEqualTo(expected.getEmailTemplate()))
            .satisfies(a -> assertThat(a.getPushTemplate()).as("check pushTemplate").isEqualTo(expected.getPushTemplate()))
            .satisfies(a -> assertThat(a.getSupportedChannels()).as("check supportedChannels").isEqualTo(expected.getSupportedChannels()))
            .satisfies(a -> assertThat(a.getDefaultChannels()).as("check defaultChannels").isEqualTo(expected.getDefaultChannels()))
            .satisfies(a -> assertThat(a.getEnabled()).as("check enabled").isEqualTo(expected.getEnabled()))
            .satisfies(a -> assertThat(a.getLanguage()).as("check language").isEqualTo(expected.getLanguage()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationTemplateUpdatableRelationshipsEquals(NotificationTemplate expected, NotificationTemplate actual) {
        // empty method
    }
}
