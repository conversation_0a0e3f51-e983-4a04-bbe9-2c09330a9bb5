package com.whiskerguard.general.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class SignatureDocumentTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static SignatureDocument getSignatureDocumentSample1() {
        return new SignatureDocument()
            .id(1L)
            .title("title1")
            .description("description1")
            .documentUrl("documentUrl1")
            .externalId("externalId1")
            .transactionId("transactionId1")
            .userId("userId1")
            .signedDocumentUrl("signedDocumentUrl1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static SignatureDocument getSignatureDocumentSample2() {
        return new SignatureDocument()
            .id(2L)
            .title("title2")
            .description("description2")
            .documentUrl("documentUrl2")
            .externalId("externalId2")
            .transactionId("transactionId2")
            .userId("userId2")
            .signedDocumentUrl("signedDocumentUrl2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static SignatureDocument getSignatureDocumentRandomSampleGenerator() {
        return new SignatureDocument()
            .id(longCount.incrementAndGet())
            .title(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .documentUrl(UUID.randomUUID().toString())
            .externalId(UUID.randomUUID().toString())
            .transactionId(UUID.randomUUID().toString())
            .userId(UUID.randomUUID().toString())
            .signedDocumentUrl(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
