package com.whiskerguard.general.domain;

import static com.whiskerguard.general.domain.UserWechatBindingTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class UserWechatBindingTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(UserWechatBinding.class);
        UserWechatBinding userWechatBinding1 = getUserWechatBindingSample1();
        UserWechatBinding userWechatBinding2 = new UserWechatBinding();
        assertThat(userWechatBinding1).isNotEqualTo(userWechatBinding2);

        userWechatBinding2.setId(userWechatBinding1.getId());
        assertThat(userWechatBinding1).isEqualTo(userWechatBinding2);

        userWechatBinding2 = getUserWechatBindingSample2();
        assertThat(userWechatBinding1).isNotEqualTo(userWechatBinding2);
    }
}
