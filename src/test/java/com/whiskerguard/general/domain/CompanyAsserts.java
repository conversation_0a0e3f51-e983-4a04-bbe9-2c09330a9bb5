package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Assertions for Company entity.
 */
public class CompanyAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyAllPropertiesEquals(Company expected, Company actual) {
        assertCompanyAutoGeneratedPropertiesEquals(expected, actual);
        assertCompanyAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyAllUpdatablePropertiesEquals(Company expected, Company actual) {
        assertCompanyUpdatableFieldsEquals(expected, actual);
        assertCompanyUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyAutoGeneratedPropertiesEquals(Company expected, Company actual) {
        assertThat(expected)
            .as("Verify Company auto generated properties")
            .satisfies(e -> assertThat(e.getId()).as("check id").isEqualTo(actual.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyUpdatableFieldsEquals(Company expected, Company actual) {
        assertThat(expected)
            .as("Verify Company relevant properties")
            .satisfies(e -> assertThat(e.getTianyanchaId()).as("check tianyanchaId").isEqualTo(actual.getTianyanchaId()))
            .satisfies(e -> assertThat(e.getName()).as("check name").isEqualTo(actual.getName()))
            .satisfies(e -> assertThat(e.getUnifiedSocialCreditCode()).as("check unifiedSocialCreditCode").isEqualTo(actual.getUnifiedSocialCreditCode()))
            .satisfies(e -> assertThat(e.getLegalPersonName()).as("check legalPersonName").isEqualTo(actual.getLegalPersonName()))
            .satisfies(e -> assertThat(e.getRegStatus()).as("check regStatus").isEqualTo(actual.getRegStatus()))
            .satisfies(e -> assertThat(e.getRegCapital()).as("check regCapital").isEqualTo(actual.getRegCapital()))
            .satisfies(e -> assertThat(e.getEstablishTime()).as("check establishTime").isEqualTo(actual.getEstablishTime()))
            .satisfies(e -> assertThat(e.getCompanyOrgType()).as("check companyOrgType").isEqualTo(actual.getCompanyOrgType()))
            .satisfies(e -> assertThat(e.getIndustry()).as("check industry").isEqualTo(actual.getIndustry()))
            .satisfies(e -> assertThat(e.getRegLocation()).as("check regLocation").isEqualTo(actual.getRegLocation()))
            .satisfies(e -> assertThat(e.getBusinessScope()).as("check businessScope").isEqualTo(actual.getBusinessScope()))
            .satisfies(e -> assertThat(e.getRegNumber()).as("check regNumber").isEqualTo(actual.getRegNumber()))
            .satisfies(e -> assertThat(e.getTaxNumber()).as("check taxNumber").isEqualTo(actual.getTaxNumber()))
            .satisfies(e -> assertThat(e.getCacheTime()).as("check cacheTime").isEqualTo(actual.getCacheTime()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyUpdatableRelationshipsEquals(Company expected, Company actual) {
        // No relationships to assert for now
    }
}
