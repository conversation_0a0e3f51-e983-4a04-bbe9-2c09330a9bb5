package com.whiskerguard.general.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class UserNotificationPreferenceTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static UserNotificationPreference getUserNotificationPreferenceSample1() {
        return new UserNotificationPreference()
            .id(1L)
            .tenantId(1L)
            .userId(1L)
            .enabledChannels("enabledChannels1")
            .quietHoursStart("quietHoursStart1")
            .quietHoursEnd("quietHoursEnd1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static UserNotificationPreference getUserNotificationPreferenceSample2() {
        return new UserNotificationPreference()
            .id(2L)
            .tenantId(2L)
            .userId(2L)
            .enabledChannels("enabledChannels2")
            .quietHoursStart("quietHoursStart2")
            .quietHoursEnd("quietHoursEnd2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static UserNotificationPreference getUserNotificationPreferenceRandomSampleGenerator() {
        return new UserNotificationPreference()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .userId(longCount.incrementAndGet())
            .enabledChannels(UUID.randomUUID().toString())
            .quietHoursStart(UUID.randomUUID().toString())
            .quietHoursEnd(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
