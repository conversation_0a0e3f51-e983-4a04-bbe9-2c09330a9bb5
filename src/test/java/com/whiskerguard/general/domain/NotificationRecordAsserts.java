package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NotificationRecordAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationRecordAllPropertiesEquals(NotificationRecord expected, NotificationRecord actual) {
        assertNotificationRecordAutoGeneratedPropertiesEquals(expected, actual);
        assertNotificationRecordAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationRecordAllUpdatablePropertiesEquals(NotificationRecord expected, NotificationRecord actual) {
        assertNotificationRecordUpdatableFieldsEquals(expected, actual);
        assertNotificationRecordUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationRecordAutoGeneratedPropertiesEquals(NotificationRecord expected, NotificationRecord actual) {
        assertThat(actual)
            .as("Verify NotificationRecord auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationRecordUpdatableFieldsEquals(NotificationRecord expected, NotificationRecord actual) {
        assertThat(actual)
            .as("Verify NotificationRecord relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()))
            .satisfies(a -> assertThat(a.getSubType()).as("check subType").isEqualTo(expected.getSubType()))
            .satisfies(a -> assertThat(a.getScope()).as("check scope").isEqualTo(expected.getScope()))
            .satisfies(a -> assertThat(a.getTitle()).as("check title").isEqualTo(expected.getTitle()))
            .satisfies(a -> assertThat(a.getContent()).as("check content").isEqualTo(expected.getContent()))
            .satisfies(a -> assertThat(a.getRecipientType()).as("check recipientType").isEqualTo(expected.getRecipientType()))
            .satisfies(a -> assertThat(a.getRecipientIds()).as("check recipientIds").isEqualTo(expected.getRecipientIds()))
            .satisfies(a -> assertThat(a.getChannels()).as("check channels").isEqualTo(expected.getChannels()))
            .satisfies(a -> assertThat(a.getPriority()).as("check priority").isEqualTo(expected.getPriority()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getScheduledTime()).as("check scheduledTime").isEqualTo(expected.getScheduledTime()))
            .satisfies(a -> assertThat(a.getSentTime()).as("check sentTime").isEqualTo(expected.getSentTime()))
            .satisfies(a -> assertThat(a.getBusinessId()).as("check businessId").isEqualTo(expected.getBusinessId()))
            .satisfies(a -> assertThat(a.getBusinessType()).as("check businessType").isEqualTo(expected.getBusinessType()))
            .satisfies(a -> assertThat(a.getTemplateParams()).as("check templateParams").isEqualTo(expected.getTemplateParams()))
            .satisfies(a -> assertThat(a.getRetryCount()).as("check retryCount").isEqualTo(expected.getRetryCount()))
            .satisfies(a -> assertThat(a.getErrorMessage()).as("check errorMessage").isEqualTo(expected.getErrorMessage()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNotificationRecordUpdatableRelationshipsEquals(NotificationRecord expected, NotificationRecord actual) {
        assertThat(actual)
            .as("Verify NotificationRecord relationships")
            .satisfies(a -> assertThat(a.getTemplate()).as("check template").isEqualTo(expected.getTemplate()));
    }
}
