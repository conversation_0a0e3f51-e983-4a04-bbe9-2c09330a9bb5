package com.whiskerguard.general.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class UserWechatBindingTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static UserWechatBinding getUserWechatBindingSample1() {
        return new UserWechatBinding()
            .id(1L)
            .tenantId(1L)
            .employeeId(1L)
            .openId("openId1")
            .unionId("unionId1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static UserWechatBinding getUserWechatBindingSample2() {
        return new UserWechatBinding()
            .id(2L)
            .tenantId(2L)
            .employeeId(2L)
            .openId("openId2")
            .unionId("unionId2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static UserWechatBinding getUserWechatBindingRandomSampleGenerator() {
        return new UserWechatBinding()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .employeeId(longCount.incrementAndGet())
            .openId(UUID.randomUUID().toString())
            .unionId(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
