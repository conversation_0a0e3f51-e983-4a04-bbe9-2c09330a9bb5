package com.whiskerguard.general.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class SignatureDocumentAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSignatureDocumentAllPropertiesEquals(SignatureDocument expected, SignatureDocument actual) {
        assertSignatureDocumentAutoGeneratedPropertiesEquals(expected, actual);
        assertSignatureDocumentAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSignatureDocumentAllUpdatablePropertiesEquals(SignatureDocument expected, SignatureDocument actual) {
        assertSignatureDocumentUpdatableFieldsEquals(expected, actual);
        assertSignatureDocumentUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSignatureDocumentAutoGeneratedPropertiesEquals(SignatureDocument expected, SignatureDocument actual) {
        assertThat(actual)
            .as("Verify SignatureDocument auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSignatureDocumentUpdatableFieldsEquals(SignatureDocument expected, SignatureDocument actual) {
        assertThat(actual)
            .as("Verify SignatureDocument relevant properties")
            .satisfies(a -> assertThat(a.getTitle()).as("check title").isEqualTo(expected.getTitle()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getDocumentUrl()).as("check documentUrl").isEqualTo(expected.getDocumentUrl()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getProvider()).as("check provider").isEqualTo(expected.getProvider()))
            .satisfies(a -> assertThat(a.getExternalId()).as("check externalId").isEqualTo(expected.getExternalId()))
            .satisfies(a -> assertThat(a.getTransactionId()).as("check transactionId").isEqualTo(expected.getTransactionId()))
            .satisfies(a -> assertThat(a.getUserId()).as("check userId").isEqualTo(expected.getUserId()))
            .satisfies(a -> assertThat(a.getExpireTime()).as("check expireTime").isEqualTo(expected.getExpireTime()))
            .satisfies(a -> assertThat(a.getSignedTime()).as("check signedTime").isEqualTo(expected.getSignedTime()))
            .satisfies(a -> assertThat(a.getSignedDocumentUrl()).as("check signedDocumentUrl").isEqualTo(expected.getSignedDocumentUrl()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertSignatureDocumentUpdatableRelationshipsEquals(SignatureDocument expected, SignatureDocument actual) {
        // empty method
    }
}
