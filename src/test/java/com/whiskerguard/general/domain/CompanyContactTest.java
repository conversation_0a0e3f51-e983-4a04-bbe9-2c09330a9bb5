package com.whiskerguard.general.domain;

import static com.whiskerguard.general.domain.CompanyContactTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.general.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

public class CompanyContactTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(CompanyContact.class);
        CompanyContact companyContact1 = getCompanyContactSample1();
        CompanyContact companyContact2 = new CompanyContact();
        assertThat(companyContact1).isNotEqualTo(companyContact2);

        companyContact2.setId(companyContact1.getId());
        assertThat(companyContact1).isEqualTo(companyContact2);

        companyContact2 = getCompanyContactSample2();
        assertThat(companyContact1).isNotEqualTo(companyContact2);
    }

    @Test
    void hashCodeVerifier() throws Exception {
        CompanyContact companyContact = new CompanyContact();
        assertThat(companyContact.hashCode()).isZero();

        CompanyContact companyContact1 = getCompanyContactSample1();
        companyContact.setId(companyContact1.getId());
        assertThat(companyContact).hasSameHashCodeAs(companyContact1);
    }

    @Test
    void companyTest() throws Exception {
        CompanyContact companyContact = getCompanyContactRandomSampleGenerator();
        Company companyBack = CompanyTestSamples.getCompanyRandomSampleGenerator();

        companyContact.setCompany(companyBack);
        assertThat(companyContact.getCompany()).isEqualTo(companyBack);

        companyContact.company(null);
        assertThat(companyContact.getCompany()).isNull();
    }
}
