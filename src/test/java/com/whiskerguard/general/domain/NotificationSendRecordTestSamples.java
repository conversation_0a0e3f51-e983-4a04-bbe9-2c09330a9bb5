package com.whiskerguard.general.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NotificationSendRecordTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NotificationSendRecord getNotificationSendRecordSample1() {
        return new NotificationSendRecord()
            .id(1L)
            .tenantId(1L)
            .recipientId(1L)
            .errorMessage("errorMessage1")
            .externalId("externalId1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static NotificationSendRecord getNotificationSendRecordSample2() {
        return new NotificationSendRecord()
            .id(2L)
            .tenantId(2L)
            .recipientId(2L)
            .errorMessage("errorMessage2")
            .externalId("externalId2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static NotificationSendRecord getNotificationSendRecordRandomSampleGenerator() {
        return new NotificationSendRecord()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .recipientId(longCount.incrementAndGet())
            .errorMessage(UUID.randomUUID().toString())
            .externalId(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}
