package com.whiskerguard.general.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

/**
 * Test configuration for Tianyancha module.
 * Provides test-specific beans and configurations.
 */
@TestConfiguration
public class TianyanchaTestConfiguration {

    /**
     * Provides a test RestTemplate bean.
     * This can be used to mock HTTP calls in tests.
     */
    @Bean
    @Primary
    public RestTemplate testRestTemplate() {
        return new RestTemplate();
    }

    /**
     * Provides a test ObjectMapper bean.
     * Configured for testing JSON serialization/deserialization.
     */
    @Bean
    @Primary
    public ObjectMapper testObjectMapper() {
        return new ObjectMapper();
    }

    /**
     * Provides test-specific Tianyancha properties.
     * Uses safe default values for testing.
     */
    @Bean
    @Primary
    public TianyanchaProperties testTianyanchaProperties() {
        TianyanchaProperties properties = new TianyanchaProperties();
        properties.setApiToken("test-token");
        properties.setBaseUrl("http://test.api.tianyancha.com/");
        properties.setCacheExpirationHours(24);
        properties.setConnectionTimeoutMs(1000);
        properties.setReadTimeoutMs(2000);
        properties.setMaxRetryAttempts(2);
        properties.setRetryDelayMs(100L);
        return properties;
    }
}
