package com.whiskerguard.general.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.client.TianyanchaApiClient;
import com.whiskerguard.general.config.TianyanchaProperties;
import com.whiskerguard.general.service.dto.TianyanchaCompanyBasicInfoResponseDTO;
import java.time.Duration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.web.client.RestTemplate;

/**
 * Manual test utility for Tianyancha API.
 * 手动测试天眼查API的工具类。
 */
public class TianyanchaApiTestUtil {

    public static void main(String[] args) {
        // if (args.length < 1) {
        //     System.out.println("使用方法: java TianyanchaApiTestUtil <API_TOKEN> [公司名称]");
        //     System.out.println("例如: java TianyanchaApiTestUtil your-api-token 中合数联（苏州）科技有限公司");
        //     return;
        // }
        if (args.length < 1) {
            args = new String[] { "b8fd5355-a31d-4bb7-9721-ee9530f213f7", "中合数联（苏州）科技有限公司" };
        }
        String apiToken = args[0];
        String companyName = args.length > 1 ? args[1] : "中合数联（苏州）科技有限公司";

        System.out.println("=== 天眼查API测试工具 ===");
        System.out.println("API Token: " + apiToken.substring(0, Math.min(8, apiToken.length())) + "...");
        System.out.println("测试企业: " + companyName);
        System.out.println("API URL: https://open.api.tianyancha.com/services/open/ic/baseinfoV2/2.0");
        System.out.println();

        try {
            // 配置TianyanchaProperties
            TianyanchaProperties properties = new TianyanchaProperties();
            properties.setApiToken(apiToken);
            properties.setBaseUrl("https://open.api.tianyancha.com/services/open/ic/");
            properties.setConnectionTimeoutMs(10000);
            properties.setReadTimeoutMs(30000);
            properties.setMaxRetryAttempts(3);
            properties.setRetryDelayMs(1000);

            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplateBuilder()
                .setConnectTimeout(Duration.ofMillis(properties.getConnectionTimeoutMs()))
                .setReadTimeout(Duration.ofMillis(properties.getReadTimeoutMs()))
                .build();

            // 创建API客户端
            ObjectMapper objectMapper = new ObjectMapper();
            TianyanchaApiClient apiClient = new TianyanchaApiClient(properties, restTemplate, objectMapper);

            System.out.println("正在调用API...");
            long startTime = System.currentTimeMillis();

            // 调用API
            TianyanchaCompanyBasicInfoResponseDTO response = apiClient.getCompanyBasicInfo(companyName);

            long endTime = System.currentTimeMillis();
            System.out.println("API调用完成，耗时: " + (endTime - startTime) + "ms");
            System.out.println();

            // 输出结果
            if (response.isSuccess()) {
                System.out.println("✅ API调用成功!");
                System.out.println("错误代码: " + response.getErrorCode());
                System.out.println("响应消息: " + response.getReason());
                System.out.println();

                if (response.getResult() != null) {
                    var result = response.getResult();
                    System.out.println("=== 企业信息 ===");
                    System.out.println("天眼查ID: " + result.getId());
                    System.out.println("企业名称: " + result.getName());
                    System.out.println("统一社会信用代码: " + result.getCreditCode());
                    System.out.println("法定代表人: " + result.getLegalPersonName());
                    System.out.println("登记状态: " + result.getRegStatus());
                    System.out.println("注册资本: " + result.getRegCapital());
                    System.out.println(
                        "成立时间: " +
                        (result.getEstiblishTime() != null ? new java.util.Date(result.getEstiblishTime()).toString() : "未知")
                    );
                    System.out.println("企业类型: " + result.getCompanyOrgType());
                    System.out.println("行业: " + result.getIndustry());
                    System.out.println("注册地址: " + result.getRegLocation());
                    if (result.getAlias() != null && !result.getAlias().isEmpty()) {
                        System.out.println("企业别名: " + result.getAlias());
                    }
                    if (result.getTags() != null && !result.getTags().isEmpty()) {
                        System.out.println("企业标签: " + result.getTags());
                    }
                    System.out.println("百分位评分: " + result.getPercentileScore());
                } else {
                    System.out.println("⚠️ 警告: 响应中没有企业信息");
                }
            } else {
                System.out.println("❌ API调用失败!");
                System.out.println("错误代码: " + response.getErrorCode());
                System.out.println("错误信息: " + response.getReason());
            }
        } catch (Exception e) {
            System.out.println("❌ 发生异常: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println();
        System.out.println("=== 测试完成 ===");
    }
}
